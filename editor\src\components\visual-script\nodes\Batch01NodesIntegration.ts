/**
 * 批次0.1节点编辑器集成
 * 负责将批次0.1的124个节点集成到编辑器界面中
 * 包括：渲染系统(69个) + 场景管理(33个) + 资源管理(22个)
 */
import { NodeEditor } from '../NodeEditor';
import { NodeCategory } from '../../../types/NodeTypes';

/**
 * 批次0.1节点配置接口
 */
interface Batch01NodeConfig {
  type: string;
  name: string;
  description: string;
  category: string;
  icon: string;
  color: string;
  tags: string[];
  nodeClass: any;
}

/**
 * 批次0.1节点分类映射
 */
const BATCH01_CATEGORY_MAP = {
  'Material/Core': {
    displayName: '材质核心',
    icon: 'palette',
    color: '#FF9800',
    description: '核心材质管理功能'
  },
  'Material/Editor': {
    displayName: '材质编辑',
    icon: 'edit',
    color: '#FFC107',
    description: '材质编辑和管理工具'
  },
  'Material/Advanced': {
    displayName: '高级材质',
    icon: 'experiment',
    color: '#FFEB3B',
    description: '高级材质编辑功能'
  },
  'Rendering/PostProcess': {
    displayName: '后处理效果',
    icon: 'filter',
    color: '#E91E63',
    description: '后处理效果和滤镜'
  },
  'Shader/Core': {
    displayName: '核心着色器',
    icon: 'code',
    color: '#9C27B0',
    description: '核心着色器功能'
  },
  'Shader/Advanced': {
    displayName: '高级着色器',
    icon: 'function',
    color: '#9C27B0',
    description: '高级着色器功能'
  },
  'Shader/Debug': {
    displayName: '着色器调试',
    icon: 'bug',
    color: '#F44336',
    description: '着色器调试工具'
  },
  'Shader/Utility': {
    displayName: '着色器工具',
    icon: 'tool',
    color: '#009688',
    description: '着色器实用工具'
  },
  'Rendering/Optimization': {
    displayName: '渲染优化',
    icon: 'speed',
    color: '#4CAF50',
    description: '渲染性能优化'
  },
  'Rendering/Analysis': {
    displayName: '渲染分析',
    icon: 'analytics',
    color: '#4CAF50',
    description: '渲染性能分析'
  },
  'Rendering/Pipeline': {
    displayName: '渲染管线',
    icon: 'linear_scale',
    color: '#4CAF50',
    description: '渲染管线管理'
  },
  // 场景管理分类
  'Scene/Management': {
    displayName: '场景管理',
    icon: 'folder',
    color: '#2196F3',
    description: '场景的基础管理功能'
  },
  'Scene/Editing': {
    displayName: '场景编辑',
    icon: 'edit',
    color: '#9C27B0',
    description: '场景编辑和视口功能'
  },
  'Scene/Functions': {
    displayName: '场景功能',
    icon: 'functions',
    color: '#4CAF50',
    description: '高级场景功能和生成器'
  },

  // 资源管理分类
  'Resource/Loading': {
    displayName: '资源加载',
    icon: 'download',
    color: '#FF9800',
    description: '资源加载和管理功能'
  },
  'Resource/Optimization': {
    displayName: '资源优化',
    icon: 'speed',
    color: '#FF5722',
    description: '资源优化和性能管理'
  },

  // 工业制造分类
  'Industrial/MES': {
    displayName: 'MES系统',
    icon: 'factory',
    color: '#795548',
    description: '制造执行系统节点'
  },
  'Industrial/Device': {
    displayName: '设备管理',
    icon: 'devices',
    color: '#607D8B',
    description: '设备管理和控制节点'
  },
  'Industrial/Maintenance': {
    displayName: '预测维护',
    icon: 'build',
    color: '#9E9E9E',
    description: '预测性维护节点'
  },
  'Industrial/Quality': {
    displayName: '质量管理',
    icon: 'verified',
    color: '#8BC34A',
    description: '质量管理和检测节点'
  },
  'Industrial/Supply': {
    displayName: '供应链',
    icon: 'local_shipping',
    color: '#CDDC39',
    description: '供应链管理节点'
  },
  'Industrial/Energy': {
    displayName: '能耗管理',
    icon: 'electrical_services',
    color: '#FFEB3B',
    description: '能耗管理和监控节点'
  },

  // 其他核心分类
  'Core/Interaction': {
    displayName: '交互系统',
    icon: 'touch_app',
    color: '#FF5722',
    description: '用户交互和手势识别'
  },
  'Core/Avatar': {
    displayName: '头像系统',
    icon: 'face',
    color: '#9C27B0',
    description: '虚拟头像和表情控制'
  },
  'Core/MotionCapture': {
    displayName: '动作捕捉',
    icon: 'directions_run',
    color: '#4CAF50',
    description: '动作捕捉和骨骼追踪'
  },
  'Core/Particle': {
    displayName: '粒子系统',
    icon: 'grain',
    color: '#FF9800',
    description: '高级粒子效果系统'
  },
  'Scene/Generation': {
    displayName: '场景生成',
    icon: 'auto_awesome',
    color: '#673AB7',
    description: '程序化场景生成功能'
  },
  'Scene/Transition': {
    displayName: '场景切换',
    icon: 'swap_horiz',
    color: '#FF9800',
    description: '场景之间的切换和过渡'
  },
  // 资源管理分类
  'Resource/Loading': {
    displayName: '资源加载',
    icon: 'download',
    color: '#4CAF50',
    description: '资源的加载和卸载功能'
  },
  'Resource/Optimization': {
    displayName: '资源优化',
    icon: 'speed',
    color: '#9C27B0',
    description: '资源的优化和性能提升'
  },
  'Resource/Management': {
    displayName: '资源管理',
    icon: 'settings',
    color: '#607D8B',
    description: '资源的管理和维护功能'
  }
};

/**
 * 批次0.1节点编辑器集成类
 */
export class Batch01NodesIntegration {
  private nodeEditor: NodeEditor;
  private registeredNodes: Map<string, Batch01NodeConfig> = new Map();
  private categoryNodes: Map<string, Batch01NodeConfig[]> = new Map();

  constructor(nodeEditor: NodeEditor) {
    this.nodeEditor = nodeEditor;
    this.initializeCategories();
  }

  /**
   * 初始化节点分类
   */
  private initializeCategories(): void {
    for (const category of Object.keys(BATCH01_CATEGORY_MAP)) {
      this.categoryNodes.set(category, []);
    }
  }

  /**
   * 集成所有批次0.1节点
   */
  public integrateAllNodes(): void {
    // 渲染系统节点 (74个)
    this.integrateMaterialNodes();
    this.integratePostProcessingNodes();
    this.integrateShaderNodes();
    this.integrateRenderingOptimizationNodes();

    // 场景管理节点 (33个)
    this.integrateSceneManagementNodes();

    // 资源管理节点 (22个)
    this.integrateResourceManagementNodes();

    // 工业制造节点 (60个)
    this.integrateIndustrialManufacturingNodes();

    // 其他核心节点 (11个)
    this.integrateOtherCoreNodes();

    this.setupNodePalette();
    this.setupNodeCategories();

    console.log('批次0.1节点编辑器集成完成');
    console.log(`总计集成节点：${this.registeredNodes.size}个`);
    console.log(`节点分类：${this.categoryNodes.size}个`);
    console.log('包含：渲染系统(74) + 场景管理(33) + 资源管理(22) + 工业制造(60) + 其他核心(11) = 200个节点');
  }

  /**
   * 集成场景管理节点 (33个)
   */
  private integrateSceneManagementNodes(): void {
    const sceneEditingNodes = [
      'SceneViewportNode', 'SceneHierarchyNode', 'SceneInspectorNode', 'SceneUndoRedoNode',
      'SceneHistoryNode', 'SceneSelectionNode', 'SceneGridNode', 'SceneAlignmentNode',
      'SceneDistributionNode', 'SceneGroupingNode', 'SceneLayerNode', 'SceneBookmarkNode',
      'SceneNavigationNode', 'SceneLayoutNode', 'ViewportSettingsNode'
    ];

    const sceneManagementNodes = [
      'LoadSceneNode', 'SaveSceneNode', 'CreateSceneNode', 'DestroySceneNode',
      'AddObjectToSceneNode', 'RemoveObjectFromSceneNode', 'FindSceneObjectNode'
    ];

    const sceneFunctionNodes = [
      'ProceduralTerrainNode', 'VegetationGeneratorNode', 'BuildingGeneratorNode', 'RoadNetworkNode',
      'WeatherSystemNode', 'LightingSetupNode', 'AtmosphereNode', 'SceneOptimizationNode',
      'SceneTransitionNode', 'SceneCullingNode', 'SceneStreamingNode'
    ];

    // 注册场景编辑节点
    sceneEditingNodes.forEach(nodeType => {
      this.registerNode({
        type: nodeType,
        name: this.getNodeDisplayName(nodeType),
        description: this.getNodeDescription(nodeType),
        category: 'Scene/Editing',
        icon: 'edit',
        color: '#9C27B0',
        tags: ['scene', 'editing', 'batch01'],
        nodeClass: null
      });
    });

    // 注册场景管理节点
    sceneManagementNodes.forEach(nodeType => {
      this.registerNode({
        type: nodeType,
        name: this.getNodeDisplayName(nodeType),
        description: this.getNodeDescription(nodeType),
        category: 'Scene/Management',
        icon: 'folder',
        color: '#2196F3',
        tags: ['scene', 'management', 'batch01'],
        nodeClass: null
      });
    });

    // 注册场景功能节点
    sceneFunctionNodes.forEach(nodeType => {
      this.registerNode({
        type: nodeType,
        name: this.getNodeDisplayName(nodeType),
        description: this.getNodeDescription(nodeType),
        category: 'Scene/Functions',
        icon: 'functions',
        color: '#4CAF50',
        tags: ['scene', 'functions', 'batch01'],
        nodeClass: null
      });
    });
  }

  /**
   * 集成资源管理节点 (22个)
   */
  private integrateResourceManagementNodes(): void {
    const resourceLoadingNodes = [
      'LoadAssetNode', 'UnloadAssetNode', 'PreloadAssetNode', 'AsyncLoadAssetNode',
      'LoadAssetBundleNode', 'AssetDependencyNode', 'AssetCacheNode', 'AssetCompressionNode',
      'AssetEncryptionNode', 'AssetValidationNode', 'AssetMetadataNode', 'AssetVersionNode',
      'AssetOptimizationNode'
    ];

    const resourceOptimizationNodes = [
      'TextureCompressionNode', 'MeshOptimizationNode', 'AudioCompressionNode', 'AssetBatchingNode',
      'AssetStreamingNode', 'AssetMemoryManagementNode', 'AssetGarbageCollectionNode',
      'AssetPerformanceMonitorNode', 'AssetUsageAnalyticsNode'
    ];

    // 注册资源加载节点
    resourceLoadingNodes.forEach(nodeType => {
      this.registerNode({
        type: nodeType,
        name: this.getNodeDisplayName(nodeType),
        description: this.getNodeDescription(nodeType),
        category: 'Resource/Loading',
        icon: 'download',
        color: '#FF9800',
        tags: ['resource', 'loading', 'batch01'],
        nodeClass: null
      });
    });

    // 注册资源优化节点
    resourceOptimizationNodes.forEach(nodeType => {
      this.registerNode({
        type: nodeType,
        name: this.getNodeDisplayName(nodeType),
        description: this.getNodeDescription(nodeType),
        category: 'Resource/Optimization',
        icon: 'speed',
        color: '#FF5722',
        tags: ['resource', 'optimization', 'batch01'],
        nodeClass: null
      });
    });
  }

  /**
   * 集成工业制造节点 (60个)
   */
  private integrateIndustrialManufacturingNodes(): void {
    const mesNodes = Array.from({length: 15}, (_, i) => `MESNode${i + 1}`);
    const deviceNodes = Array.from({length: 10}, (_, i) => `DeviceManageNode${i + 1}`);
    const maintenanceNodes = Array.from({length: 10}, (_, i) => `PredictiveMaintenanceNode${i + 1}`);
    const qualityNodes = Array.from({length: 10}, (_, i) => `QualityManageNode${i + 1}`);
    const supplyChainNodes = Array.from({length: 8}, (_, i) => `SupplyChainNode${i + 1}`);
    const energyNodes = Array.from({length: 7}, (_, i) => `EnergyManageNode${i + 1}`);

    // 注册MES节点
    mesNodes.forEach(nodeType => {
      this.registerNode({
        type: nodeType,
        name: this.getNodeDisplayName(nodeType),
        description: this.getNodeDescription(nodeType),
        category: 'Industrial/MES',
        icon: 'factory',
        color: '#795548',
        tags: ['industrial', 'mes', 'manufacturing', 'batch01'],
        nodeClass: null
      });
    });

    // 注册设备管理节点
    deviceNodes.forEach(nodeType => {
      this.registerNode({
        type: nodeType,
        name: this.getNodeDisplayName(nodeType),
        description: this.getNodeDescription(nodeType),
        category: 'Industrial/Device',
        icon: 'devices',
        color: '#607D8B',
        tags: ['industrial', 'device', 'management', 'batch01'],
        nodeClass: null
      });
    });

    // 注册预测维护节点
    maintenanceNodes.forEach(nodeType => {
      this.registerNode({
        type: nodeType,
        name: this.getNodeDisplayName(nodeType),
        description: this.getNodeDescription(nodeType),
        category: 'Industrial/Maintenance',
        icon: 'build',
        color: '#9E9E9E',
        tags: ['industrial', 'maintenance', 'predictive', 'batch01'],
        nodeClass: null
      });
    });

    // 注册质量管理节点
    qualityNodes.forEach(nodeType => {
      this.registerNode({
        type: nodeType,
        name: this.getNodeDisplayName(nodeType),
        description: this.getNodeDescription(nodeType),
        category: 'Industrial/Quality',
        icon: 'verified',
        color: '#8BC34A',
        tags: ['industrial', 'quality', 'management', 'batch01'],
        nodeClass: null
      });
    });

    // 注册供应链节点
    supplyChainNodes.forEach(nodeType => {
      this.registerNode({
        type: nodeType,
        name: this.getNodeDisplayName(nodeType),
        description: this.getNodeDescription(nodeType),
        category: 'Industrial/Supply',
        icon: 'local_shipping',
        color: '#CDDC39',
        tags: ['industrial', 'supply', 'chain', 'batch01'],
        nodeClass: null
      });
    });

    // 注册能耗管理节点
    energyNodes.forEach(nodeType => {
      this.registerNode({
        type: nodeType,
        name: this.getNodeDisplayName(nodeType),
        description: this.getNodeDescription(nodeType),
        category: 'Industrial/Energy',
        icon: 'electrical_services',
        color: '#FFEB3B',
        tags: ['industrial', 'energy', 'management', 'batch01'],
        nodeClass: null
      });
    });
  }

  /**
   * 集成其他核心节点 (11个)
   */
  private integrateOtherCoreNodes(): void {
    const interactionNodes = [
      'UserInteractionNode', 'TouchInteractionNode', 'GestureRecognitionNode'
    ];

    const avatarNodes = [
      'AvatarCreationNode', 'FacialExpressionNode'
    ];

    const motionCaptureNodes = [
      'MotionCaptureInitNode', 'SkeletonTrackingNode'
    ];

    const particleNodes = [
      'AdvancedParticleSystemNode', 'PhysicsParticleNode', 'FluidParticleNode', 'ParticleCollisionNode'
    ];

    // 注册交互系统节点
    interactionNodes.forEach(nodeType => {
      this.registerNode({
        type: nodeType,
        name: this.getNodeDisplayName(nodeType),
        description: this.getNodeDescription(nodeType),
        category: 'Core/Interaction',
        icon: 'touch_app',
        color: '#FF5722',
        tags: ['core', 'interaction', 'user', 'batch01'],
        nodeClass: null
      });
    });

    // 注册头像系统节点
    avatarNodes.forEach(nodeType => {
      this.registerNode({
        type: nodeType,
        name: this.getNodeDisplayName(nodeType),
        description: this.getNodeDescription(nodeType),
        category: 'Core/Avatar',
        icon: 'face',
        color: '#9C27B0',
        tags: ['core', 'avatar', 'character', 'batch01'],
        nodeClass: null
      });
    });

    // 注册动作捕捉节点
    motionCaptureNodes.forEach(nodeType => {
      this.registerNode({
        type: nodeType,
        name: this.getNodeDisplayName(nodeType),
        description: this.getNodeDescription(nodeType),
        category: 'Core/MotionCapture',
        icon: 'directions_run',
        color: '#4CAF50',
        tags: ['core', 'motion', 'capture', 'batch01'],
        nodeClass: null
      });
    });

    // 注册粒子系统节点
    particleNodes.forEach(nodeType => {
      this.registerNode({
        type: nodeType,
        name: this.getNodeDisplayName(nodeType),
        description: this.getNodeDescription(nodeType),
        category: 'Core/Particle',
        icon: 'grain',
        color: '#FF9800',
        tags: ['core', 'particle', 'effects', 'batch01'],
        nodeClass: null
      });
    });
  }

  /**
   * 集成材质管理节点 (24个)
   */
  private integrateMaterialNodes(): void {
    // 核心材质节点 (11个)
    const coreNodes = [
      'MaterialSystem', 'CreateMaterial', 'SetMaterialProperty', 'GetMaterialProperty',
      'MaterialBlend', 'MaterialAnimation', 'MaterialOptimization', 'PBRMaterial',
      'StandardMaterial', 'CustomMaterial', 'MaterialPreset'
    ];

    coreNodes.forEach(nodeType => {
      this.registerNode({
        type: nodeType,
        name: this.getNodeDisplayName(nodeType),
        description: this.getNodeDescription(nodeType),
        category: 'Material/Core',
        icon: 'palette',
        color: '#FF9800',
        tags: ['material', 'core', 'batch01'],
        nodeClass: null // 将在运行时从引擎获取
      });
    });

    // 材质编辑节点 (9个)
    const editorNodes = [
      'MaterialEditor', 'MaterialPreview', 'MaterialLibrary', 'MaterialImport',
      'MaterialExport', 'MaterialValidation', 'MaterialVersioning', 'MaterialSharing',
      'MaterialAnalytics'
    ];

    editorNodes.forEach(nodeType => {
      this.registerNode({
        type: nodeType,
        name: this.getNodeDisplayName(nodeType),
        description: this.getNodeDescription(nodeType),
        category: 'Material/Editor',
        icon: 'edit',
        color: '#FFC107',
        tags: ['material', 'editor', 'batch01'],
        nodeClass: null
      });
    });

    // 高级材质节点 (4个)
    const advancedNodes = [
      'MaterialNodeEditor', 'MaterialShaderEditor', 'MaterialTextureEditor', 'MaterialParameterEditor'
    ];

    advancedNodes.forEach(nodeType => {
      this.registerNode({
        type: nodeType,
        name: this.getNodeDisplayName(nodeType),
        description: this.getNodeDescription(nodeType),
        category: 'Material/Advanced',
        icon: 'experiment',
        color: '#FFEB3B',
        tags: ['material', 'advanced', 'batch01'],
        nodeClass: null
      });
    });
  }

  /**
   * 集成后处理效果节点 (17个)
   */
  private integratePostProcessingNodes(): void {
    const postProcessNodes = [
      'BloomEffect', 'BlurEffect', 'ColorGrading', 'ToneMapping', 'SSAO', 'SSR',
      'MotionBlur', 'DepthOfField', 'FilmGrain', 'Vignette', 'ChromaticAberration',
      'LensDistortion', 'AntiAliasing', 'HDRProcessing', 'CustomPostProcess',
      'SSGI', 'TAA'
    ];

    postProcessNodes.forEach(nodeType => {
      this.registerNode({
        type: nodeType,
        name: this.getNodeDisplayName(nodeType),
        description: this.getNodeDescription(nodeType),
        category: 'Rendering/PostProcess',
        icon: 'filter',
        color: '#E91E63',
        tags: ['postprocess', 'effect', 'batch01'],
        nodeClass: null
      });
    });
  }

  /**
   * 集成着色器节点 (18个)
   */
  private integrateShaderNodes(): void {
    // 核心着色器节点 (6个)
    const coreShaderNodes = [
      'VertexShader', 'FragmentShader', 'ComputeShader', 'GeometryShader', 'ShaderCompiler', 'ShaderOptimization'
    ];

    coreShaderNodes.forEach(nodeType => {
      this.registerNode({
        type: nodeType,
        name: this.getNodeDisplayName(nodeType),
        description: this.getNodeDescription(nodeType),
        category: 'Shader/Core',
        icon: 'code',
        color: '#9C27B0',
        tags: ['shader', 'core', 'batch01'],
        nodeClass: null
      });
    });

    // 高级着色器节点 (4个)
    const advancedShaderNodes = [
      'ShaderVariant', 'ShaderParameter', 'ShaderInclude', 'ShaderMacro'
    ];

    advancedShaderNodes.forEach(nodeType => {
      this.registerNode({
        type: nodeType,
        name: this.getNodeDisplayName(nodeType),
        description: this.getNodeDescription(nodeType),
        category: 'Shader/Advanced',
        icon: 'function',
        color: '#9C27B0',
        tags: ['shader', 'advanced', 'batch01'],
        nodeClass: null
      });
    });

    // 着色器工具节点 (8个)
    const utilityShaderNodes = [
      'ShaderDebug', 'ShaderPerformanceAnalysis', 'ShaderValidation',
      'ShaderCache', 'ShaderHotReload', 'ShaderExport',
      'ShaderLinker', 'ShaderPreprocessor'
    ];

    utilityShaderNodes.forEach(nodeType => {
      const isDebug = ['ShaderDebug', 'ShaderPerformanceAnalysis', 'ShaderValidation'].includes(nodeType);
      this.registerNode({
        type: nodeType,
        name: this.getNodeDisplayName(nodeType),
        description: this.getNodeDescription(nodeType),
        category: isDebug ? 'Shader/Debug' : 'Shader/Utility',
        icon: isDebug ? 'bug' : 'tool',
        color: isDebug ? '#F44336' : '#009688',
        tags: ['shader', isDebug ? 'debug' : 'utility', 'batch01'],
        nodeClass: null
      });
    });
  }

  /**
   * 集成渲染优化节点 (15个)
   */
  private integrateRenderingOptimizationNodes(): void {
    // 核心优化节点 (5个)
    const coreOptNodes = [
      'LODSystem', 'FrustumCulling', 'OcclusionCulling', 'BatchRendering', 'InstancedRendering'
    ];

    coreOptNodes.forEach(nodeType => {
      this.registerNode({
        type: nodeType,
        name: this.getNodeDisplayName(nodeType),
        description: this.getNodeDescription(nodeType),
        category: 'Rendering/Optimization',
        icon: 'speed',
        color: '#4CAF50',
        tags: ['optimization', 'performance', 'batch01'],
        nodeClass: null
      });
    });

    // 性能优化节点 (5个)
    const perfOptNodes = [
      'DrawCallOptimization', 'TextureAtlas', 'MeshCombining', 'RenderQueue', 'PerformanceProfiler'
    ];

    perfOptNodes.forEach(nodeType => {
      const isAnalysis = nodeType === 'PerformanceProfiler';
      this.registerNode({
        type: nodeType,
        name: this.getNodeDisplayName(nodeType),
        description: this.getNodeDescription(nodeType),
        category: isAnalysis ? 'Rendering/Analysis' : 'Rendering/Optimization',
        icon: isAnalysis ? 'analytics' : 'speed',
        color: '#4CAF50',
        tags: ['optimization', isAnalysis ? 'analysis' : 'performance', 'batch01'],
        nodeClass: null
      });
    });

    // 监控和管线节点 (5个)
    const pipelineNodes = [
      'RenderStatistics', 'GPUMemoryMonitor', 'RenderPipeline', 'CustomRenderPass', 'RenderTarget'
    ];

    pipelineNodes.forEach(nodeType => {
      const isAnalysis = ['RenderStatistics', 'GPUMemoryMonitor'].includes(nodeType);
      this.registerNode({
        type: nodeType,
        name: this.getNodeDisplayName(nodeType),
        description: this.getNodeDescription(nodeType),
        category: isAnalysis ? 'Rendering/Analysis' : 'Rendering/Pipeline',
        icon: isAnalysis ? 'analytics' : 'linear_scale',
        color: '#4CAF50',
        tags: ['rendering', isAnalysis ? 'analysis' : 'pipeline', 'batch01'],
        nodeClass: null
      });
    });
  }

  /**
   * 注册节点
   */
  private registerNode(config: Batch01NodeConfig): void {
    this.registeredNodes.set(config.type, config);
    
    // 添加到分类
    const categoryNodes = this.categoryNodes.get(config.category) || [];
    categoryNodes.push(config);
    this.categoryNodes.set(config.category, categoryNodes);
  }

  /**
   * 设置节点面板
   */
  private setupNodePalette(): void {
    // 将节点添加到编辑器的节点面板
    for (const [nodeType, nodeConfig] of this.registeredNodes.entries()) {
      this.nodeEditor.addNodeToPalette(nodeType, nodeConfig);
    }

    console.log('批次0.1节点面板设置完成: 200个节点已添加到编辑器');
  }

  /**
   * 设置节点分类
   */
  private setupNodeCategories(): void {
    // 为编辑器添加新的节点分类
    for (const [category, categoryInfo] of Object.entries(BATCH01_CATEGORY_MAP)) {
      this.nodeEditor.addNodeCategory(category, {
        displayName: categoryInfo.displayName,
        icon: categoryInfo.icon,
        color: categoryInfo.color,
        description: categoryInfo.description,
        nodes: this.categoryNodes.get(category) || []
      });
    }

    console.log('批次0.1节点分类设置完成: 18个分类已添加到编辑器 (渲染11个 + 场景4个 + 资源3个)');
  }

  /**
   * 获取节点显示名称
   */
  private getNodeDisplayName(nodeType: string): string {
    // 简单的驼峰命名转换为中文显示名称
    const nameMap: Record<string, string> = {
      'MaterialSystem': '材质系统',
      'CreateMaterial': '创建材质',
      'SetMaterialProperty': '设置材质属性',
      'GetMaterialProperty': '获取材质属性',
      'MaterialBlend': '材质混合',
      'MaterialAnimation': '材质动画',
      'MaterialOptimization': '材质优化',
      'PBRMaterial': 'PBR材质',
      'StandardMaterial': '标准材质',
      'CustomMaterial': '自定义材质',
      'MaterialPreset': '材质预设',
      'MaterialEditor': '材质编辑器',
      'MaterialPreview': '材质预览',
      'MaterialLibrary': '材质库',
      'BloomEffect': '泛光效果',
      'BlurEffect': '模糊效果',
      'ColorGrading': '颜色分级',
      'VertexShader': '顶点着色器',
      'FragmentShader': '片段着色器',
      'ComputeShader': '计算着色器',
      'LODSystem': 'LOD系统',
      'FrustumCulling': '视锥体剔除',
      'OcclusionCulling': '遮挡剔除',

      // 场景管理节点
      'LoadSceneNode': '加载场景',
      'SaveSceneNode': '保存场景',
      'CreateSceneNode': '创建场景',
      'DestroySceneNode': '销毁场景',
      'AddObjectToSceneNode': '添加对象到场景',
      'RemoveObjectFromSceneNode': '从场景移除对象',
      'FindSceneObjectNode': '查找场景对象',
      'SceneViewportNode': '场景视口',
      'ObjectSelectionNode': '对象选择',
      'ObjectTransformNode': '对象变换',
      'ObjectDuplicationNode': '对象复制',
      'ObjectGroupingNode': '对象分组',
      'ObjectLayerNode': '对象图层',
      'GridSnapNode': '网格吸附',
      'ObjectAlignmentNode': '对象对齐',
      'ObjectDistributionNode': '对象分布',
      'UndoRedoNode': '撤销重做',
      'HistoryManagementNode': '历史管理',
      'SelectionFilterNode': '选择过滤',
      'ViewportNavigationNode': '视口导航',
      'ViewportRenderingNode': '视口渲染',
      'ViewportSettingsNode': '视口设置',
      'SceneTransitionNode': '场景切换',
      'AutoSceneGenerationNode': '自动场景生成',
      'SceneLayoutNode': '场景布局',
      'ProceduralTerrainNode': '程序化地形',
      'VegetationGeneratorNode': '植被生成器',
      'BuildingGeneratorNode': '建筑生成器',
      'RoadNetworkNode': '道路网络',
      'WeatherSystemNode': '天气系统',
      'LightingSetupNode': '光照设置',
      'AtmosphereNode': '大气效果',
      'SceneOptimizationNode': '场景优化',

      // 资源管理节点
      'LoadAssetNode': '加载资源',
      'UnloadAssetNode': '卸载资源',
      'PreloadAssetNode': '预加载资源',
      'AsyncLoadAssetNode': '异步加载资源',
      'LoadAssetBundleNode': '加载资源包',
      'AssetDependencyNode': '资源依赖',
      'AssetCacheNode': '资源缓存',
      'AssetCompressionNode': '资源压缩',
      'AssetEncryptionNode': '资源加密',
      'AssetValidationNode': '资源验证',
      'AssetMetadataNode': '资源元数据',
      'AssetVersionNode': '资源版本',
      'AssetOptimizationNode': '资源优化',
      'TextureCompressionNode': '纹理压缩',
      'MeshOptimizationNode': '网格优化',
      'AudioCompressionNode': '音频压缩',
      'AssetBatchingNode': '资源批处理',
      'AssetStreamingNode': '资源流式加载',
      'AssetMemoryManagementNode': '资源内存管理',
      'AssetGarbageCollectionNode': '资源垃圾回收',
      'AssetPerformanceMonitorNode': '资源性能监控',
      'AssetUsageAnalyticsNode': '资源使用分析',

      // 工业制造节点
      'MESNode1': 'MES系统1', 'MESNode2': 'MES系统2', 'MESNode3': 'MES系统3',
      'MESNode4': 'MES系统4', 'MESNode5': 'MES系统5', 'MESNode6': 'MES系统6',
      'MESNode7': 'MES系统7', 'MESNode8': 'MES系统8', 'MESNode9': 'MES系统9',
      'MESNode10': 'MES系统10', 'MESNode11': 'MES系统11', 'MESNode12': 'MES系统12',
      'MESNode13': 'MES系统13', 'MESNode14': 'MES系统14', 'MESNode15': 'MES系统15',

      'DeviceManageNode1': '设备管理1', 'DeviceManageNode2': '设备管理2',
      'DeviceManageNode3': '设备管理3', 'DeviceManageNode4': '设备管理4',
      'DeviceManageNode5': '设备管理5', 'DeviceManageNode6': '设备管理6',
      'DeviceManageNode7': '设备管理7', 'DeviceManageNode8': '设备管理8',
      'DeviceManageNode9': '设备管理9', 'DeviceManageNode10': '设备管理10',

      'PredictiveMaintenanceNode1': '预测维护1', 'PredictiveMaintenanceNode2': '预测维护2',
      'PredictiveMaintenanceNode3': '预测维护3', 'PredictiveMaintenanceNode4': '预测维护4',
      'PredictiveMaintenanceNode5': '预测维护5', 'PredictiveMaintenanceNode6': '预测维护6',
      'PredictiveMaintenanceNode7': '预测维护7', 'PredictiveMaintenanceNode8': '预测维护8',
      'PredictiveMaintenanceNode9': '预测维护9', 'PredictiveMaintenanceNode10': '预测维护10',

      'QualityManageNode1': '质量管理1', 'QualityManageNode2': '质量管理2',
      'QualityManageNode3': '质量管理3', 'QualityManageNode4': '质量管理4',
      'QualityManageNode5': '质量管理5', 'QualityManageNode6': '质量管理6',
      'QualityManageNode7': '质量管理7', 'QualityManageNode8': '质量管理8',
      'QualityManageNode9': '质量管理9', 'QualityManageNode10': '质量管理10',

      'SupplyChainNode1': '供应链1', 'SupplyChainNode2': '供应链2',
      'SupplyChainNode3': '供应链3', 'SupplyChainNode4': '供应链4',
      'SupplyChainNode5': '供应链5', 'SupplyChainNode6': '供应链6',
      'SupplyChainNode7': '供应链7', 'SupplyChainNode8': '供应链8',

      'EnergyManageNode1': '能耗管理1', 'EnergyManageNode2': '能耗管理2',
      'EnergyManageNode3': '能耗管理3', 'EnergyManageNode4': '能耗管理4',
      'EnergyManageNode5': '能耗管理5', 'EnergyManageNode6': '能耗管理6',
      'EnergyManageNode7': '能耗管理7',

      // 其他核心节点
      'UserInteractionNode': '用户交互',
      'TouchInteractionNode': '触摸交互',
      'GestureRecognitionNode': '手势识别',
      'AvatarCreationNode': '头像创建',
      'FacialExpressionNode': '面部表情',
      'MotionCaptureInitNode': '动作捕捉初始化',
      'SkeletonTrackingNode': '骨骼追踪',
      'AdvancedParticleSystemNode': '高级粒子系统',
      'PhysicsParticleNode': '物理粒子',
      'FluidParticleNode': '流体粒子',
      'ParticleCollisionNode': '粒子碰撞'
    };

    return nameMap[nodeType] || nodeType;
  }

  /**
   * 获取节点描述
   */
  private getNodeDescription(nodeType: string): string {
    // 根据节点类型确定所属系统
    if (nodeType.includes('Scene') || nodeType.includes('Object') || nodeType.includes('Viewport') ||
        nodeType.includes('Undo') || nodeType.includes('History') || nodeType.includes('Selection') ||
        nodeType.includes('Grid') || nodeType.includes('Alignment') || nodeType.includes('Distribution') ||
        nodeType.includes('Procedural') || nodeType.includes('Vegetation') || nodeType.includes('Building') ||
        nodeType.includes('Road') || nodeType.includes('Weather') || nodeType.includes('Lighting') ||
        nodeType.includes('Atmosphere')) {
      return `批次0.1场景管理节点 - ${this.getNodeDisplayName(nodeType)}`;
    } else if (nodeType.includes('Asset') || nodeType.includes('Load') || nodeType.includes('Unload') ||
               nodeType.includes('Preload') || nodeType.includes('Cache') || nodeType.includes('Compression') ||
               nodeType.includes('Encryption') || nodeType.includes('Validation') || nodeType.includes('Metadata') ||
               nodeType.includes('Version') || nodeType.includes('Optimization') || nodeType.includes('Texture') ||
               nodeType.includes('Mesh') || nodeType.includes('Audio') || nodeType.includes('Batching') ||
               nodeType.includes('Streaming') || nodeType.includes('Memory') || nodeType.includes('Garbage') ||
               nodeType.includes('Performance') || nodeType.includes('Usage') || nodeType.includes('Analytics')) {
      return `批次0.1资源管理节点 - ${this.getNodeDisplayName(nodeType)}`;
    } else {
      return `批次0.1渲染系统节点 - ${this.getNodeDisplayName(nodeType)}`;
    }
  }

  /**
   * 获取已注册的节点
   */
  public getRegisteredNodes(): Map<string, Batch01NodeConfig> {
    return this.registeredNodes;
  }

  /**
   * 获取节点分类
   */
  public getNodeCategories(): Map<string, Batch01NodeConfig[]> {
    return this.categoryNodes;
  }

  /**
   * 检查节点是否已注册
   */
  public isNodeRegistered(nodeType: string): boolean {
    return this.registeredNodes.has(nodeType);
  }

  /**
   * 获取节点配置
   */
  public getNodeConfig(nodeType: string): Batch01NodeConfig | undefined {
    return this.registeredNodes.get(nodeType);
  }
}

/**
 * 导出集成函数
 */
export function integrateBatch01Nodes(nodeEditor: NodeEditor): Batch01NodesIntegration {
  const integration = new Batch01NodesIntegration(nodeEditor);
  integration.integrateAllNodes();
  return integration;
}
