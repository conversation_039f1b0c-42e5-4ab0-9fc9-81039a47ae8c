/**
 * 工业制造节点面板包装器
 * 用于集成到编辑器的面板系统中
 */

import React, { useState, useEffect, useRef } from 'react';
import { Card, message, Spin } from 'antd';
import { FactoryOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import IndustrialNodesPanel from './IndustrialNodesPanel';
import { 
  IndustrialNodesIntegration, 
  integrateIndustrialNodes 
} from '../nodes/IndustrialNodesIntegration';
import { NodeRegistry } from '../../../libs/dl-engine-types';

interface IndustrialNodesPanelWrapperProps {
  onNodeSelect?: (nodeType: string, nodeConfig: any) => void;
  onNodeAdd?: (nodeType: string, position?: { x: number; y: number }) => void;
  height?: number | string;
  width?: number | string;
}

/**
 * 工业制造节点面板包装器组件
 */
export const IndustrialNodesPanelWrapper: React.FC<IndustrialNodesPanelWrapperProps> = ({
  onNodeSelect,
  onNodeAdd,
  height = '100%',
  width = '100%'
}) => {
  const { t } = useTranslation();
  const [integration, setIntegration] = useState<IndustrialNodesIntegration | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const nodeEditorRef = useRef<any>(null);

  // 模拟节点编辑器接口
  const createMockNodeEditor = () => {
    return {
      addNodeToPalette: (nodeType: string, nodeConfig: any) => {
        console.log(`添加节点到面板: ${nodeType}`, nodeConfig);
      },
      addNodeCategory: (category: string, categoryInfo: any) => {
        console.log(`添加节点分类: ${category}`, categoryInfo);
      },
      getRegisteredNodes: () => {
        return new Map();
      }
    };
  };

  // 初始化工业制造节点集成
  useEffect(() => {
    const initializeIntegration = async () => {
      try {
        setLoading(true);
        setError(null);

        // 创建模拟节点编辑器
        const mockNodeEditor = createMockNodeEditor();
        nodeEditorRef.current = mockNodeEditor;

        // 确保NodeRegistry已初始化
        const nodeRegistry = NodeRegistry.getInstance();
        
        // 创建工业制造节点集成
        const industrialIntegration = new IndustrialNodesIntegration(mockNodeEditor);
        
        // 集成所有工业制造节点
        industrialIntegration.integrateAllNodes();
        
        // 验证集成结果
        const validation = industrialIntegration.validateIntegration();
        if (!validation.success) {
          console.warn('工业制造节点集成验证失败:', validation.errors);
          message.warning(`节点集成存在问题: ${validation.errors.join(', ')}`);
        } else {
          console.log('工业制造节点集成验证成功');
          message.success(`成功集成 ${industrialIntegration.getTotalNodeCount()} 个工业制造节点`);
        }

        setIntegration(industrialIntegration);
        
      } catch (err) {
        console.error('工业制造节点集成失败:', err);
        setError(err instanceof Error ? err.message : '未知错误');
        message.error('工业制造节点集成失败');
      } finally {
        setLoading(false);
      }
    };

    initializeIntegration();
  }, []);

  // 处理节点选择
  const handleNodeSelect = (nodeType: string, nodeConfig: any) => {
    console.log('选择节点:', nodeType, nodeConfig);
    
    if (onNodeSelect) {
      onNodeSelect(nodeType, nodeConfig);
    } else {
      // 默认行为：显示节点信息
      message.info(`选择了节点: ${nodeConfig?.name || nodeType}`);
    }
  };

  // 处理节点添加
  const handleNodeAdd = (nodeType: string, position?: { x: number; y: number }) => {
    console.log('添加节点:', nodeType, position);
    
    if (onNodeAdd) {
      onNodeAdd(nodeType, position);
    } else {
      // 默认行为：显示添加信息
      const nodeConfig = integration?.getNodeConfig(nodeType);
      message.success(`添加节点: ${nodeConfig?.name || nodeType}`);
    }
  };

  // 渲染加载状态
  if (loading) {
    return (
      <Card
        title={
          <span>
            <FactoryOutlined /> 工业制造节点
          </span>
        }
        style={{ height, width }}
        bodyStyle={{ 
          display: 'flex', 
          alignItems: 'center', 
          justifyContent: 'center',
          height: 'calc(100% - 57px)'
        }}
      >
        <Spin size="large" tip="正在加载工业制造节点..." />
      </Card>
    );
  }

  // 渲染错误状态
  if (error) {
    return (
      <Card
        title={
          <span>
            <FactoryOutlined /> 工业制造节点
          </span>
        }
        style={{ height, width }}
        bodyStyle={{ 
          display: 'flex', 
          alignItems: 'center', 
          justifyContent: 'center',
          height: 'calc(100% - 57px)'
        }}
      >
        <div style={{ textAlign: 'center', color: '#ff4d4f' }}>
          <p>加载失败: {error}</p>
          <p>请检查控制台获取详细信息</p>
        </div>
      </Card>
    );
  }

  // 渲染正常状态
  return (
    <div style={{ height, width }}>
      <IndustrialNodesPanel
        integration={integration}
        onNodeSelect={handleNodeSelect}
        onNodeAdd={handleNodeAdd}
        visible={true}
        loading={false}
      />
    </div>
  );
};

export default IndustrialNodesPanelWrapper;
