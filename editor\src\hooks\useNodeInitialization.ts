/**
 * 节点初始化钩子
 * 负责在编辑器启动时初始化和注册所有视觉脚本节点
 */
import { useEffect, useState } from 'react';
import { useEngineService } from './useEngineService';

/**
 * 节点初始化状态
 */
interface NodeInitializationState {
  isInitialized: boolean;
  isLoading: boolean;
  error: string | null;
  registeredNodes: number;
  nodeCategories: number;
}

/**
 * 节点初始化钩子
 */
export const useNodeInitialization = () => {
  const engineService = useEngineService();
  const [state, setState] = useState<NodeInitializationState>({
    isInitialized: false,
    isLoading: false,
    error: null,
    registeredNodes: 0,
    nodeCategories: 0
  });

  useEffect(() => {
    const initializeNodes = async () => {
      if (!engineService || state.isInitialized || state.isLoading) {
        return;
      }

      setState(prev => ({ ...prev, isLoading: true, error: null }));

      try {
        console.log('🚀 开始初始化视觉脚本节点系统...');

        // 等待引擎服务完全初始化
        await engineService.waitForInitialization();

        // 获取可视化脚本引擎
        const visualScriptEngine = engineService.getVisualScriptEngine();
        if (!visualScriptEngine) {
          throw new Error('可视化脚本引擎未初始化');
        }

        // 初始化批次0.1节点 (200个)
        await initializeBatch01Nodes(visualScriptEngine);

        // 初始化批次0.2节点 (50个)
        await initializeBatch02Nodes(visualScriptEngine);

        setState(prev => ({
          ...prev,
          isInitialized: true,
          isLoading: false,
          registeredNodes: 250, // 批次0.1(200个) + 批次0.2(50个)
          nodeCategories: 15   // 更新分类数量
        }));

        console.log('✅ 视觉脚本节点系统初始化完成');

      } catch (error) {
        console.error('❌ 节点系统初始化失败:', error);
        setState(prev => ({
          ...prev,
          isLoading: false,
          error: error instanceof Error ? error.message : '未知错误'
        }));
      }
    };

    initializeNodes();
  }, [engineService, state.isInitialized, state.isLoading]);

  /**
   * 初始化批次0.1渲染系统节点
   */
  const initializeBatch01Nodes = async (visualScriptEngine: any): Promise<void> => {
    try {
      console.log('📦 正在加载批次0.1渲染系统节点...');

      // 动态导入批次0.1节点集成模块
      const { integrateBatch01Nodes } = await import('../components/visual-script/nodes/Batch01NodesIntegration');

      // 获取节点编辑器
      const nodeEditor = visualScriptEngine.getNodeEditor?.();
      if (!nodeEditor) {
        throw new Error('无法获取节点编辑器实例');
      }

      // 集成批次0.1节点
      const integration = integrateBatch01Nodes(nodeEditor);

      console.log('✅ 批次0.1节点集成完成');
      console.log(`📊 已注册节点: ${integration.getRegisteredNodes().size}个`);
      console.log(`🏷️ 节点分类: ${integration.getNodeCategories().size}个`);

      // 验证节点注册
      const registeredNodeTypes = integration.getAllRegisteredNodeTypes?.();
      if (registeredNodeTypes && registeredNodeTypes.length > 0) {
        console.log('🔍 已注册的节点类型:', registeredNodeTypes.slice(0, 5).join(', '), '...');
      }

    } catch (error) {
      console.error('❌ 批次0.1节点初始化失败:', error);
      throw error;
    }
  };

  /**
   * 初始化批次0.2 AI系统扩展节点
   */
  const initializeBatch02Nodes = async (visualScriptEngine: any): Promise<void> => {
    try {
      console.log('📦 正在加载批次0.2 AI系统扩展节点...');

      // 动态导入批次0.2节点集成模块
      const { integrateBatch02Nodes } = await import('../components/visual-script/nodes/Batch02NodesIntegration');

      // 获取节点编辑器
      const nodeEditor = visualScriptEngine.getNodeEditor?.();
      if (!nodeEditor) {
        throw new Error('无法获取节点编辑器实例');
      }

      // 集成批次0.2节点
      const integration = integrateBatch02Nodes(nodeEditor);

      console.log('✅ 批次0.2节点集成完成');
      console.log(`📊 已注册AI节点: ${integration.getRegisteredNodes().size}个`);
      console.log(`🏷️ AI节点分类: ${integration.getNodeCategories().size}个`);

      // 验证节点注册
      const registeredNodeTypes = integration.getAllRegisteredNodeTypes?.();
      if (registeredNodeTypes && registeredNodeTypes.length > 0) {
        console.log('🔍 已注册的AI节点类型:', registeredNodeTypes.slice(0, 5).join(', '), '...');
      }

    } catch (error) {
      console.error('❌ 批次0.2节点初始化失败:', error);
      throw error;
    }
  };

  /**
   * 重新初始化节点系统
   */
  const reinitialize = () => {
    setState({
      isInitialized: false,
      isLoading: false,
      error: null,
      registeredNodes: 0,
      nodeCategories: 0
    });
  };

  /**
   * 获取节点统计信息
   */
  const getNodeStatistics = () => {
    return {
      totalNodes: state.registeredNodes,
      categories: state.nodeCategories,
      isReady: state.isInitialized && !state.isLoading && !state.error
    };
  };

  return {
    ...state,
    reinitialize,
    getNodeStatistics
  };
};

/**
 * 节点初始化状态提供者组件
 */
export const NodeInitializationProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const nodeInitialization = useNodeInitialization();

  // 在开发模式下显示初始化状态
  useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      console.log('🔧 节点初始化状态:', {
        isInitialized: nodeInitialization.isInitialized,
        isLoading: nodeInitialization.isLoading,
        error: nodeInitialization.error,
        registeredNodes: nodeInitialization.registeredNodes,
        nodeCategories: nodeInitialization.nodeCategories
      });
    }
  }, [nodeInitialization]);

  return <>{children}</>;
};

export default useNodeInitialization;
