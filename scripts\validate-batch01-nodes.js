#!/usr/bin/env node

/**
 * 批次0.1渲染系统节点功能验证脚本
 * 验证69个渲染系统节点的功能和集成状态
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

/**
 * 验证配置
 */
const VALIDATION_CONFIG = {
  // 预期的节点数量
  expectedNodes: {
    materialManagement: 24,
    postProcessing: 17,
    shader: 18,
    renderingOptimization: 15,
    total: 74
  },
  
  // 节点分类
  nodeCategories: [
    'Material/Core',
    'Material/Editor', 
    'Material/Advanced',
    'Rendering/PostProcess',
    'Shader/Core',
    'Shader/Advanced',
    'Shader/Debug',
    'Shader/Utility',
    'Rendering/Optimization',
    'Rendering/Analysis',
    'Rendering/Pipeline'
  ],
  
  // 关键节点列表
  keyNodes: [
    'MaterialSystem',
    'CreateMaterial',
    'BloomEffect',
    'VertexShader',
    'LODSystem',
    'RenderStatistics'
  ],
  
  // 验证超时时间
  timeout: 30000,
  
  // 输出目录
  outputDir: 'validation-results/batch01'
};

/**
 * 颜色输出工具
 */
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

/**
 * 日志工具
 */
const logger = {
  info: (msg) => console.log(`${colors.blue}ℹ${colors.reset} ${msg}`),
  success: (msg) => console.log(`${colors.green}✓${colors.reset} ${msg}`),
  warning: (msg) => console.log(`${colors.yellow}⚠${colors.reset} ${msg}`),
  error: (msg) => console.log(`${colors.red}✗${colors.reset} ${msg}`),
  header: (msg) => console.log(`\n${colors.bright}${colors.cyan}${msg}${colors.reset}\n`),
  separator: () => console.log(`${colors.cyan}${'='.repeat(60)}${colors.reset}`)
};

/**
 * 验证结果
 */
class ValidationResult {
  constructor() {
    this.timestamp = new Date().toISOString();
    this.success = true;
    this.errors = [];
    this.warnings = [];
    this.results = {
      nodeRegistration: null,
      editorIntegration: null,
      categorySystem: null,
      nodeExecution: null,
      performance: null
    };
    this.statistics = {
      totalNodes: 0,
      registeredNodes: 0,
      workingNodes: 0,
      failedNodes: 0,
      categories: 0,
      executionTime: 0
    };
  }

  addError(message) {
    this.errors.push(message);
    this.success = false;
    logger.error(message);
  }

  addWarning(message) {
    this.warnings.push(message);
    logger.warning(message);
  }

  addSuccess(message) {
    logger.success(message);
  }
}

/**
 * 创建输出目录
 */
function createOutputDirectory() {
  const outputPath = path.resolve(VALIDATION_CONFIG.outputDir);
  if (!fs.existsSync(outputPath)) {
    fs.mkdirSync(outputPath, { recursive: true });
    logger.success(`创建输出目录: ${outputPath}`);
  }
}

/**
 * 验证节点注册状态
 */
function validateNodeRegistration(result) {
  logger.info('验证节点注册状态...');
  
  try {
    // 检查节点注册表文件
    const registryPath = path.resolve('engine/src/visual-script/registry/Batch01NodesRegistry.ts');
    if (!fs.existsSync(registryPath)) {
      result.addError('批次0.1节点注册表文件不存在');
      return false;
    }
    
    const registryContent = fs.readFileSync(registryPath, 'utf8');
    
    // 检查关键方法
    const requiredMethods = [
      'registerAllNodes',
      'validateBatch01Nodes',
      'getBatch01Statistics'
    ];
    
    for (const method of requiredMethods) {
      if (!registryContent.includes(method)) {
        result.addError(`节点注册表缺少方法: ${method}`);
        return false;
      }
    }
    
    // 检查节点导入
    const keyNodeImports = [
      'MaterialSystemNode',
      'BloomEffectNode',
      'VertexShaderNode',
      'LODSystemNode'
    ];
    
    for (const nodeImport of keyNodeImports) {
      if (!registryContent.includes(nodeImport)) {
        result.addWarning(`节点注册表可能缺少导入: ${nodeImport}`);
      }
    }
    
    result.addSuccess('节点注册状态验证通过');
    result.results.nodeRegistration = {
      status: 'success',
      registryExists: true,
      methodsComplete: true,
      importsValid: true
    };
    
    return true;
    
  } catch (error) {
    result.addError(`节点注册验证失败: ${error.message}`);
    return false;
  }
}

/**
 * 验证编辑器集成
 */
function validateEditorIntegration(result) {
  logger.info('验证编辑器集成...');
  
  try {
    // 检查编辑器集成文件
    const integrationPath = path.resolve('editor/src/components/visual-script/nodes/Batch01NodesIntegration.ts');
    if (!fs.existsSync(integrationPath)) {
      result.addError('批次0.1节点编辑器集成文件不存在');
      return false;
    }
    
    const integrationContent = fs.readFileSync(integrationPath, 'utf8');
    
    // 检查集成方法
    const requiredMethods = [
      'integrateAllNodes',
      'setupNodePalette',
      'setupNodeCategories'
    ];
    
    for (const method of requiredMethods) {
      if (!integrationContent.includes(method)) {
        result.addError(`编辑器集成缺少方法: ${method}`);
        return false;
      }
    }
    
    // 检查分类映射
    if (!integrationContent.includes('BATCH01_CATEGORY_MAP')) {
      result.addError('编辑器集成缺少分类映射');
      return false;
    }
    
    // 检查国际化文件
    const i18nPath = path.resolve('editor/src/i18n/locales/zh-CN/batch01-nodes.json');
    if (!fs.existsSync(i18nPath)) {
      result.addWarning('批次0.1节点国际化文件不存在');
    }
    
    result.addSuccess('编辑器集成验证通过');
    result.results.editorIntegration = {
      status: 'success',
      integrationExists: true,
      methodsComplete: true,
      i18nExists: fs.existsSync(i18nPath)
    };
    
    return true;
    
  } catch (error) {
    result.addError(`编辑器集成验证失败: ${error.message}`);
    return false;
  }
}

/**
 * 验证分类系统
 */
function validateCategorySystem(result) {
  logger.info('验证分类系统...');
  
  try {
    // 检查分类管理器
    const categoryManagerPath = path.resolve('editor/src/components/visual-script/NodeCategoryManager.ts');
    if (!fs.existsSync(categoryManagerPath)) {
      result.addError('节点分类管理器文件不存在');
      return false;
    }
    
    const categoryContent = fs.readFileSync(categoryManagerPath, 'utf8');
    
    // 检查分类管理器方法
    const requiredMethods = [
      'getAllCategories',
      'getBatch01Categories',
      'searchCategories'
    ];
    
    for (const method of requiredMethods) {
      if (!categoryContent.includes(method)) {
        result.addError(`分类管理器缺少方法: ${method}`);
        return false;
      }
    }
    
    // 检查预期分类
    let foundCategories = 0;
    for (const category of VALIDATION_CONFIG.nodeCategories) {
      if (categoryContent.includes(category)) {
        foundCategories++;
      }
    }
    
    if (foundCategories < VALIDATION_CONFIG.nodeCategories.length) {
      result.addWarning(`分类系统可能缺少某些分类: ${foundCategories}/${VALIDATION_CONFIG.nodeCategories.length}`);
    }
    
    result.addSuccess('分类系统验证通过');
    result.results.categorySystem = {
      status: 'success',
      managerExists: true,
      methodsComplete: true,
      categoriesFound: foundCategories,
      categoriesExpected: VALIDATION_CONFIG.nodeCategories.length
    };
    
    result.statistics.categories = foundCategories;
    return true;
    
  } catch (error) {
    result.addError(`分类系统验证失败: ${error.message}`);
    return false;
  }
}

/**
 * 验证节点执行功能
 */
function validateNodeExecution(result) {
  logger.info('验证节点执行功能...');
  
  try {
    // 检查节点实现文件
    const nodeDirectories = [
      'engine/src/visual-script/nodes/rendering',
      'engine/src/visual-script/nodes/material'
    ];
    
    let totalNodeFiles = 0;
    let validNodeFiles = 0;
    
    for (const nodeDir of nodeDirectories) {
      const dirPath = path.resolve(nodeDir);
      if (fs.existsSync(dirPath)) {
        const files = fs.readdirSync(dirPath).filter(file => 
          file.endsWith('.ts') && !file.includes('.test.') && !file.includes('.spec.')
        );
        
        totalNodeFiles += files.length;
        
        for (const file of files) {
          const filePath = path.join(dirPath, file);
          const content = fs.readFileSync(filePath, 'utf8');
          
          // 检查是否包含execute方法
          if (content.includes('execute(') || content.includes('execute :')) {
            validNodeFiles++;
          }
        }
      }
    }
    
    if (totalNodeFiles === 0) {
      result.addError('未找到节点实现文件');
      return false;
    }
    
    const executionRate = (validNodeFiles / totalNodeFiles) * 100;
    if (executionRate < 80) {
      result.addWarning(`节点执行方法覆盖率较低: ${executionRate.toFixed(1)}%`);
    }
    
    result.addSuccess(`节点执行功能验证通过: ${validNodeFiles}/${totalNodeFiles} 个文件包含执行方法`);
    result.results.nodeExecution = {
      status: 'success',
      totalFiles: totalNodeFiles,
      validFiles: validNodeFiles,
      executionRate: executionRate
    };
    
    result.statistics.totalNodes = totalNodeFiles;
    result.statistics.workingNodes = validNodeFiles;
    result.statistics.failedNodes = totalNodeFiles - validNodeFiles;
    
    return true;
    
  } catch (error) {
    result.addError(`节点执行验证失败: ${error.message}`);
    return false;
  }
}

/**
 * 验证性能指标
 */
function validatePerformance(result) {
  logger.info('验证性能指标...');
  
  try {
    const startTime = Date.now();
    
    // 模拟节点加载性能测试
    const testFiles = [
      'engine/src/visual-script/registry/Batch01NodesRegistry.ts',
      'editor/src/components/visual-script/nodes/Batch01NodesIntegration.ts',
      'editor/src/components/visual-script/NodeCategoryManager.ts'
    ];
    
    let totalSize = 0;
    let loadTime = 0;
    
    for (const file of testFiles) {
      const filePath = path.resolve(file);
      if (fs.existsSync(filePath)) {
        const stats = fs.statSync(filePath);
        totalSize += stats.size;
        
        // 模拟文件加载时间
        const content = fs.readFileSync(filePath, 'utf8');
        loadTime += content.length / 10000; // 简单的加载时间估算
      }
    }
    
    const endTime = Date.now();
    const totalTime = endTime - startTime;
    
    // 性能阈值检查
    if (totalTime > 1000) {
      result.addWarning(`验证耗时较长: ${totalTime}ms`);
    }
    
    if (totalSize > 1024 * 1024) { // 1MB
      result.addWarning(`文件总大小较大: ${(totalSize / 1024 / 1024).toFixed(2)}MB`);
    }
    
    result.addSuccess(`性能验证通过: 耗时 ${totalTime}ms, 文件大小 ${(totalSize / 1024).toFixed(2)}KB`);
    result.results.performance = {
      status: 'success',
      validationTime: totalTime,
      totalFileSize: totalSize,
      estimatedLoadTime: loadTime
    };
    
    result.statistics.executionTime = totalTime;
    return true;
    
  } catch (error) {
    result.addError(`性能验证失败: ${error.message}`);
    return false;
  }
}

/**
 * 生成验证报告
 */
function generateValidationReport(result) {
  logger.info('生成验证报告...');
  
  const reportPath = path.resolve(VALIDATION_CONFIG.outputDir, 'validation-report.json');
  const htmlReportPath = path.resolve(VALIDATION_CONFIG.outputDir, 'validation-report.html');
  
  // 生成JSON报告
  fs.writeFileSync(reportPath, JSON.stringify(result, null, 2));
  logger.success(`JSON报告已生成: ${reportPath}`);
  
  // 生成HTML报告
  const htmlContent = generateHTMLValidationReport(result);
  fs.writeFileSync(htmlReportPath, htmlContent);
  logger.success(`HTML报告已生成: ${htmlReportPath}`);
  
  return result;
}

/**
 * 生成HTML验证报告
 */
function generateHTMLValidationReport(result) {
  const statusColor = result.success ? '#28a745' : '#dc3545';
  const statusText = result.success ? '✓ 验证通过' : '✗ 验证失败';
  
  return `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>批次0.1节点功能验证报告</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { text-align: center; margin-bottom: 30px; }
        .status { padding: 15px; border-radius: 4px; margin: 20px 0; color: white; background-color: ${statusColor}; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 4px; }
        .stats { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin: 20px 0; }
        .stat-card { background: #f8f9fa; padding: 15px; border-radius: 4px; text-align: center; }
        .stat-number { font-size: 2em; font-weight: bold; color: #007bff; }
        .error { color: #dc3545; }
        .warning { color: #ffc107; }
        .success { color: #28a745; }
        .timestamp { color: #6c757d; font-size: 0.9em; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>批次0.1渲染系统节点功能验证报告</h1>
            <div class="timestamp">生成时间: ${result.timestamp}</div>
        </div>
        
        <div class="status">
            <strong>${statusText}</strong>
        </div>
        
        <div class="stats">
            <div class="stat-card">
                <div class="stat-number">${result.statistics.totalNodes}</div>
                <div>总节点数</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">${result.statistics.workingNodes}</div>
                <div>正常工作节点</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">${result.statistics.categories}</div>
                <div>节点分类</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">${result.statistics.executionTime}ms</div>
                <div>验证耗时</div>
            </div>
        </div>
        
        <div class="section">
            <h2>验证结果详情</h2>
            <ul>
                <li>节点注册: ${result.results.nodeRegistration?.status || '未验证'}</li>
                <li>编辑器集成: ${result.results.editorIntegration?.status || '未验证'}</li>
                <li>分类系统: ${result.results.categorySystem?.status || '未验证'}</li>
                <li>节点执行: ${result.results.nodeExecution?.status || '未验证'}</li>
                <li>性能指标: ${result.results.performance?.status || '未验证'}</li>
            </ul>
        </div>
        
        ${result.errors.length > 0 ? `
        <div class="section">
            <h2 class="error">错误信息 (${result.errors.length})</h2>
            <ul>
                ${result.errors.map(error => `<li class="error">${error}</li>`).join('')}
            </ul>
        </div>
        ` : ''}
        
        ${result.warnings.length > 0 ? `
        <div class="section">
            <h2 class="warning">警告信息 (${result.warnings.length})</h2>
            <ul>
                ${result.warnings.map(warning => `<li class="warning">${warning}</li>`).join('')}
            </ul>
        </div>
        ` : ''}
        
        <div class="section">
            <h2>预期指标</h2>
            <ul>
                <li>材质管理节点: ${VALIDATION_CONFIG.expectedNodes.materialManagement}个</li>
                <li>后处理效果节点: ${VALIDATION_CONFIG.expectedNodes.postProcessing}个</li>
                <li>着色器节点: ${VALIDATION_CONFIG.expectedNodes.shader}个</li>
                <li>渲染优化节点: ${VALIDATION_CONFIG.expectedNodes.renderingOptimization}个</li>
                <li>总计: ${VALIDATION_CONFIG.expectedNodes.total}个</li>
            </ul>
        </div>
    </div>
</body>
</html>
`;
}

/**
 * 主验证函数
 */
function main() {
  logger.header('批次0.1渲染系统节点功能验证');
  logger.separator();
  
  const result = new ValidationResult();
  
  try {
    // 创建输出目录
    createOutputDirectory();
    
    // 执行验证步骤
    const validations = [
      () => validateNodeRegistration(result),
      () => validateEditorIntegration(result),
      () => validateCategorySystem(result),
      () => validateNodeExecution(result),
      () => validatePerformance(result)
    ];
    
    for (const validation of validations) {
      if (!validation()) {
        break; // 如果某个验证失败，停止后续验证
      }
    }
    
    // 生成报告
    generateValidationReport(result);
    
    // 输出总结
    logger.separator();
    logger.header('验证总结');
    
    if (result.success) {
      logger.success('所有验证项目通过');
      logger.success(`节点数量: ${result.statistics.workingNodes}/${result.statistics.totalNodes}`);
      logger.success(`分类数量: ${result.statistics.categories}`);
      logger.success(`验证耗时: ${result.statistics.executionTime}ms`);
      
      if (result.warnings.length > 0) {
        logger.warning(`发现 ${result.warnings.length} 个警告，请查看报告详情`);
      }
      
      process.exit(0);
    } else {
      logger.error(`验证失败: ${result.errors.length} 个错误`);
      logger.error('请查看验证报告了解详细信息');
      process.exit(1);
    }
    
  } catch (error) {
    logger.error('验证过程中发生未预期的错误:');
    logger.error(error.message);
    process.exit(1);
  }
}

// 运行主函数
if (require.main === module) {
  main();
}

module.exports = {
  VALIDATION_CONFIG,
  ValidationResult,
  main,
  validateNodeRegistration,
  validateEditorIntegration,
  validateCategorySystem,
  validateNodeExecution,
  validatePerformance
};
