/**
 * 批次3.4节点导出
 * VR/AR节点、游戏逻辑节点、社交功能节点、支付系统节点、第三方集成节点
 */

// VR/AR节点
export {
  VRControllerNode,
  ARTrackingNode,
  SpatialMappingNode,
  HandTrackingNode,
  EyeTrackingNode,
  VoiceCommandNode,
  HapticFeedbackNode,
  VRTeleportationNode,
  ARPlacementNode,
  ImmersiveUINode
} from './VRARNodes';

// 游戏逻辑节点
export {
  GameStateNode,
  PlayerControllerNode,
  InventorySystemNode,
  QuestSystemNode,
  DialogueSystemNode,
  SaveLoadSystemNode,
  AchievementSystemNode,
  LeaderboardNode
} from './GameLogicNodes';

// 社交功能节点
export {
  FriendSystemNode,
  ChatSystemNode,
  GroupSystemNode,
  SocialSharingNode,
  UserGeneratedContentNode,
  CommunityFeaturesNode
} from './SocialNodes';

// 支付系统节点
export {
  PaymentGatewayNode,
  SubscriptionNode,
  InAppPurchaseNode,
  WalletSystemNode,
  TransactionHistoryNode,
  PaymentAnalyticsNode
} from './PaymentNodes';

// 第三方集成节点
export {
  GoogleServicesNode,
  FacebookIntegrationNode,
  TwitterIntegrationNode,
  CloudStorageNode,
  AnalyticsIntegrationNode
} from './ThirdPartyNodes';

// 注册表
export {
  Batch34ExtensionNodesRegistry,
  batch34ExtensionNodesRegistry
} from './Batch34ExtensionNodesRegistry';
