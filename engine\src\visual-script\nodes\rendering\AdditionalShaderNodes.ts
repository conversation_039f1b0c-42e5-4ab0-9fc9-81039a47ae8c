/**
 * 批次0.1补充着色器节点
 * 补充缺失的6个着色器节点，使总数达到21个
 */
import { BaseNode } from '../BaseNode';
import { NodeInput, NodeOutput } from '../../types/NodeTypes';

/**
 * 几何着色器节点
 */
export class GeometryShaderNode extends BaseNode {
  public static readonly TYPE = 'GeometryShaderNode';
  public static readonly NAME = '几何着色器';
  public static readonly DESCRIPTION = '编译和管理几何着色器程序';

  constructor() {
    super(GeometryShaderNode.TYPE, GeometryShaderNode.NAME, GeometryShaderNode.DESCRIPTION);
    this.setupInputsAndOutputs();
  }

  private setupInputsAndOutputs(): void {
    // 输入
    this.addInput('compile', 'boolean', '编译着色器', false);
    this.addInput('source', 'string', '着色器源码', '');
    this.addInput('shaderId', 'string', '着色器ID', '');
    this.addInput('inputPrimitive', 'string', '输入图元类型', 'triangles');
    this.addInput('outputPrimitive', 'string', '输出图元类型', 'triangle_strip');
    this.addInput('maxVertices', 'number', '最大顶点数', 3);

    // 输出
    this.addOutput('shader', 'object', '着色器对象');
    this.addOutput('shaderId', 'string', '着色器ID');
    this.addOutput('compiled', 'boolean', '编译状态');
    this.addOutput('compilationTime', 'number', '编译时间');
    this.addOutput('onCompiled', 'boolean', '编译完成');
    this.addOutput('onError', 'boolean', '编译错误');
  }

  public execute(inputs: Record<string, any>): Record<string, any> {
    try {
      if (!inputs.compile || !inputs.source || !inputs.shaderId) {
        return {
          shader: null,
          shaderId: '',
          compiled: false,
          compilationTime: 0,
          onCompiled: false,
          onError: false
        };
      }

      const startTime = Date.now();
      
      // 模拟几何着色器编译
      const shader = {
        id: inputs.shaderId,
        type: 'geometry',
        source: inputs.source,
        inputPrimitive: inputs.inputPrimitive || 'triangles',
        outputPrimitive: inputs.outputPrimitive || 'triangle_strip',
        maxVertices: Math.max(1, Math.min(256, inputs.maxVertices || 3)),
        compiled: true,
        timestamp: Date.now()
      };

      const compilationTime = Date.now() - startTime;

      return {
        shader,
        shaderId: inputs.shaderId,
        compiled: true,
        compilationTime,
        onCompiled: true,
        onError: false
      };

    } catch (error) {
      console.error('几何着色器编译错误:', error);
      return {
        shader: null,
        shaderId: inputs.shaderId || '',
        compiled: false,
        compilationTime: 0,
        onCompiled: false,
        onError: true
      };
    }
  }
}

/**
 * 曲面细分控制着色器节点
 */
export class TessellationControlShaderNode extends BaseNode {
  public static readonly TYPE = 'TessellationControlShaderNode';
  public static readonly NAME = '曲面细分控制着色器';
  public static readonly DESCRIPTION = '编译和管理曲面细分控制着色器程序';

  constructor() {
    super(TessellationControlShaderNode.TYPE, TessellationControlShaderNode.NAME, TessellationControlShaderNode.DESCRIPTION);
    this.setupInputsAndOutputs();
  }

  private setupInputsAndOutputs(): void {
    // 输入
    this.addInput('compile', 'boolean', '编译着色器', false);
    this.addInput('source', 'string', '着色器源码', '');
    this.addInput('shaderId', 'string', '着色器ID', '');
    this.addInput('patchVertices', 'number', '补丁顶点数', 3);

    // 输出
    this.addOutput('shader', 'object', '着色器对象');
    this.addOutput('shaderId', 'string', '着色器ID');
    this.addOutput('compiled', 'boolean', '编译状态');
    this.addOutput('compilationTime', 'number', '编译时间');
    this.addOutput('onCompiled', 'boolean', '编译完成');
    this.addOutput('onError', 'boolean', '编译错误');
  }

  public execute(inputs: Record<string, any>): Record<string, any> {
    try {
      if (!inputs.compile || !inputs.source || !inputs.shaderId) {
        return {
          shader: null,
          shaderId: '',
          compiled: false,
          compilationTime: 0,
          onCompiled: false,
          onError: false
        };
      }

      const startTime = Date.now();
      
      const shader = {
        id: inputs.shaderId,
        type: 'tessellation_control',
        source: inputs.source,
        patchVertices: Math.max(1, Math.min(32, inputs.patchVertices || 3)),
        compiled: true,
        timestamp: Date.now()
      };

      const compilationTime = Date.now() - startTime;

      return {
        shader,
        shaderId: inputs.shaderId,
        compiled: true,
        compilationTime,
        onCompiled: true,
        onError: false
      };

    } catch (error) {
      console.error('曲面细分控制着色器编译错误:', error);
      return {
        shader: null,
        shaderId: inputs.shaderId || '',
        compiled: false,
        compilationTime: 0,
        onCompiled: false,
        onError: true
      };
    }
  }
}

/**
 * 曲面细分评估着色器节点
 */
export class TessellationEvaluationShaderNode extends BaseNode {
  public static readonly TYPE = 'TessellationEvaluationShaderNode';
  public static readonly NAME = '曲面细分评估着色器';
  public static readonly DESCRIPTION = '编译和管理曲面细分评估着色器程序';

  constructor() {
    super(TessellationEvaluationShaderNode.TYPE, TessellationEvaluationShaderNode.NAME, TessellationEvaluationShaderNode.DESCRIPTION);
    this.setupInputsAndOutputs();
  }

  private setupInputsAndOutputs(): void {
    // 输入
    this.addInput('compile', 'boolean', '编译着色器', false);
    this.addInput('source', 'string', '着色器源码', '');
    this.addInput('shaderId', 'string', '着色器ID', '');
    this.addInput('primitiveMode', 'string', '图元模式', 'triangles');

    // 输出
    this.addOutput('shader', 'object', '着色器对象');
    this.addOutput('shaderId', 'string', '着色器ID');
    this.addOutput('compiled', 'boolean', '编译状态');
    this.addOutput('compilationTime', 'number', '编译时间');
    this.addOutput('onCompiled', 'boolean', '编译完成');
    this.addOutput('onError', 'boolean', '编译错误');
  }

  public execute(inputs: Record<string, any>): Record<string, any> {
    try {
      if (!inputs.compile || !inputs.source || !inputs.shaderId) {
        return {
          shader: null,
          shaderId: '',
          compiled: false,
          compilationTime: 0,
          onCompiled: false,
          onError: false
        };
      }

      const startTime = Date.now();
      
      const shader = {
        id: inputs.shaderId,
        type: 'tessellation_evaluation',
        source: inputs.source,
        primitiveMode: inputs.primitiveMode || 'triangles',
        compiled: true,
        timestamp: Date.now()
      };

      const compilationTime = Date.now() - startTime;

      return {
        shader,
        shaderId: inputs.shaderId,
        compiled: true,
        compilationTime,
        onCompiled: true,
        onError: false
      };

    } catch (error) {
      console.error('曲面细分评估着色器编译错误:', error);
      return {
        shader: null,
        shaderId: inputs.shaderId || '',
        compiled: false,
        compilationTime: 0,
        onCompiled: false,
        onError: true
      };
    }
  }
}

/**
 * 着色器链接器节点
 */
export class ShaderLinkerNode extends BaseNode {
  public static readonly TYPE = 'ShaderLinkerNode';
  public static readonly NAME = '着色器链接器';
  public static readonly DESCRIPTION = '链接多个着色器创建完整的着色器程序';

  constructor() {
    super(ShaderLinkerNode.TYPE, ShaderLinkerNode.NAME, ShaderLinkerNode.DESCRIPTION);
    this.setupInputsAndOutputs();
  }

  private setupInputsAndOutputs(): void {
    // 输入
    this.addInput('link', 'boolean', '链接程序', false);
    this.addInput('vertexShader', 'object', '顶点着色器', null);
    this.addInput('fragmentShader', 'object', '片段着色器', null);
    this.addInput('geometryShader', 'object', '几何着色器', null);
    this.addInput('tessControlShader', 'object', '曲面细分控制着色器', null);
    this.addInput('tessEvalShader', 'object', '曲面细分评估着色器', null);
    this.addInput('programId', 'string', '程序ID', '');

    // 输出
    this.addOutput('program', 'object', '着色器程序');
    this.addOutput('programId', 'string', '程序ID');
    this.addOutput('linked', 'boolean', '链接状态');
    this.addOutput('linkTime', 'number', '链接时间');
    this.addOutput('onLinked', 'boolean', '链接完成');
    this.addOutput('onError', 'boolean', '链接错误');
  }

  public execute(inputs: Record<string, any>): Record<string, any> {
    try {
      if (!inputs.link || !inputs.vertexShader || !inputs.fragmentShader || !inputs.programId) {
        return {
          program: null,
          programId: '',
          linked: false,
          linkTime: 0,
          onLinked: false,
          onError: false
        };
      }

      const startTime = Date.now();
      
      const program = {
        id: inputs.programId,
        vertexShader: inputs.vertexShader,
        fragmentShader: inputs.fragmentShader,
        geometryShader: inputs.geometryShader || null,
        tessControlShader: inputs.tessControlShader || null,
        tessEvalShader: inputs.tessEvalShader || null,
        linked: true,
        timestamp: Date.now()
      };

      const linkTime = Date.now() - startTime;

      return {
        program,
        programId: inputs.programId,
        linked: true,
        linkTime,
        onLinked: true,
        onError: false
      };

    } catch (error) {
      console.error('着色器链接错误:', error);
      return {
        program: null,
        programId: inputs.programId || '',
        linked: false,
        linkTime: 0,
        onLinked: false,
        onError: true
      };
    }
  }
}

/**
 * 着色器预处理器节点
 */
export class ShaderPreprocessorNode extends BaseNode {
  public static readonly TYPE = 'ShaderPreprocessorNode';
  public static readonly NAME = '着色器预处理器';
  public static readonly DESCRIPTION = '处理着色器源码的预处理指令和宏定义';

  constructor() {
    super(ShaderPreprocessorNode.TYPE, ShaderPreprocessorNode.NAME, ShaderPreprocessorNode.DESCRIPTION);
    this.setupInputsAndOutputs();
  }

  private setupInputsAndOutputs(): void {
    // 输入
    this.addInput('process', 'boolean', '处理源码', false);
    this.addInput('source', 'string', '原始源码', '');
    this.addInput('defines', 'object', '宏定义', {});
    this.addInput('includes', 'array', '包含文件', []);

    // 输出
    this.addOutput('processedSource', 'string', '处理后源码');
    this.addOutput('defines', 'object', '实际宏定义');
    this.addOutput('includes', 'array', '实际包含文件');
    this.addOutput('onProcessed', 'boolean', '处理完成');
    this.addOutput('onError', 'boolean', '处理错误');
  }

  public execute(inputs: Record<string, any>): Record<string, any> {
    try {
      if (!inputs.process || !inputs.source) {
        return {
          processedSource: '',
          defines: {},
          includes: [],
          onProcessed: false,
          onError: false
        };
      }

      let processedSource = inputs.source;
      const defines = inputs.defines || {};
      const includes = inputs.includes || [];

      // 处理宏定义
      for (const [key, value] of Object.entries(defines)) {
        const regex = new RegExp(`#define\\s+${key}\\s+.*`, 'g');
        processedSource = processedSource.replace(regex, `#define ${key} ${value}`);
      }

      // 处理包含文件
      includes.forEach((include: string) => {
        const regex = new RegExp(`#include\\s+"${include}"`, 'g');
        processedSource = processedSource.replace(regex, `// Included: ${include}`);
      });

      return {
        processedSource,
        defines,
        includes,
        onProcessed: true,
        onError: false
      };

    } catch (error) {
      console.error('着色器预处理错误:', error);
      return {
        processedSource: '',
        defines: {},
        includes: [],
        onProcessed: false,
        onError: true
      };
    }
  }
}

/**
 * 着色器反射节点
 */
export class ShaderReflectionNode extends BaseNode {
  public static readonly TYPE = 'ShaderReflectionNode';
  public static readonly NAME = '着色器反射';
  public static readonly DESCRIPTION = '分析着色器程序的输入输出和资源信息';

  constructor() {
    super(ShaderReflectionNode.TYPE, ShaderReflectionNode.NAME, ShaderReflectionNode.DESCRIPTION);
    this.setupInputsAndOutputs();
  }

  private setupInputsAndOutputs(): void {
    // 输入
    this.addInput('analyze', 'boolean', '分析程序', false);
    this.addInput('program', 'object', '着色器程序', null);

    // 输出
    this.addOutput('uniforms', 'array', '统一变量列表');
    this.addOutput('attributes', 'array', '属性列表');
    this.addOutput('varyings', 'array', '变量列表');
    this.addOutput('textures', 'array', '纹理列表');
    this.addOutput('buffers', 'array', '缓冲区列表');
    this.addOutput('onAnalyzed', 'boolean', '分析完成');
    this.addOutput('onError', 'boolean', '分析错误');
  }

  public execute(inputs: Record<string, any>): Record<string, any> {
    try {
      if (!inputs.analyze || !inputs.program) {
        return {
          uniforms: [],
          attributes: [],
          varyings: [],
          textures: [],
          buffers: [],
          onAnalyzed: false,
          onError: false
        };
      }

      // 模拟着色器反射分析
      const uniforms = [
        { name: 'u_modelMatrix', type: 'mat4', location: 0 },
        { name: 'u_viewMatrix', type: 'mat4', location: 1 },
        { name: 'u_projectionMatrix', type: 'mat4', location: 2 }
      ];

      const attributes = [
        { name: 'a_position', type: 'vec3', location: 0 },
        { name: 'a_normal', type: 'vec3', location: 1 },
        { name: 'a_texCoord', type: 'vec2', location: 2 }
      ];

      const varyings = [
        { name: 'v_worldPos', type: 'vec3' },
        { name: 'v_normal', type: 'vec3' },
        { name: 'v_texCoord', type: 'vec2' }
      ];

      const textures = [
        { name: 'u_diffuseTexture', type: 'sampler2D', unit: 0 },
        { name: 'u_normalTexture', type: 'sampler2D', unit: 1 }
      ];

      const buffers = [
        { name: 'VertexBuffer', type: 'vertex', binding: 0 },
        { name: 'IndexBuffer', type: 'index', binding: 1 }
      ];

      return {
        uniforms,
        attributes,
        varyings,
        textures,
        buffers,
        onAnalyzed: true,
        onError: false
      };

    } catch (error) {
      console.error('着色器反射分析错误:', error);
      return {
        uniforms: [],
        attributes: [],
        varyings: [],
        textures: [],
        buffers: [],
        onAnalyzed: false,
        onError: true
      };
    }
  }
}
