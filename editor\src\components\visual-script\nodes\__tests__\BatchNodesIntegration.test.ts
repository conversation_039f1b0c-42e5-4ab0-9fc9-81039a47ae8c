/**
 * 编辑器批次节点集成测试
 * 测试批次0.1和批次0.2节点在编辑器中的集成功能
 */

import { NodeEditor } from '../NodeEditor';
import { Batch01NodesIntegration, integrateBatch01Nodes } from '../Batch01NodesIntegration';
import { Batch02NodesIntegration, integrateBatch02Nodes } from '../Batch02NodesIntegration';

// Mock NodeEditor
jest.mock('../NodeEditor');

describe('编辑器批次节点集成测试', () => {
  let mockNodeEditor: jest.Mocked<NodeEditor>;

  beforeEach(() => {
    mockNodeEditor = {
      addNodeToPalette: jest.fn(),
      addNodeCategory: jest.fn(),
      registerNodeType: jest.fn(),
      getRegisteredNodes: jest.fn().mockReturnValue(new Map()),
      getNodeCategories: jest.fn().mockReturnValue(new Map())
    } as any;
  });

  describe('批次0.1节点编辑器集成测试', () => {
    test('应该成功集成批次0.1的200个节点到编辑器', () => {
      const integration = integrateBatch01Nodes(mockNodeEditor);

      expect(integration).toBeInstanceOf(Batch01NodesIntegration);
      expect(integration.getRegisteredNodes().size).toBeGreaterThan(0);
      expect(integration.getNodeCategories().size).toBeGreaterThan(0);
    });

    test('应该正确设置批次0.1节点面板', () => {
      const integration = new Batch01NodesIntegration(mockNodeEditor);

      // 验证节点被添加到面板
      expect(mockNodeEditor.addNodeToPalette).toHaveBeenCalled();
      
      // 验证调用次数应该等于注册的节点数量
      const registeredNodes = integration.getRegisteredNodes();
      expect(mockNodeEditor.addNodeToPalette).toHaveBeenCalledTimes(registeredNodes.size);
    });

    test('应该正确设置批次0.1节点分类', () => {
      const integration = new Batch01NodesIntegration(mockNodeEditor);

      // 验证分类被添加到编辑器
      expect(mockNodeEditor.addNodeCategory).toHaveBeenCalled();

      // 验证特定分类的添加
      expect(mockNodeEditor.addNodeCategory).toHaveBeenCalledWith(
        expect.stringContaining('Rendering'),
        expect.objectContaining({
          displayName: expect.any(String),
          icon: expect.any(String),
          color: expect.any(String),
          description: expect.any(String)
        })
      );
    });

    test('应该返回正确的批次0.1节点类型列表', () => {
      const integration = new Batch01NodesIntegration(mockNodeEditor);
      const nodeTypes = integration.getAllRegisteredNodeTypes();

      expect(Array.isArray(nodeTypes)).toBe(true);
      expect(nodeTypes.length).toBeGreaterThan(0);

      // 验证包含渲染系统节点
      expect(nodeTypes.some(type => type.includes('Material'))).toBe(true);
      expect(nodeTypes.some(type => type.includes('Shader'))).toBe(true);
      expect(nodeTypes.some(type => type.includes('PostProcess'))).toBe(true);
    });
  });

  describe('批次0.2节点编辑器集成测试', () => {
    test('应该成功集成批次0.2的50个AI节点到编辑器', () => {
      const integration = integrateBatch02Nodes(mockNodeEditor);

      expect(integration).toBeInstanceOf(Batch02NodesIntegration);
      expect(integration.getRegisteredNodes().size).toBe(50);
      expect(integration.getNodeCategories().size).toBe(5); // 5个AI分类
    });

    test('应该正确设置批次0.2 AI节点面板', () => {
      const integration = new Batch02NodesIntegration(mockNodeEditor);

      // 验证50个AI节点被添加到面板
      expect(mockNodeEditor.addNodeToPalette).toHaveBeenCalledTimes(50);
    });

    test('应该正确设置批次0.2 AI节点分类', () => {
      const integration = new Batch02NodesIntegration(mockNodeEditor);

      // 验证5个AI分类被添加到编辑器
      expect(mockNodeEditor.addNodeCategory).toHaveBeenCalledTimes(5);

      // 验证深度学习分类
      expect(mockNodeEditor.addNodeCategory).toHaveBeenCalledWith(
        'AI系统/深度学习',
        expect.objectContaining({
          displayName: '深度学习',
          icon: 'psychology',
          color: '#E91E63',
          description: '深度学习模型和神经网络节点'
        })
      );

      // 验证机器学习分类
      expect(mockNodeEditor.addNodeCategory).toHaveBeenCalledWith(
        'AI系统/机器学习',
        expect.objectContaining({
          displayName: '机器学习',
          icon: 'smart_toy',
          color: '#9C27B0'
        })
      );

      // 验证计算机视觉分类
      expect(mockNodeEditor.addNodeCategory).toHaveBeenCalledWith(
        'AI系统/计算机视觉',
        expect.objectContaining({
          displayName: '计算机视觉',
          icon: 'visibility',
          color: '#2196F3'
        })
      );

      // 验证自然语言处理分类
      expect(mockNodeEditor.addNodeCategory).toHaveBeenCalledWith(
        'AI系统/自然语言处理',
        expect.objectContaining({
          displayName: '自然语言处理',
          icon: 'translate',
          color: '#4CAF50'
        })
      );

      // 验证AI工具分类
      expect(mockNodeEditor.addNodeCategory).toHaveBeenCalledWith(
        'AI系统/AI工具',
        expect.objectContaining({
          displayName: 'AI工具',
          icon: 'build',
          color: '#FF5722'
        })
      );
    });

    test('应该返回正确的批次0.2 AI节点类型列表', () => {
      const integration = new Batch02NodesIntegration(mockNodeEditor);
      const nodeTypes = integration.getAllRegisteredNodeTypes();

      expect(nodeTypes).toHaveLength(50);

      // 验证深度学习节点
      expect(nodeTypes).toContain('DeepLearningModelNode');
      expect(nodeTypes).toContain('NeuralNetworkNode');
      expect(nodeTypes).toContain('CNNNode');
      expect(nodeTypes).toContain('TransformerNode');

      // 验证机器学习节点
      expect(nodeTypes).toContain('ReinforcementLearningNode');
      expect(nodeTypes).toContain('DecisionTreeNode');

      // 验证计算机视觉节点
      expect(nodeTypes).toContain('ImageSegmentationNode');
      expect(nodeTypes).toContain('ObjectDetectionNode');

      // 验证NLP节点
      expect(nodeTypes).toContain('TextClassificationNode');
      expect(nodeTypes).toContain('SentimentAnalysisNode');

      // 验证AI工具节点
      expect(nodeTypes).toContain('ModelDeploymentNode');
      expect(nodeTypes).toContain('ModelTrainingNode');
    });

    test('应该正确生成AI节点的显示名称', () => {
      const integration = new Batch02NodesIntegration(mockNodeEditor);
      const registeredNodes = integration.getRegisteredNodes();

      // 验证节点配置包含正确的中文显示名称
      const deepLearningNode = registeredNodes.get('DeepLearningModelNode');
      expect(deepLearningNode?.name).toBe('深度学习模型');

      const cnnNode = registeredNodes.get('CNNNode');
      expect(cnnNode?.name).toBe('卷积神经网络');

      const transformerNode = registeredNodes.get('TransformerNode');
      expect(transformerNode?.name).toBe('Transformer');

      const faceRecognitionNode = registeredNodes.get('FaceRecognitionNode');
      expect(faceRecognitionNode?.name).toBe('人脸识别');

      const sentimentAnalysisNode = registeredNodes.get('SentimentAnalysisNode');
      expect(sentimentAnalysisNode?.name).toBe('情感分析');
    });
  });

  describe('批次节点统一集成测试', () => {
    test('应该能够同时集成批次0.1和批次0.2节点', () => {
      const batch01Integration = integrateBatch01Nodes(mockNodeEditor);
      const batch02Integration = integrateBatch02Nodes(mockNodeEditor);

      expect(batch01Integration.getRegisteredNodes().size).toBeGreaterThan(0);
      expect(batch02Integration.getRegisteredNodes().size).toBe(50);

      // 验证总的节点面板调用次数
      const totalCalls = batch01Integration.getRegisteredNodes().size + batch02Integration.getRegisteredNodes().size;
      expect(mockNodeEditor.addNodeToPalette).toHaveBeenCalledTimes(totalCalls);
    });

    test('应该正确处理节点标签和分类', () => {
      const batch01Integration = new Batch01NodesIntegration(mockNodeEditor);
      const batch02Integration = new Batch02NodesIntegration(mockNodeEditor);

      // 验证批次0.1节点包含正确的标签
      const batch01Nodes = batch01Integration.getRegisteredNodes();
      for (const [nodeType, nodeConfig] of batch01Nodes.entries()) {
        expect(nodeConfig.tags).toContain('batch01');
        expect(nodeConfig.tags.length).toBeGreaterThan(1);
      }

      // 验证批次0.2节点包含正确的标签
      const batch02Nodes = batch02Integration.getRegisteredNodes();
      for (const [nodeType, nodeConfig] of batch02Nodes.entries()) {
        expect(nodeConfig.tags).toContain('batch02');
        expect(nodeConfig.tags).toContain('ai');
        expect(nodeConfig.tags.length).toBeGreaterThan(2);
      }
    });

    test('应该支持节点搜索和过滤', () => {
      const batch02Integration = new Batch02NodesIntegration(mockNodeEditor);
      const nodeTypes = batch02Integration.getAllRegisteredNodeTypes();

      // 模拟搜索深度学习相关节点
      const deepLearningNodes = nodeTypes.filter(type => 
        type.toLowerCase().includes('neural') || 
        type.toLowerCase().includes('cnn') || 
        type.toLowerCase().includes('rnn')
      );
      expect(deepLearningNodes.length).toBeGreaterThan(0);

      // 模拟搜索计算机视觉相关节点
      const visionNodes = nodeTypes.filter(type => 
        type.toLowerCase().includes('image') || 
        type.toLowerCase().includes('face') || 
        type.toLowerCase().includes('detection')
      );
      expect(visionNodes.length).toBeGreaterThan(0);

      // 模拟搜索NLP相关节点
      const nlpNodes = nodeTypes.filter(type => 
        type.toLowerCase().includes('text') || 
        type.toLowerCase().includes('sentiment') || 
        type.toLowerCase().includes('translation')
      );
      expect(nlpNodes.length).toBeGreaterThan(0);
    });
  });

  describe('错误处理和边界情况测试', () => {
    test('应该正确处理空的NodeEditor', () => {
      const nullNodeEditor = null as any;
      
      expect(() => {
        new Batch01NodesIntegration(nullNodeEditor);
      }).toThrow();
    });

    test('应该正确处理NodeEditor方法调用失败', () => {
      const faultyNodeEditor = {
        addNodeToPalette: jest.fn().mockImplementation(() => {
          throw new Error('Mock error');
        }),
        addNodeCategory: jest.fn(),
        registerNodeType: jest.fn()
      } as any;

      expect(() => {
        new Batch02NodesIntegration(faultyNodeEditor);
      }).toThrow();
    });
  });
});
