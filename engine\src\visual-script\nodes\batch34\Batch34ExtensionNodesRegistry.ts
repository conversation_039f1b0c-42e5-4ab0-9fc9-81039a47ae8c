/**
 * 批次3.4扩展节点注册表
 * 注册支付系统节点和第三方集成节点到编辑器
 */

import { NodeRegistry } from '../../registry/NodeRegistry';

// 导入支付系统节点
import {
  PaymentGatewayNode,
  SubscriptionNode,
  InAppPurchaseNode,
  WalletSystemNode,
  TransactionHistoryNode,
  PaymentAnalyticsNode
} from './PaymentNodes';

// 导入第三方集成节点
import {
  GoogleServicesNode,
  FacebookIntegrationNode,
  TwitterIntegrationNode,
  CloudStorageNode,
  AnalyticsIntegrationNode
} from './ThirdPartyNodes';

/**
 * 批次3.4扩展节点注册表类
 */
export class Batch34ExtensionNodesRegistry {
  private static instance: Batch34ExtensionNodesRegistry;
  private registered = false;

  private constructor() {}

  public static getInstance(): Batch34ExtensionNodesRegistry {
    if (!Batch34ExtensionNodesRegistry.instance) {
      Batch34ExtensionNodesRegistry.instance = new Batch34ExtensionNodesRegistry();
    }
    return Batch34ExtensionNodesRegistry.instance;
  }

  /**
   * 注册所有批次3.4扩展节点
   */
  public registerAllNodes(): void {
    if (this.registered) {
      console.log('批次3.4扩展节点已经注册过了');
      return;
    }

    console.log('开始注册批次3.4扩展节点...');

    // 注册支付系统节点
    this.registerPaymentNodes();

    // 注册第三方集成节点
    this.registerThirdPartyNodes();

    this.registered = true;
    console.log('批次3.4扩展节点注册完成！总计11个节点');
  }

  /**
   * 注册支付系统节点（6个）
   */
  private registerPaymentNodes(): void {
    console.log('注册支付系统节点...');

    // 支付网关节点
    NodeRegistry.registerNode('PaymentGateway', {
      name: '支付网关',
      description: '处理各种支付方式的统一接口',
      category: '支付系统',
      createNode: () => new PaymentGatewayNode(),
      inputs: [
        { name: 'amount', type: 'number', label: '支付金额' },
        { name: 'currency', type: 'string', label: '货币类型', defaultValue: 'USD' },
        { name: 'paymentMethod', type: 'string', label: '支付方式', defaultValue: 'credit_card' },
        { name: 'merchantId', type: 'string', label: '商户ID' },
        { name: 'orderId', type: 'string', label: '订单ID' },
        { name: 'customerInfo', type: 'object', label: '客户信息' },
        { name: 'metadata', type: 'object', label: '元数据', defaultValue: {} }
      ],
      outputs: [
        { name: 'transactionId', type: 'string', label: '交易ID' },
        { name: 'status', type: 'string', label: '支付状态' },
        { name: 'paymentUrl', type: 'string', label: '支付链接' },
        { name: 'success', type: 'boolean', label: '支付成功' },
        { name: 'errorMessage', type: 'string', label: '错误信息' },
        { name: 'fees', type: 'number', label: '手续费' },
        { name: 'netAmount', type: 'number', label: '净收入' }
      ],
      color: '#4CAF50'
    });

    // 订阅系统节点
    NodeRegistry.registerNode('Subscription', {
      name: '订阅系统',
      description: '处理订阅服务的创建、管理和计费',
      category: '支付系统',
      createNode: () => new SubscriptionNode(),
      inputs: [
        { name: 'action', type: 'string', label: '操作类型', defaultValue: 'create' },
        { name: 'customerId', type: 'string', label: '客户ID' },
        { name: 'planId', type: 'string', label: '订阅计划ID' },
        { name: 'subscriptionId', type: 'string', label: '订阅ID' },
        { name: 'billingCycle', type: 'string', label: '计费周期', defaultValue: 'monthly' },
        { name: 'trialDays', type: 'number', label: '试用天数', defaultValue: 0 },
        { name: 'couponCode', type: 'string', label: '优惠券代码' }
      ],
      outputs: [
        { name: 'subscriptionId', type: 'string', label: '订阅ID' },
        { name: 'status', type: 'string', label: '订阅状态' },
        { name: 'currentPeriodStart', type: 'string', label: '当前周期开始' },
        { name: 'currentPeriodEnd', type: 'string', label: '当前周期结束' },
        { name: 'nextBillingDate', type: 'string', label: '下次计费日期' },
        { name: 'amount', type: 'number', label: '订阅金额' },
        { name: 'success', type: 'boolean', label: '操作成功' },
        { name: 'errorMessage', type: 'string', label: '错误信息' }
      ],
      color: '#2196F3'
    });

    // 应用内购买节点
    NodeRegistry.registerNode('InAppPurchase', {
      name: '应用内购买',
      description: '处理移动应用内购买和虚拟商品交易',
      category: '支付系统',
      createNode: () => new InAppPurchaseNode(),
      inputs: [
        { name: 'productId', type: 'string', label: '商品ID' },
        { name: 'userId', type: 'string', label: '用户ID' },
        { name: 'platform', type: 'string', label: '平台', defaultValue: 'ios' },
        { name: 'receipt', type: 'string', label: '购买凭证' },
        { name: 'action', type: 'string', label: '操作类型', defaultValue: 'purchase' },
        { name: 'quantity', type: 'number', label: '购买数量', defaultValue: 1 }
      ],
      outputs: [
        { name: 'transactionId', type: 'string', label: '交易ID' },
        { name: 'productInfo', type: 'object', label: '商品信息' },
        { name: 'purchaseDate', type: 'string', label: '购买日期' },
        { name: 'success', type: 'boolean', label: '购买成功' },
        { name: 'errorMessage', type: 'string', label: '错误信息' },
        { name: 'virtualCurrency', type: 'number', label: '虚拟货币' },
        { name: 'items', type: 'array', label: '获得物品' }
      ],
      color: '#FF9800'
    });

    // 钱包系统节点
    NodeRegistry.registerNode('WalletSystem', {
      name: '钱包系统',
      description: '处理用户钱包余额、充值、提现等操作',
      category: '支付系统',
      createNode: () => new WalletSystemNode(),
      inputs: [
        { name: 'action', type: 'string', label: '操作类型', defaultValue: 'getBalance' },
        { name: 'userId', type: 'string', label: '用户ID' },
        { name: 'amount', type: 'number', label: '金额', defaultValue: 0 },
        { name: 'currency', type: 'string', label: '货币类型', defaultValue: 'USD' },
        { name: 'description', type: 'string', label: '交易描述' },
        { name: 'metadata', type: 'object', label: '元数据', defaultValue: {} }
      ],
      outputs: [
        { name: 'balance', type: 'number', label: '余额' },
        { name: 'transactionId', type: 'string', label: '交易ID' },
        { name: 'success', type: 'boolean', label: '操作成功' },
        { name: 'errorMessage', type: 'string', label: '错误信息' },
        { name: 'walletInfo', type: 'object', label: '钱包信息' },
        { name: 'transactionHistory', type: 'array', label: '交易历史' }
      ],
      color: '#9C27B0'
    });

    // 交易历史节点
    NodeRegistry.registerNode('TransactionHistory', {
      name: '交易历史',
      description: '查询和管理用户的交易历史记录',
      category: '支付系统',
      createNode: () => new TransactionHistoryNode(),
      inputs: [
        { name: 'userId', type: 'string', label: '用户ID' },
        { name: 'startDate', type: 'string', label: '开始日期' },
        { name: 'endDate', type: 'string', label: '结束日期' },
        { name: 'transactionType', type: 'string', label: '交易类型', defaultValue: 'all' },
        { name: 'limit', type: 'number', label: '返回数量限制', defaultValue: 50 },
        { name: 'offset', type: 'number', label: '偏移量', defaultValue: 0 },
        { name: 'sortBy', type: 'string', label: '排序字段', defaultValue: 'timestamp' },
        { name: 'sortOrder', type: 'string', label: '排序顺序', defaultValue: 'desc' }
      ],
      outputs: [
        { name: 'transactions', type: 'array', label: '交易列表' },
        { name: 'totalCount', type: 'number', label: '总交易数' },
        { name: 'totalAmount', type: 'number', label: '总交易金额' },
        { name: 'success', type: 'boolean', label: '查询成功' },
        { name: 'errorMessage', type: 'string', label: '错误信息' },
        { name: 'summary', type: 'object', label: '交易摘要' }
      ],
      color: '#607D8B'
    });

    // 支付分析节点
    NodeRegistry.registerNode('PaymentAnalytics', {
      name: '支付分析',
      description: '提供支付数据的统计分析和报表功能',
      category: '支付系统',
      createNode: () => new PaymentAnalyticsNode(),
      inputs: [
        { name: 'analysisType', type: 'string', label: '分析类型', defaultValue: 'revenue' },
        { name: 'timeRange', type: 'string', label: '时间范围', defaultValue: '30d' },
        { name: 'startDate', type: 'string', label: '开始日期' },
        { name: 'endDate', type: 'string', label: '结束日期' },
        { name: 'groupBy', type: 'string', label: '分组方式', defaultValue: 'day' },
        { name: 'currency', type: 'string', label: '货币类型', defaultValue: 'USD' },
        { name: 'filters', type: 'object', label: '过滤条件', defaultValue: {} }
      ],
      outputs: [
        { name: 'analyticsData', type: 'object', label: '分析数据' },
        { name: 'chartData', type: 'array', label: '图表数据' },
        { name: 'summary', type: 'object', label: '数据摘要' },
        { name: 'trends', type: 'object', label: '趋势分析' },
        { name: 'success', type: 'boolean', label: '分析成功' },
        { name: 'errorMessage', type: 'string', label: '错误信息' }
      ],
      color: '#795548'
    });

    console.log('支付系统节点注册完成（6个）');
  }

  /**
   * 注册第三方集成节点（5个）
   */
  private registerThirdPartyNodes(): void {
    console.log('注册第三方集成节点...');

    // Google服务节点
    NodeRegistry.registerNode('GoogleServices', {
      name: 'Google服务',
      description: '集成Google各种服务API',
      category: '第三方集成',
      createNode: () => new GoogleServicesNode(),
      inputs: [
        { name: 'service', type: 'string', label: '服务类型', defaultValue: 'auth' },
        { name: 'action', type: 'string', label: '操作类型', defaultValue: 'login' },
        { name: 'apiKey', type: 'string', label: 'API密钥' },
        { name: 'clientId', type: 'string', label: '客户端ID' },
        { name: 'scopes', type: 'array', label: '权限范围', defaultValue: ['profile', 'email'] },
        { name: 'query', type: 'string', label: '查询内容' },
        { name: 'data', type: 'object', label: '数据', defaultValue: {} }
      ],
      outputs: [
        { name: 'result', type: 'object', label: '结果' },
        { name: 'accessToken', type: 'string', label: '访问令牌' },
        { name: 'userInfo', type: 'object', label: '用户信息' },
        { name: 'success', type: 'boolean', label: '操作成功' },
        { name: 'errorMessage', type: 'string', label: '错误信息' },
        { name: 'responseData', type: 'object', label: '响应数据' }
      ],
      color: '#4285F4'
    });

    // Facebook集成节点
    NodeRegistry.registerNode('FacebookIntegration', {
      name: 'Facebook集成',
      description: '集成Facebook Graph API和社交功能',
      category: '第三方集成',
      createNode: () => new FacebookIntegrationNode(),
      inputs: [
        { name: 'action', type: 'string', label: '操作类型', defaultValue: 'login' },
        { name: 'appId', type: 'string', label: '应用ID' },
        { name: 'accessToken', type: 'string', label: '访问令牌' },
        { name: 'permissions', type: 'array', label: '权限', defaultValue: ['public_profile', 'email'] },
        { name: 'postContent', type: 'string', label: '发布内容' },
        { name: 'userId', type: 'string', label: '用户ID', defaultValue: 'me' },
        { name: 'data', type: 'object', label: '数据', defaultValue: {} }
      ],
      outputs: [
        { name: 'result', type: 'object', label: '结果' },
        { name: 'userProfile', type: 'object', label: '用户资料' },
        { name: 'postId', type: 'string', label: '发布ID' },
        { name: 'success', type: 'boolean', label: '操作成功' },
        { name: 'errorMessage', type: 'string', label: '错误信息' },
        { name: 'responseData', type: 'object', label: '响应数据' }
      ],
      color: '#1877F2'
    });

    // Twitter集成节点
    NodeRegistry.registerNode('TwitterIntegration', {
      name: 'Twitter集成',
      description: '集成Twitter API v2功能',
      category: '第三方集成',
      createNode: () => new TwitterIntegrationNode(),
      inputs: [
        { name: 'action', type: 'string', label: '操作类型', defaultValue: 'tweet' },
        { name: 'apiKey', type: 'string', label: 'API密钥' },
        { name: 'apiSecret', type: 'string', label: 'API密钥密码' },
        { name: 'accessToken', type: 'string', label: '访问令牌' },
        { name: 'accessTokenSecret', type: 'string', label: '访问令牌密码' },
        { name: 'tweetText', type: 'string', label: '推文内容' },
        { name: 'query', type: 'string', label: '搜索查询' },
        { name: 'userId', type: 'string', label: '用户ID' },
        { name: 'tweetId', type: 'string', label: '推文ID' },
        { name: 'mediaIds', type: 'array', label: '媒体ID列表', defaultValue: [] }
      ],
      outputs: [
        { name: 'result', type: 'object', label: '结果' },
        { name: 'tweetData', type: 'object', label: '推文数据' },
        { name: 'userProfile', type: 'object', label: '用户资料' },
        { name: 'searchResults', type: 'array', label: '搜索结果' },
        { name: 'success', type: 'boolean', label: '操作成功' },
        { name: 'errorMessage', type: 'string', label: '错误信息' }
      ],
      color: '#1DA1F2'
    });

    // 云存储节点
    NodeRegistry.registerNode('CloudStorage', {
      name: '云存储',
      description: '集成各种云存储服务（AWS S3、Azure Blob、Google Cloud Storage等）',
      category: '第三方集成',
      createNode: () => new CloudStorageNode(),
      inputs: [
        { name: 'provider', type: 'string', label: '存储提供商', defaultValue: 'aws' },
        { name: 'action', type: 'string', label: '操作类型', defaultValue: 'upload' },
        { name: 'accessKey', type: 'string', label: '访问密钥' },
        { name: 'secretKey', type: 'string', label: '密钥' },
        { name: 'bucket', type: 'string', label: '存储桶名称' },
        { name: 'fileName', type: 'string', label: '文件名' },
        { name: 'fileData', type: 'string', label: '文件数据' },
        { name: 'filePath', type: 'string', label: '文件路径' },
        { name: 'metadata', type: 'object', label: '元数据', defaultValue: {} },
        { name: 'permissions', type: 'string', label: '权限设置', defaultValue: 'private' }
      ],
      outputs: [
        { name: 'result', type: 'object', label: '结果' },
        { name: 'fileUrl', type: 'string', label: '文件URL' },
        { name: 'fileInfo', type: 'object', label: '文件信息' },
        { name: 'fileList', type: 'array', label: '文件列表' },
        { name: 'success', type: 'boolean', label: '操作成功' },
        { name: 'errorMessage', type: 'string', label: '错误信息' }
      ],
      color: '#FF6F00'
    });

    // 分析集成节点
    NodeRegistry.registerNode('AnalyticsIntegration', {
      name: '分析集成',
      description: '集成各种分析服务（Google Analytics、Mixpanel、Amplitude等）',
      category: '第三方集成',
      createNode: () => new AnalyticsIntegrationNode(),
      inputs: [
        { name: 'provider', type: 'string', label: '分析提供商', defaultValue: 'google' },
        { name: 'action', type: 'string', label: '操作类型', defaultValue: 'track' },
        { name: 'trackingId', type: 'string', label: '跟踪ID' },
        { name: 'apiKey', type: 'string', label: 'API密钥' },
        { name: 'eventName', type: 'string', label: '事件名称' },
        { name: 'eventProperties', type: 'object', label: '事件属性', defaultValue: {} },
        { name: 'userId', type: 'string', label: '用户ID' },
        { name: 'userProperties', type: 'object', label: '用户属性', defaultValue: {} },
        { name: 'reportType', type: 'string', label: '报告类型', defaultValue: 'realtime' },
        { name: 'dateRange', type: 'object', label: '日期范围', defaultValue: {} }
      ],
      outputs: [
        { name: 'result', type: 'object', label: '结果' },
        { name: 'eventId', type: 'string', label: '事件ID' },
        { name: 'reportData', type: 'object', label: '报告数据' },
        { name: 'userProfile', type: 'object', label: '用户画像' },
        { name: 'success', type: 'boolean', label: '操作成功' },
        { name: 'errorMessage', type: 'string', label: '错误信息' }
      ],
      color: '#E91E63'
    });

    console.log('第三方集成节点注册完成（5个）');
  }

  /**
   * 获取所有已注册的节点类型
   */
  public getAllRegisteredNodeTypes(): string[] {
    return [
      // 支付系统节点
      'PaymentGateway',
      'Subscription',
      'InAppPurchase',
      'WalletSystem',
      'TransactionHistory',
      'PaymentAnalytics',

      // 第三方集成节点
      'GoogleServices',
      'FacebookIntegration',
      'TwitterIntegration',
      'CloudStorage',
      'AnalyticsIntegration'
    ];
  }

  /**
   * 检查节点是否已注册
   */
  public isRegistered(): boolean {
    return this.registered;
  }

  /**
   * 重置注册状态（用于测试）
   */
  public resetRegistration(): void {
    this.registered = false;
  }
}

// 导出单例实例
export const batch34ExtensionNodesRegistry = Batch34ExtensionNodesRegistry.getInstance();
