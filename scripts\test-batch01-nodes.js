#!/usr/bin/env node

/**
 * 批次0.1渲染系统节点测试运行脚本
 * 运行所有批次0.1相关的测试并生成报告
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

/**
 * 测试配置
 */
const TEST_CONFIG = {
  // 测试文件路径
  testFiles: [
    'engine/src/visual-script/nodes/rendering/__tests__/Batch01RenderingNodes.test.ts',
    'engine/src/visual-script/registry/__tests__/Batch01NodesRegistry.test.ts',
    'editor/src/components/visual-script/nodes/__tests__/Batch01NodesIntegration.test.ts'
  ],
  
  // 测试覆盖率阈值
  coverageThresholds: {
    statements: 80,
    branches: 75,
    functions: 80,
    lines: 80
  },
  
  // 输出目录
  outputDir: 'test-results/batch01',
  
  // Jest配置
  jestConfig: {
    testEnvironment: 'node',
    collectCoverage: true,
    coverageDirectory: 'test-results/batch01/coverage',
    coverageReporters: ['text', 'lcov', 'html', 'json'],
    testMatch: [
      '**/batch01/**/*.test.ts',
      '**/*Batch01*.test.ts'
    ],
    moduleFileExtensions: ['ts', 'tsx', 'js', 'jsx', 'json'],
    transform: {
      '^.+\\.(ts|tsx)$': 'ts-jest'
    },
    setupFilesAfterEnv: ['<rootDir>/jest.setup.js'],
    testTimeout: 30000
  }
};

/**
 * 颜色输出工具
 */
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

/**
 * 日志工具
 */
const logger = {
  info: (msg) => console.log(`${colors.blue}ℹ${colors.reset} ${msg}`),
  success: (msg) => console.log(`${colors.green}✓${colors.reset} ${msg}`),
  warning: (msg) => console.log(`${colors.yellow}⚠${colors.reset} ${msg}`),
  error: (msg) => console.log(`${colors.red}✗${colors.reset} ${msg}`),
  header: (msg) => console.log(`\n${colors.bright}${colors.cyan}${msg}${colors.reset}\n`),
  separator: () => console.log(`${colors.cyan}${'='.repeat(60)}${colors.reset}`)
};

/**
 * 创建输出目录
 */
function createOutputDirectory() {
  const outputPath = path.resolve(TEST_CONFIG.outputDir);
  if (!fs.existsSync(outputPath)) {
    fs.mkdirSync(outputPath, { recursive: true });
    logger.success(`创建输出目录: ${outputPath}`);
  }
}

/**
 * 检查测试文件是否存在
 */
function checkTestFiles() {
  logger.info('检查测试文件...');
  
  const missingFiles = [];
  TEST_CONFIG.testFiles.forEach(file => {
    const filePath = path.resolve(file);
    if (!fs.existsSync(filePath)) {
      missingFiles.push(file);
    }
  });
  
  if (missingFiles.length > 0) {
    logger.error('以下测试文件不存在:');
    missingFiles.forEach(file => logger.error(`  - ${file}`));
    return false;
  }
  
  logger.success(`所有 ${TEST_CONFIG.testFiles.length} 个测试文件存在`);
  return true;
}

/**
 * 运行Jest测试
 */
function runJestTests() {
  logger.info('运行Jest测试...');
  
  try {
    // 创建临时Jest配置文件
    const jestConfigPath = path.resolve('jest.batch01.config.js');
    const jestConfigContent = `
module.exports = ${JSON.stringify(TEST_CONFIG.jestConfig, null, 2)};
`;
    
    fs.writeFileSync(jestConfigPath, jestConfigContent);
    logger.success('创建Jest配置文件');
    
    // 运行Jest
    const jestCommand = `npx jest --config=${jestConfigPath} --verbose --passWithNoTests`;
    logger.info(`执行命令: ${jestCommand}`);
    
    const output = execSync(jestCommand, { 
      encoding: 'utf8',
      stdio: 'pipe'
    });
    
    logger.success('Jest测试执行完成');
    
    // 清理临时配置文件
    fs.unlinkSync(jestConfigPath);
    
    return {
      success: true,
      output: output
    };
    
  } catch (error) {
    logger.error('Jest测试执行失败');
    logger.error(error.message);
    
    return {
      success: false,
      error: error.message,
      output: error.stdout || error.stderr || ''
    };
  }
}

/**
 * 分析测试结果
 */
function analyzeTestResults(testResult) {
  logger.info('分析测试结果...');
  
  const output = testResult.output;
  const results = {
    totalTests: 0,
    passedTests: 0,
    failedTests: 0,
    skippedTests: 0,
    coverage: {
      statements: 0,
      branches: 0,
      functions: 0,
      lines: 0
    },
    testSuites: []
  };
  
  // 解析测试数量
  const testSummaryMatch = output.match(/Tests:\s+(\d+)\s+passed(?:,\s+(\d+)\s+failed)?(?:,\s+(\d+)\s+skipped)?(?:,\s+(\d+)\s+total)?/);
  if (testSummaryMatch) {
    results.passedTests = parseInt(testSummaryMatch[1]) || 0;
    results.failedTests = parseInt(testSummaryMatch[2]) || 0;
    results.skippedTests = parseInt(testSummaryMatch[3]) || 0;
    results.totalTests = parseInt(testSummaryMatch[4]) || (results.passedTests + results.failedTests + results.skippedTests);
  }
  
  // 解析覆盖率
  const coverageMatches = output.match(/All files\s+\|\s+([\d.]+)\s+\|\s+([\d.]+)\s+\|\s+([\d.]+)\s+\|\s+([\d.]+)/);
  if (coverageMatches) {
    results.coverage.statements = parseFloat(coverageMatches[1]);
    results.coverage.branches = parseFloat(coverageMatches[2]);
    results.coverage.functions = parseFloat(coverageMatches[3]);
    results.coverage.lines = parseFloat(coverageMatches[4]);
  }
  
  return results;
}

/**
 * 生成测试报告
 */
function generateTestReport(testResult, analysis) {
  logger.info('生成测试报告...');
  
  const reportPath = path.resolve(TEST_CONFIG.outputDir, 'test-report.json');
  const htmlReportPath = path.resolve(TEST_CONFIG.outputDir, 'test-report.html');
  
  const report = {
    timestamp: new Date().toISOString(),
    success: testResult.success,
    summary: analysis,
    configuration: TEST_CONFIG,
    output: testResult.output,
    error: testResult.error || null
  };
  
  // 生成JSON报告
  fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
  logger.success(`JSON报告已生成: ${reportPath}`);
  
  // 生成HTML报告
  const htmlContent = generateHTMLReport(report);
  fs.writeFileSync(htmlReportPath, htmlContent);
  logger.success(`HTML报告已生成: ${htmlReportPath}`);
  
  return report;
}

/**
 * 生成HTML报告
 */
function generateHTMLReport(report) {
  const { summary, timestamp, success } = report;
  
  return `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>批次0.1渲染系统节点测试报告</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { text-align: center; margin-bottom: 30px; }
        .status { padding: 10px; border-radius: 4px; margin: 10px 0; }
        .success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .failure { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .stats { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin: 20px 0; }
        .stat-card { background: #f8f9fa; padding: 15px; border-radius: 4px; text-align: center; }
        .stat-number { font-size: 2em; font-weight: bold; color: #007bff; }
        .coverage-bar { background: #e9ecef; height: 20px; border-radius: 10px; overflow: hidden; margin: 5px 0; }
        .coverage-fill { height: 100%; background: linear-gradient(90deg, #28a745, #ffc107, #dc3545); transition: width 0.3s ease; }
        .timestamp { color: #6c757d; font-size: 0.9em; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>批次0.1渲染系统节点测试报告</h1>
            <div class="timestamp">生成时间: ${timestamp}</div>
        </div>
        
        <div class="status ${success ? 'success' : 'failure'}">
            <strong>测试状态: ${success ? '✓ 通过' : '✗ 失败'}</strong>
        </div>
        
        <div class="stats">
            <div class="stat-card">
                <div class="stat-number">${summary.totalTests}</div>
                <div>总测试数</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" style="color: #28a745">${summary.passedTests}</div>
                <div>通过测试</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" style="color: #dc3545">${summary.failedTests}</div>
                <div>失败测试</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" style="color: #ffc107">${summary.skippedTests}</div>
                <div>跳过测试</div>
            </div>
        </div>

        <div class="section">
            <h2>批次0.1节点统计</h2>
            <ul>
                <li>材质管理节点: 24个</li>
                <li>后处理效果节点: 17个</li>
                <li>着色器节点: 18个</li>
                <li>渲染优化节点: 15个</li>
                <li>总计: 74个节点</li>
            </ul>
        </div>
        
        <h2>测试覆盖率</h2>
        <div>
            <div>语句覆盖率: ${summary.coverage.statements}%</div>
            <div class="coverage-bar">
                <div class="coverage-fill" style="width: ${summary.coverage.statements}%"></div>
            </div>
            
            <div>分支覆盖率: ${summary.coverage.branches}%</div>
            <div class="coverage-bar">
                <div class="coverage-fill" style="width: ${summary.coverage.branches}%"></div>
            </div>
            
            <div>函数覆盖率: ${summary.coverage.functions}%</div>
            <div class="coverage-bar">
                <div class="coverage-fill" style="width: ${summary.coverage.functions}%"></div>
            </div>
            
            <div>行覆盖率: ${summary.coverage.lines}%</div>
            <div class="coverage-bar">
                <div class="coverage-fill" style="width: ${summary.coverage.lines}%"></div>
            </div>
        </div>
        
        <h2>测试文件</h2>
        <ul>
            ${TEST_CONFIG.testFiles.map(file => `<li>${file}</li>`).join('')}
        </ul>
    </div>
</body>
</html>
`;
}

/**
 * 检查覆盖率阈值
 */
function checkCoverageThresholds(coverage) {
  logger.info('检查覆盖率阈值...');
  
  const thresholds = TEST_CONFIG.coverageThresholds;
  const failures = [];
  
  Object.keys(thresholds).forEach(metric => {
    const actual = coverage[metric];
    const required = thresholds[metric];
    
    if (actual < required) {
      failures.push(`${metric}: ${actual}% < ${required}%`);
    }
  });
  
  if (failures.length > 0) {
    logger.warning('以下覆盖率指标未达到阈值:');
    failures.forEach(failure => logger.warning(`  - ${failure}`));
    return false;
  }
  
  logger.success('所有覆盖率指标均达到阈值');
  return true;
}

/**
 * 主函数
 */
function main() {
  logger.header('批次0.1渲染系统节点测试');
  logger.separator();
  
  try {
    // 1. 创建输出目录
    createOutputDirectory();
    
    // 2. 检查测试文件
    if (!checkTestFiles()) {
      process.exit(1);
    }
    
    // 3. 运行测试
    const testResult = runJestTests();
    
    // 4. 分析结果
    const analysis = analyzeTestResults(testResult);
    
    // 5. 生成报告
    const report = generateTestReport(testResult, analysis);
    
    // 6. 检查覆盖率
    const coverageOk = checkCoverageThresholds(analysis.coverage);
    
    // 7. 输出总结
    logger.separator();
    logger.header('测试总结');
    
    if (testResult.success) {
      logger.success(`测试通过: ${analysis.passedTests}/${analysis.totalTests}`);
      logger.success(`覆盖率: 语句 ${analysis.coverage.statements}%, 分支 ${analysis.coverage.branches}%, 函数 ${analysis.coverage.functions}%, 行 ${analysis.coverage.lines}%`);
      
      if (coverageOk) {
        logger.success('所有质量指标均达标');
        process.exit(0);
      } else {
        logger.warning('覆盖率未达到要求阈值');
        process.exit(1);
      }
    } else {
      logger.error(`测试失败: ${analysis.failedTests} 个测试未通过`);
      process.exit(1);
    }
    
  } catch (error) {
    logger.error('测试执行过程中发生错误:');
    logger.error(error.message);
    process.exit(1);
  }
}

// 运行主函数
if (require.main === module) {
  main();
}

module.exports = {
  TEST_CONFIG,
  main,
  runJestTests,
  analyzeTestResults,
  generateTestReport
};
