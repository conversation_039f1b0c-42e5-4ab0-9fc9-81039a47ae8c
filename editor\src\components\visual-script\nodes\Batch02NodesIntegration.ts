/**
 * 批次0.2节点编辑器集成
 * AI系统扩展集成(50个)节点在编辑器中的集成
 */

import { NodeEditor } from '../NodeEditor';
import { VisualScriptNode } from '../../../libs/dl-engine-types';
import { batch02NodesRegistry } from '../../../libs/dl-engine';

/**
 * 批次0.2节点配置接口
 */
interface Batch02NodeConfig {
  type: string;
  name: string;
  description: string;
  category: string;
  icon: string;
  color: string;
  tags: string[];
  nodeClass: any;
}

/**
 * 批次0.2节点分类映射
 */
const BATCH02_CATEGORY_MAP = {
  'AI系统/深度学习': {
    displayName: '深度学习',
    icon: 'psychology',
    color: '#E91E63',
    description: '深度学习模型和神经网络节点'
  },
  'AI系统/机器学习': {
    displayName: '机器学习',
    icon: 'smart_toy',
    color: '#9C27B0',
    description: '传统机器学习算法节点'
  },
  'AI系统/计算机视觉': {
    displayName: '计算机视觉',
    icon: 'visibility',
    color: '#2196F3',
    description: '图像处理和计算机视觉节点'
  },
  'AI系统/自然语言处理': {
    displayName: '自然语言处理',
    icon: 'translate',
    color: '#4CAF50',
    description: '文本处理和NLP节点'
  },
  'AI系统/AI工具': {
    displayName: 'AI工具',
    icon: 'build',
    color: '#FF5722',
    description: 'AI开发和部署工具节点'
  }
};

/**
 * 批次0.2节点编辑器集成类
 */
export class Batch02NodesIntegration {
  private nodeEditor: NodeEditor;
  private registeredNodes: Map<string, Batch02NodeConfig> = new Map();
  private categoryNodes: Map<string, Batch02NodeConfig[]> = new Map();

  constructor(nodeEditor: NodeEditor) {
    this.nodeEditor = nodeEditor;
    this.initializeNodes();
  }

  /**
   * 初始化批次0.2节点
   */
  private initializeNodes(): void {
    this.registerDeepLearningNodes();
    this.registerMachineLearningNodes();
    this.registerComputerVisionNodes();
    this.registerNLPNodes();
    this.registerAIToolNodes();
    this.setupNodeCategories();
    this.setupNodePalette();
  }

  /**
   * 注册深度学习节点 (15个)
   */
  private registerDeepLearningNodes(): void {
    const deepLearningNodes = [
      'DeepLearningModelNode', 'NeuralNetworkNode', 'CNNNode', 'RNNNode', 'LSTMNode',
      'TransformerNode', 'GANNode', 'AutoencoderNode', 'VAENode', 'ResNetNode',
      'UNetNode', 'AttentionNode', 'EmbeddingNode', 'DropoutNode', 'BatchNormNode'
    ];

    deepLearningNodes.forEach(nodeType => {
      this.registerNode({
        type: nodeType,
        name: this.getNodeDisplayName(nodeType),
        description: this.getNodeDescription(nodeType),
        category: 'AI系统/深度学习',
        icon: 'psychology',
        color: '#E91E63',
        tags: ['ai', 'deep-learning', 'neural-network', 'batch02'],
        nodeClass: null
      });
    });
  }

  /**
   * 注册机器学习节点 (10个)
   */
  private registerMachineLearningNodes(): void {
    const mlNodes = [
      'ReinforcementLearningNode', 'DecisionTreeNode', 'RandomForestNode', 'SVMNode', 'KMeansNode',
      'LinearRegressionNode', 'LogisticRegressionNode', 'NaiveBayesNode', 'PCANode', 'GradientBoostingNode'
    ];

    mlNodes.forEach(nodeType => {
      this.registerNode({
        type: nodeType,
        name: this.getNodeDisplayName(nodeType),
        description: this.getNodeDescription(nodeType),
        category: 'AI系统/机器学习',
        icon: 'smart_toy',
        color: '#9C27B0',
        tags: ['ai', 'machine-learning', 'algorithm', 'batch02'],
        nodeClass: null
      });
    });
  }

  /**
   * 注册计算机视觉节点 (12个)
   */
  private registerComputerVisionNodes(): void {
    const cvNodes = [
      'ImageSegmentationNode', 'ObjectDetectionNode', 'FaceRecognitionNode', 'OpticalFlowNode', 'FeatureExtractionNode',
      'ImageClassificationNode', 'EdgeDetectionNode', 'ImageFilterNode', 'StereoVisionNode', 'MotionTrackingNode',
      'ImageRegistrationNode', 'DepthEstimationNode'
    ];

    cvNodes.forEach(nodeType => {
      this.registerNode({
        type: nodeType,
        name: this.getNodeDisplayName(nodeType),
        description: this.getNodeDescription(nodeType),
        category: 'AI系统/计算机视觉',
        icon: 'visibility',
        color: '#2196F3',
        tags: ['ai', 'computer-vision', 'image-processing', 'batch02'],
        nodeClass: null
      });
    });
  }

  /**
   * 注册自然语言处理节点 (11个)
   */
  private registerNLPNodes(): void {
    const nlpNodes = [
      'TextClassificationNode', 'SentimentAnalysisNode', 'NamedEntityRecognitionNode', 'TextSummarizationNode', 'MachineTranslationNode',
      'QuestionAnsweringNode', 'TextGenerationNode', 'TokenizationNode', 'POSTaggingNode', 'SyntaxParsingNode',
      'SemanticAnalysisNode'
    ];

    nlpNodes.forEach(nodeType => {
      this.registerNode({
        type: nodeType,
        name: this.getNodeDisplayName(nodeType),
        description: this.getNodeDescription(nodeType),
        category: 'AI系统/自然语言处理',
        icon: 'translate',
        color: '#4CAF50',
        tags: ['ai', 'nlp', 'text-processing', 'batch02'],
        nodeClass: null
      });
    });
  }

  /**
   * 注册AI工具节点 (12个)
   */
  private registerAIToolNodes(): void {
    const aiToolNodes = [
      'ModelDeploymentNode', 'ModelTrainingNode', 'ModelEvaluationNode', 'DataPreprocessingNode', 'FeatureEngineeringNode',
      'HyperparameterTuningNode', 'ModelOptimizationNode', 'ModelVersioningNode', 'ModelMonitoringNode', 'AutoMLNode',
      'MLOpsNode', 'ModelInterpretabilityNode'
    ];

    aiToolNodes.forEach(nodeType => {
      this.registerNode({
        type: nodeType,
        name: this.getNodeDisplayName(nodeType),
        description: this.getNodeDescription(nodeType),
        category: 'AI系统/AI工具',
        icon: 'build',
        color: '#FF5722',
        tags: ['ai', 'tools', 'mlops', 'batch02'],
        nodeClass: null
      });
    });
  }

  /**
   * 注册节点
   */
  private registerNode(config: Batch02NodeConfig): void {
    this.registeredNodes.set(config.type, config);
    
    // 添加到分类
    const categoryNodes = this.categoryNodes.get(config.category) || [];
    categoryNodes.push(config);
    this.categoryNodes.set(config.category, categoryNodes);
  }

  /**
   * 设置节点面板
   */
  private setupNodePalette(): void {
    // 将节点添加到编辑器的节点面板
    for (const [nodeType, nodeConfig] of this.registeredNodes.entries()) {
      this.nodeEditor.addNodeToPalette(nodeType, nodeConfig);
    }

    console.log('批次0.2节点面板设置完成: 50个AI节点已添加到编辑器');
  }

  /**
   * 设置节点分类
   */
  private setupNodeCategories(): void {
    // 为编辑器添加新的节点分类
    for (const [category, categoryInfo] of Object.entries(BATCH02_CATEGORY_MAP)) {
      this.nodeEditor.addNodeCategory(category, {
        displayName: categoryInfo.displayName,
        icon: categoryInfo.icon,
        color: categoryInfo.color,
        description: categoryInfo.description,
        nodes: this.categoryNodes.get(`AI系统/${categoryInfo.displayName}`) || []
      });
    }

    console.log('批次0.2节点分类设置完成: 5个AI分类已添加到编辑器');
  }

  /**
   * 获取节点显示名称
   */
  private getNodeDisplayName(nodeType: string): string {
    const nameMap: { [key: string]: string } = {
      // 深度学习节点
      'DeepLearningModelNode': '深度学习模型',
      'NeuralNetworkNode': '神经网络',
      'CNNNode': '卷积神经网络',
      'RNNNode': '循环神经网络',
      'LSTMNode': '长短期记忆网络',
      'TransformerNode': 'Transformer',
      'GANNode': '生成对抗网络',
      'AutoencoderNode': '自编码器',
      'VAENode': '变分自编码器',
      'ResNetNode': 'ResNet',
      'UNetNode': 'U-Net',
      'AttentionNode': '注意力机制',
      'EmbeddingNode': '嵌入层',
      'DropoutNode': 'Dropout',
      'BatchNormNode': '批量归一化',
      
      // 机器学习节点
      'ReinforcementLearningNode': '强化学习',
      'DecisionTreeNode': '决策树',
      'RandomForestNode': '随机森林',
      'SVMNode': '支持向量机',
      'KMeansNode': 'K均值聚类',
      'LinearRegressionNode': '线性回归',
      'LogisticRegressionNode': '逻辑回归',
      'NaiveBayesNode': '朴素贝叶斯',
      'PCANode': '主成分分析',
      'GradientBoostingNode': '梯度提升',
      
      // 计算机视觉节点
      'ImageSegmentationNode': '图像分割',
      'ObjectDetectionNode': '目标检测',
      'FaceRecognitionNode': '人脸识别',
      'OpticalFlowNode': '光流估计',
      'FeatureExtractionNode': '特征提取',
      'ImageClassificationNode': '图像分类',
      'EdgeDetectionNode': '边缘检测',
      'ImageFilterNode': '图像滤波',
      'StereoVisionNode': '立体视觉',
      'MotionTrackingNode': '运动跟踪',
      'ImageRegistrationNode': '图像配准',
      'DepthEstimationNode': '深度估计',
      
      // 自然语言处理节点
      'TextClassificationNode': '文本分类',
      'SentimentAnalysisNode': '情感分析',
      'NamedEntityRecognitionNode': '命名实体识别',
      'TextSummarizationNode': '文本摘要',
      'MachineTranslationNode': '机器翻译',
      'QuestionAnsweringNode': '问答系统',
      'TextGenerationNode': '文本生成',
      'TokenizationNode': '分词处理',
      'POSTaggingNode': '词性标注',
      'SyntaxParsingNode': '语法解析',
      'SemanticAnalysisNode': '语义分析',
      
      // AI工具节点
      'ModelDeploymentNode': '模型部署',
      'ModelTrainingNode': '模型训练',
      'ModelEvaluationNode': '模型评估',
      'DataPreprocessingNode': '数据预处理',
      'FeatureEngineeringNode': '特征工程',
      'HyperparameterTuningNode': '超参数调优',
      'ModelOptimizationNode': '模型优化',
      'ModelVersioningNode': '模型版本管理',
      'ModelMonitoringNode': '模型监控',
      'AutoMLNode': '自动机器学习',
      'MLOpsNode': 'MLOps',
      'ModelInterpretabilityNode': '模型可解释性'
    };

    return nameMap[nodeType] || nodeType;
  }

  /**
   * 获取节点描述
   */
  private getNodeDescription(nodeType: string): string {
    const descMap: { [key: string]: string } = {
      'DeepLearningModelNode': '管理和配置深度学习模型',
      'NeuralNetworkNode': '构建基础神经网络结构',
      'CNNNode': '卷积神经网络用于图像处理',
      'RNNNode': '循环神经网络用于序列数据',
      'LSTMNode': '长短期记忆网络处理时序数据',
      'TransformerNode': 'Transformer架构用于注意力机制',
      'GANNode': '生成对抗网络用于数据生成',
      'AutoencoderNode': '自编码器用于特征学习',
      'VAENode': '变分自编码器用于生成建模',
      'ResNetNode': 'ResNet残差网络用于深度学习',
      'UNetNode': 'U-Net网络用于图像分割',
      'AttentionNode': '注意力机制增强模型性能',
      'EmbeddingNode': '嵌入层将离散数据转换为向量',
      'DropoutNode': 'Dropout层防止过拟合',
      'BatchNormNode': '批量归一化加速训练收敛'
    };

    return descMap[nodeType] || `${this.getNodeDisplayName(nodeType)}节点`;
  }

  /**
   * 获取已注册的节点
   */
  public getRegisteredNodes(): Map<string, Batch02NodeConfig> {
    return this.registeredNodes;
  }

  /**
   * 获取节点分类
   */
  public getNodeCategories(): Map<string, Batch02NodeConfig[]> {
    return this.categoryNodes;
  }

  /**
   * 获取所有已注册的节点类型
   */
  public getAllRegisteredNodeTypes(): string[] {
    return Array.from(this.registeredNodes.keys());
  }
}

/**
 * 集成批次0.2节点到编辑器
 */
export function integrateBatch02Nodes(nodeEditor: NodeEditor): Batch02NodesIntegration {
  return new Batch02NodesIntegration(nodeEditor);
}
