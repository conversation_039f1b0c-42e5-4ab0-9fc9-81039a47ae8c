/**
 * 工业制造节点演示组件
 * 展示60个工业制造节点的集成效果和使用方法
 */

import React, { useState, useEffect } from 'react';
import {
  Card,
  Row,
  Col,
  Statistic,
  Progress,
  Button,
  Space,
  Alert,
  Typography,
  Divider,
  Badge,
  Timeline,
  message
} from 'antd';
import {
  FactoryOutlined,
  SettingOutlined,
  MonitorOutlined,
  VerifiedOutlined,
  TruckOutlined,
  ThunderboltOutlined,
  PlayCircleOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import {
  IndustrialNodesIntegration,
  IndustrialNodeCategory,
  INDUSTRIAL_CATEGORY_MAP,
  integrateIndustrialNodes
} from '../nodes/IndustrialNodesIntegration';
import IndustrialNodesPanelWrapper from '../panels/IndustrialNodesPanelWrapper';

const { Title, Text, Paragraph } = Typography;

interface DemoStats {
  totalNodes: number;
  integratedNodes: number;
  categories: number;
  integrationProgress: number;
}

interface IntegrationStep {
  category: IndustrialNodeCategory;
  status: 'pending' | 'running' | 'completed' | 'error';
  nodeCount: number;
  duration?: number;
}

export const IndustrialNodesDemo: React.FC = () => {
  const { t } = useTranslation();
  const [integration, setIntegration] = useState<IndustrialNodesIntegration | null>(null);
  const [demoStats, setDemoStats] = useState<DemoStats>({
    totalNodes: 60,
    integratedNodes: 0,
    categories: 6,
    integrationProgress: 0
  });
  const [integrationSteps, setIntegrationSteps] = useState<IntegrationStep[]>([]);
  const [isRunning, setIsRunning] = useState(false);
  const [selectedNode, setSelectedNode] = useState<string | null>(null);

  // 初始化集成步骤
  useEffect(() => {
    const steps: IntegrationStep[] = Object.values(IndustrialNodeCategory).map(category => ({
      category,
      status: 'pending',
      nodeCount: INDUSTRIAL_CATEGORY_MAP[category].nodeCount
    }));
    setIntegrationSteps(steps);
  }, []);

  // 模拟节点编辑器
  const createMockNodeEditor = () => ({
    addNodeToPalette: (nodeType: string, nodeConfig: any) => {
      console.log(`✓ 节点 ${nodeType} 已添加到面板`);
    },
    addNodeCategory: (category: string, categoryInfo: any) => {
      console.log(`✓ 分类 ${categoryInfo.displayName} 已添加`);
    }
  });

  // 执行集成演示
  const runIntegrationDemo = async () => {
    setIsRunning(true);
    setDemoStats(prev => ({ ...prev, integratedNodes: 0, integrationProgress: 0 }));

    try {
      // 创建模拟节点编辑器
      const mockNodeEditor = createMockNodeEditor();
      
      // 创建工业制造节点集成实例
      const industrialIntegration = new IndustrialNodesIntegration(mockNodeEditor);

      let totalIntegrated = 0;

      // 按步骤执行集成
      for (let i = 0; i < integrationSteps.length; i++) {
        const step = integrationSteps[i];
        
        // 更新步骤状态为运行中
        setIntegrationSteps(prev => prev.map((s, index) => 
          index === i ? { ...s, status: 'running' } : s
        ));

        const startTime = Date.now();

        try {
          // 根据分类执行相应的集成方法
          switch (step.category) {
            case IndustrialNodeCategory.MES_SYSTEM:
              industrialIntegration.integrateMESNodes();
              break;
            case IndustrialNodeCategory.DEVICE_MANAGEMENT:
              industrialIntegration.integrateDeviceManagementNodes();
              break;
            case IndustrialNodeCategory.PREDICTIVE_MAINTENANCE:
              industrialIntegration.integratePredictiveMaintenanceNodes();
              break;
            case IndustrialNodeCategory.QUALITY_MANAGEMENT:
              industrialIntegration.integrateQualityManagementNodes();
              break;
            case IndustrialNodeCategory.SUPPLY_CHAIN_MANAGEMENT:
              industrialIntegration.integrateSupplyChainManagementNodes();
              break;
            case IndustrialNodeCategory.ENERGY_MANAGEMENT:
              industrialIntegration.integrateEnergyManagementNodes();
              break;
          }

          const duration = Date.now() - startTime;
          totalIntegrated += step.nodeCount;

          // 更新步骤状态为完成
          setIntegrationSteps(prev => prev.map((s, index) => 
            index === i ? { ...s, status: 'completed', duration } : s
          ));

          // 更新统计信息
          setDemoStats(prev => ({
            ...prev,
            integratedNodes: totalIntegrated,
            integrationProgress: Math.round((totalIntegrated / 60) * 100)
          }));

          // 模拟异步操作延迟
          await new Promise(resolve => setTimeout(resolve, 800));

        } catch (error) {
          // 更新步骤状态为错误
          setIntegrationSteps(prev => prev.map((s, index) => 
            index === i ? { ...s, status: 'error' } : s
          ));
          console.error(`集成 ${step.category} 失败:`, error);
        }
      }

      // 验证集成结果
      const validation = industrialIntegration.validateIntegration();
      if (validation.success) {
        message.success('工业制造节点集成演示完成！');
        setIntegration(industrialIntegration);
      } else {
        message.warning(`集成完成但存在问题: ${validation.errors.join(', ')}`);
      }

    } catch (error) {
      message.error('集成演示失败');
      console.error('集成演示错误:', error);
    } finally {
      setIsRunning(false);
    }
  };

  // 重置演示
  const resetDemo = () => {
    setIntegration(null);
    setSelectedNode(null);
    setDemoStats({
      totalNodes: 60,
      integratedNodes: 0,
      categories: 6,
      integrationProgress: 0
    });
    setIntegrationSteps(prev => prev.map(step => ({ 
      ...step, 
      status: 'pending', 
      duration: undefined 
    })));
  };

  // 处理节点选择
  const handleNodeSelect = (nodeType: string, nodeConfig: any) => {
    setSelectedNode(nodeType);
    message.info(`选择了节点: ${nodeConfig?.name || nodeType}`);
  };

  // 获取步骤图标
  const getStepIcon = (status: IntegrationStep['status']) => {
    switch (status) {
      case 'completed':
        return <CheckCircleOutlined style={{ color: '#52c41a' }} />;
      case 'running':
        return <ClockCircleOutlined style={{ color: '#1890ff' }} />;
      case 'error':
        return <CheckCircleOutlined style={{ color: '#ff4d4f' }} />;
      default:
        return <ClockCircleOutlined style={{ color: '#d9d9d9' }} />;
    }
  };

  // 获取分类图标
  const getCategoryIcon = (category: IndustrialNodeCategory) => {
    switch (category) {
      case IndustrialNodeCategory.MES_SYSTEM:
        return <FactoryOutlined />;
      case IndustrialNodeCategory.DEVICE_MANAGEMENT:
        return <SettingOutlined />;
      case IndustrialNodeCategory.PREDICTIVE_MAINTENANCE:
        return <MonitorOutlined />;
      case IndustrialNodeCategory.QUALITY_MANAGEMENT:
        return <VerifiedOutlined />;
      case IndustrialNodeCategory.SUPPLY_CHAIN_MANAGEMENT:
        return <TruckOutlined />;
      case IndustrialNodeCategory.ENERGY_MANAGEMENT:
        return <ThunderboltOutlined />;
      default:
        return <SettingOutlined />;
    }
  };

  return (
    <div style={{ padding: '24px', background: '#f0f2f5', minHeight: '100vh' }}>
      <Title level={2}>
        <FactoryOutlined /> 工业制造节点集成演示
      </Title>
      
      <Alert
        message="演示说明"
        description="本演示展示了60个工业制造节点的集成过程，包括6个主要分类的节点注册和面板集成。"
        type="info"
        showIcon
        style={{ marginBottom: 24 }}
      />

      {/* 统计信息 */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="总节点数"
              value={demoStats.totalNodes}
              prefix={<FactoryOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="已集成节点"
              value={demoStats.integratedNodes}
              prefix={<CheckCircleOutlined />}
              valueStyle={{ color: '#3f8600' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="节点分类"
              value={demoStats.categories}
              prefix={<SettingOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Progress
              type="circle"
              percent={demoStats.integrationProgress}
              size={80}
              status={demoStats.integrationProgress === 100 ? 'success' : 'active'}
            />
            <div style={{ textAlign: 'center', marginTop: 8 }}>
              <Text>集成进度</Text>
            </div>
          </Card>
        </Col>
      </Row>

      <Row gutter={16}>
        {/* 集成控制面板 */}
        <Col span={12}>
          <Card title="集成控制" style={{ height: 600 }}>
            <Space style={{ marginBottom: 16 }}>
              <Button
                type="primary"
                icon={<PlayCircleOutlined />}
                onClick={runIntegrationDemo}
                loading={isRunning}
                disabled={isRunning}
              >
                开始集成演示
              </Button>
              <Button onClick={resetDemo} disabled={isRunning}>
                重置演示
              </Button>
            </Space>

            <Divider />

            {/* 集成步骤时间线 */}
            <Timeline>
              {integrationSteps.map((step, index) => {
                const categoryInfo = INDUSTRIAL_CATEGORY_MAP[step.category];
                return (
                  <Timeline.Item
                    key={step.category}
                    dot={getStepIcon(step.status)}
                    color={step.status === 'completed' ? 'green' : 
                           step.status === 'running' ? 'blue' : 
                           step.status === 'error' ? 'red' : 'gray'}
                  >
                    <div>
                      <Space>
                        {getCategoryIcon(step.category)}
                        <Text strong>{categoryInfo.displayName}</Text>
                        <Badge count={step.nodeCount} style={{ backgroundColor: categoryInfo.color }} />
                      </Space>
                      <div style={{ marginTop: 4 }}>
                        <Text type="secondary">{categoryInfo.description}</Text>
                      </div>
                      {step.duration && (
                        <div style={{ marginTop: 4 }}>
                          <Text type="secondary">耗时: {step.duration}ms</Text>
                        </div>
                      )}
                    </div>
                  </Timeline.Item>
                );
              })}
            </Timeline>
          </Card>
        </Col>

        {/* 节点面板预览 */}
        <Col span={12}>
          <Card title="节点面板预览" style={{ height: 600 }}>
            {integration ? (
              <IndustrialNodesPanelWrapper
                onNodeSelect={handleNodeSelect}
                height="520px"
              />
            ) : (
              <div style={{ 
                height: 520, 
                display: 'flex', 
                alignItems: 'center', 
                justifyContent: 'center',
                background: '#fafafa',
                border: '1px dashed #d9d9d9',
                borderRadius: '6px'
              }}>
                <div style={{ textAlign: 'center' }}>
                  <FactoryOutlined style={{ fontSize: 48, color: '#d9d9d9', marginBottom: 16 }} />
                  <Paragraph type="secondary">
                    请先运行集成演示以查看节点面板
                  </Paragraph>
                </div>
              </div>
            )}
          </Card>
        </Col>
      </Row>

      {/* 选中节点信息 */}
      {selectedNode && integration && (
        <Card title="选中节点信息" style={{ marginTop: 16 }}>
          <Row gutter={16}>
            <Col span={12}>
              <Text strong>节点类型: </Text>
              <Text code>{selectedNode}</Text>
            </Col>
            <Col span={12}>
              <Text strong>节点配置: </Text>
              <pre style={{ background: '#f5f5f5', padding: '8px', borderRadius: '4px', fontSize: '12px' }}>
                {JSON.stringify(integration.getNodeConfig(selectedNode), null, 2)}
              </pre>
            </Col>
          </Row>
        </Card>
      )}
    </div>
  );
};

export default IndustrialNodesDemo;
