/**
 * 工业制造节点集成测试
 * 验证60个工业制造节点的正确集成和功能
 */

import { describe, test, expect, beforeEach, jest } from '@jest/globals';
import {
  IndustrialNodesIntegration,
  IndustrialNodeCategory,
  INDUSTRIAL_CATEGORY_MAP,
  ALL_INDUSTRIAL_NODES,
  MES_SYSTEM_NODES,
  DEVICE_MANAGEMENT_NODES,
  PREDICTIVE_MAINTENANCE_NODES,
  QUALITY_MANAGEMENT_NODES,
  SUPPLY_CHAIN_MANAGEMENT_NODES,
  ENERGY_MANAGEMENT_NODES
} from '../nodes/IndustrialNodesIntegration';
import { NodeRegistry } from '../../../libs/dl-engine-types';

// 模拟NodeRegistry
jest.mock('../../../libs/dl-engine-types', () => ({
  NodeRegistry: {
    getInstance: jest.fn(() => ({
      getNodeInfo: jest.fn((nodeType: string) => ({
        name: `${nodeType} Name`,
        description: `${nodeType} Description`,
        icon: 'default-icon',
        color: '#666666',
        tags: ['industrial', 'manufacturing'],
        nodeClass: `${nodeType}Class`,
        inputs: [
          { name: 'input1', type: 'string', required: true },
          { name: 'input2', type: 'number', required: false }
        ],
        outputs: [
          { name: 'output1', type: 'object' },
          { name: 'output2', type: 'boolean' }
        ]
      }))
    }))
  }
}));

// 模拟节点编辑器
const createMockNodeEditor = () => ({
  addNodeToPalette: jest.fn(),
  addNodeCategory: jest.fn(),
  getRegisteredNodes: jest.fn(() => new Map())
});

describe('IndustrialNodesIntegration', () => {
  let integration: IndustrialNodesIntegration;
  let mockNodeEditor: any;

  beforeEach(() => {
    mockNodeEditor = createMockNodeEditor();
    integration = new IndustrialNodesIntegration(mockNodeEditor);
  });

  describe('节点分类常量验证', () => {
    test('应该包含所有6个工业制造节点分类', () => {
      const categories = Object.values(IndustrialNodeCategory);
      expect(categories).toHaveLength(6);
      expect(categories).toContain(IndustrialNodeCategory.MES_SYSTEM);
      expect(categories).toContain(IndustrialNodeCategory.DEVICE_MANAGEMENT);
      expect(categories).toContain(IndustrialNodeCategory.PREDICTIVE_MAINTENANCE);
      expect(categories).toContain(IndustrialNodeCategory.QUALITY_MANAGEMENT);
      expect(categories).toContain(IndustrialNodeCategory.SUPPLY_CHAIN_MANAGEMENT);
      expect(categories).toContain(IndustrialNodeCategory.ENERGY_MANAGEMENT);
    });

    test('每个分类应该有正确的节点数量', () => {
      expect(MES_SYSTEM_NODES).toHaveLength(15);
      expect(DEVICE_MANAGEMENT_NODES).toHaveLength(10);
      expect(PREDICTIVE_MAINTENANCE_NODES).toHaveLength(10);
      expect(QUALITY_MANAGEMENT_NODES).toHaveLength(10);
      expect(SUPPLY_CHAIN_MANAGEMENT_NODES).toHaveLength(8);
      expect(ENERGY_MANAGEMENT_NODES).toHaveLength(7);
    });

    test('总节点数应该为60个', () => {
      const totalNodes = Object.values(ALL_INDUSTRIAL_NODES)
        .reduce((total, nodes) => total + nodes.length, 0);
      expect(totalNodes).toBe(60);
    });

    test('分类信息应该完整', () => {
      Object.values(IndustrialNodeCategory).forEach(category => {
        const categoryInfo = INDUSTRIAL_CATEGORY_MAP[category];
        expect(categoryInfo).toBeDefined();
        expect(categoryInfo.displayName).toBeTruthy();
        expect(categoryInfo.icon).toBeTruthy();
        expect(categoryInfo.color).toBeTruthy();
        expect(categoryInfo.description).toBeTruthy();
        expect(categoryInfo.nodeCount).toBeGreaterThan(0);
      });
    });
  });

  describe('节点集成功能', () => {
    test('应该能够集成所有工业制造节点', () => {
      integration.integrateAllNodes();
      
      const registeredNodes = integration.getRegisteredNodes();
      expect(registeredNodes.size).toBe(60);
    });

    test('应该能够按分类集成MES系统节点', () => {
      integration.integrateMESNodes();
      
      const mesNodes = integration.getNodesByCategory(IndustrialNodeCategory.MES_SYSTEM);
      expect(mesNodes).toHaveLength(15);
      
      // 验证所有MES节点都已注册
      MES_SYSTEM_NODES.forEach(nodeType => {
        expect(integration.isNodeRegistered(nodeType)).toBe(true);
      });
    });

    test('应该能够按分类集成设备管理节点', () => {
      integration.integrateDeviceManagementNodes();
      
      const deviceNodes = integration.getNodesByCategory(IndustrialNodeCategory.DEVICE_MANAGEMENT);
      expect(deviceNodes).toHaveLength(10);
      
      // 验证所有设备管理节点都已注册
      DEVICE_MANAGEMENT_NODES.forEach(nodeType => {
        expect(integration.isNodeRegistered(nodeType)).toBe(true);
      });
    });

    test('应该能够按分类集成预测性维护节点', () => {
      integration.integratePredictiveMaintenanceNodes();
      
      const maintenanceNodes = integration.getNodesByCategory(IndustrialNodeCategory.PREDICTIVE_MAINTENANCE);
      expect(maintenanceNodes).toHaveLength(10);
      
      // 验证所有预测性维护节点都已注册
      PREDICTIVE_MAINTENANCE_NODES.forEach(nodeType => {
        expect(integration.isNodeRegistered(nodeType)).toBe(true);
      });
    });

    test('应该能够按分类集成质量管理节点', () => {
      integration.integrateQualityManagementNodes();
      
      const qualityNodes = integration.getNodesByCategory(IndustrialNodeCategory.QUALITY_MANAGEMENT);
      expect(qualityNodes).toHaveLength(10);
      
      // 验证所有质量管理节点都已注册
      QUALITY_MANAGEMENT_NODES.forEach(nodeType => {
        expect(integration.isNodeRegistered(nodeType)).toBe(true);
      });
    });

    test('应该能够按分类集成供应链管理节点', () => {
      integration.integrateSupplyChainManagementNodes();
      
      const supplyChainNodes = integration.getNodesByCategory(IndustrialNodeCategory.SUPPLY_CHAIN_MANAGEMENT);
      expect(supplyChainNodes).toHaveLength(8);
      
      // 验证所有供应链管理节点都已注册
      SUPPLY_CHAIN_MANAGEMENT_NODES.forEach(nodeType => {
        expect(integration.isNodeRegistered(nodeType)).toBe(true);
      });
    });

    test('应该能够按分类集成能耗管理节点', () => {
      integration.integrateEnergyManagementNodes();
      
      const energyNodes = integration.getNodesByCategory(IndustrialNodeCategory.ENERGY_MANAGEMENT);
      expect(energyNodes).toHaveLength(7);
      
      // 验证所有能耗管理节点都已注册
      ENERGY_MANAGEMENT_NODES.forEach(nodeType => {
        expect(integration.isNodeRegistered(nodeType)).toBe(true);
      });
    });
  });

  describe('节点配置验证', () => {
    beforeEach(() => {
      integration.integrateAllNodes();
    });

    test('每个节点应该有完整的配置信息', () => {
      const registeredNodes = integration.getRegisteredNodes();
      
      registeredNodes.forEach((nodeConfig, nodeType) => {
        expect(nodeConfig.type).toBe(nodeType);
        expect(nodeConfig.name).toBeTruthy();
        expect(nodeConfig.description).toBeTruthy();
        expect(nodeConfig.category).toBeTruthy();
        expect(nodeConfig.icon).toBeTruthy();
        expect(nodeConfig.color).toBeTruthy();
        expect(Array.isArray(nodeConfig.tags)).toBe(true);
        expect(nodeConfig.nodeClass).toBeTruthy();
        expect(Array.isArray(nodeConfig.inputs)).toBe(true);
        expect(Array.isArray(nodeConfig.outputs)).toBe(true);
      });
    });

    test('应该能够获取特定节点的配置', () => {
      const nodeConfig = integration.getNodeConfig('ProductionOrderNode');
      expect(nodeConfig).toBeDefined();
      expect(nodeConfig.type).toBe('ProductionOrderNode');
      expect(nodeConfig.category).toBe(IndustrialNodeCategory.MES_SYSTEM);
    });

    test('应该能够检查节点是否已注册', () => {
      expect(integration.isNodeRegistered('ProductionOrderNode')).toBe(true);
      expect(integration.isNodeRegistered('NonExistentNode')).toBe(false);
    });
  });

  describe('统计信息验证', () => {
    beforeEach(() => {
      integration.integrateAllNodes();
    });

    test('应该返回正确的总节点数', () => {
      expect(integration.getTotalNodeCount()).toBe(60);
    });

    test('应该返回正确的分类统计信息', () => {
      const stats = integration.getCategoryStatistics();
      
      expect(stats['MES系统']).toBe(15);
      expect(stats['设备管理']).toBe(10);
      expect(stats['预测性维护']).toBe(10);
      expect(stats['质量管理']).toBe(10);
      expect(stats['供应链管理']).toBe(8);
      expect(stats['能耗管理']).toBe(7);
    });
  });

  describe('集成验证', () => {
    test('完整集成应该通过验证', () => {
      integration.integrateAllNodes();
      
      const validation = integration.validateIntegration();
      expect(validation.success).toBe(true);
      expect(validation.errors).toHaveLength(0);
    });

    test('部分集成应该检测到错误', () => {
      // 只集成部分节点
      integration.integrateMESNodes();
      
      const validation = integration.validateIntegration();
      expect(validation.success).toBe(false);
      expect(validation.errors.length).toBeGreaterThan(0);
    });
  });

  describe('编辑器集成', () => {
    test('应该调用编辑器的节点面板添加方法', () => {
      integration.integrateAllNodes();
      
      // 验证addNodeToPalette被调用了60次
      expect(mockNodeEditor.addNodeToPalette).toHaveBeenCalledTimes(60);
    });

    test('应该调用编辑器的分类添加方法', () => {
      integration.integrateAllNodes();
      
      // 验证addNodeCategory被调用了6次（6个分类）
      expect(mockNodeEditor.addNodeCategory).toHaveBeenCalledTimes(6);
    });

    test('应该为每个分类正确设置分类信息', () => {
      integration.integrateAllNodes();
      
      // 验证每个分类都被正确添加
      Object.values(IndustrialNodeCategory).forEach(category => {
        const categoryInfo = INDUSTRIAL_CATEGORY_MAP[category];
        expect(mockNodeEditor.addNodeCategory).toHaveBeenCalledWith(
          category,
          expect.objectContaining({
            displayName: categoryInfo.displayName,
            icon: categoryInfo.icon,
            color: categoryInfo.color,
            description: categoryInfo.description,
            nodes: expect.any(Array)
          })
        );
      });
    });
  });

  describe('错误处理', () => {
    test('应该处理NodeRegistry获取节点信息失败的情况', () => {
      // 模拟NodeRegistry返回null
      const mockRegistry = NodeRegistry.getInstance() as any;
      mockRegistry.getNodeInfo.mockReturnValue(null);
      
      const newIntegration = new IndustrialNodesIntegration(mockNodeEditor);
      
      // 应该不会抛出错误
      expect(() => {
        newIntegration.integrateAllNodes();
      }).not.toThrow();
      
      // 但是注册的节点数应该为0
      expect(newIntegration.getTotalNodeCount()).toBe(0);
    });

    test('应该处理编辑器方法调用失败的情况', () => {
      // 模拟编辑器方法抛出错误
      mockNodeEditor.addNodeToPalette.mockImplementation(() => {
        throw new Error('Editor error');
      });
      
      // 应该不会导致整个集成失败
      expect(() => {
        integration.integrateAllNodes();
      }).not.toThrow();
    });
  });
});
