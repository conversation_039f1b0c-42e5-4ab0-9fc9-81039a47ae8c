/**
 * 编辑器节点面板更新脚本
 * 确保批次0.1的场景管理和资源管理节点能够在编辑器中正确显示
 */

const fs = require('fs');
const path = require('path');

class EditorNodePanelUpdater {
  constructor() {
    this.results = {
      integration: { updated: false, errors: [] },
      categories: { updated: false, errors: [] },
      translations: { updated: false, errors: [] },
      validation: { passed: false, errors: [] }
    };
  }

  /**
   * 更新节点集成文件
   */
  updateNodeIntegration() {
    console.log('🔧 更新节点集成文件...');
    
    try {
      const integrationPath = path.resolve('editor/src/components/visual-script/nodes/Batch01NodesIntegration.ts');
      
      if (!fs.existsSync(integrationPath)) {
        this.results.integration.errors.push('Batch01NodesIntegration.ts 文件不存在');
        return false;
      }
      
      const content = fs.readFileSync(integrationPath, 'utf8');
      
      // 检查是否包含场景管理和资源管理的配置
      const hasSceneManagement = content.includes('Scene/Management') && 
                                 content.includes('Scene/Editing') && 
                                 content.includes('Scene/Generation');
      
      const hasResourceManagement = content.includes('Resource/Loading') && 
                                   content.includes('Resource/Optimization') && 
                                   content.includes('Resource/Management');
      
      if (!hasSceneManagement) {
        this.results.integration.errors.push('缺少场景管理节点配置');
      }
      
      if (!hasResourceManagement) {
        this.results.integration.errors.push('缺少资源管理节点配置');
      }
      
      // 检查节点显示名称映射
      const sceneNodes = ['LoadSceneNode', 'SaveSceneNode', 'CreateSceneNode', 'ObjectSelectionNode'];
      const resourceNodes = ['LoadAssetNode', 'UnloadAssetNode', 'AssetOptimizationNode'];
      
      sceneNodes.forEach(node => {
        if (!content.includes(`'${node}':`)) {
          this.results.integration.errors.push(`缺少场景节点显示名称映射: ${node}`);
        }
      });
      
      resourceNodes.forEach(node => {
        if (!content.includes(`'${node}':`)) {
          this.results.integration.errors.push(`缺少资源节点显示名称映射: ${node}`);
        }
      });
      
      this.results.integration.updated = this.results.integration.errors.length === 0;
      console.log(`✅ 节点集成文件更新完成 - ${this.results.integration.updated ? '成功' : '失败'}`);
      
      return this.results.integration.updated;
      
    } catch (error) {
      this.results.integration.errors.push(`更新失败: ${error.message}`);
      return false;
    }
  }

  /**
   * 更新节点分类管理器
   */
  updateNodeCategories() {
    console.log('🔧 更新节点分类管理器...');
    
    try {
      const categoryPath = path.resolve('editor/src/components/visual-script/NodeCategoryManager.ts');
      
      if (!fs.existsSync(categoryPath)) {
        this.results.categories.errors.push('NodeCategoryManager.ts 文件不存在');
        return false;
      }
      
      const content = fs.readFileSync(categoryPath, 'utf8');
      
      // 检查场景管理分类
      const sceneCategories = ['scene', 'Scene/Management', 'Scene/Editing', 'Scene/Generation', 'Scene/Transition'];
      sceneCategories.forEach(category => {
        if (!content.includes(`id: '${category}'`)) {
          this.results.categories.errors.push(`缺少场景分类: ${category}`);
        }
      });
      
      // 检查资源管理分类
      const resourceCategories = ['resource', 'Resource/Loading', 'Resource/Optimization', 'Resource/Management'];
      resourceCategories.forEach(category => {
        if (!content.includes(`id: '${category}'`)) {
          this.results.categories.errors.push(`缺少资源分类: ${category}`);
        }
      });
      
      this.results.categories.updated = this.results.categories.errors.length === 0;
      console.log(`✅ 节点分类管理器更新完成 - ${this.results.categories.updated ? '成功' : '失败'}`);
      
      return this.results.categories.updated;
      
    } catch (error) {
      this.results.categories.errors.push(`更新失败: ${error.message}`);
      return false;
    }
  }

  /**
   * 更新翻译文件
   */
  updateTranslations() {
    console.log('🔧 更新翻译文件...');
    
    try {
      const translationPath = path.resolve('editor/src/locales/zh/scripting.json');
      
      if (!fs.existsSync(translationPath)) {
        this.results.translations.errors.push('scripting.json 翻译文件不存在');
        return false;
      }
      
      const content = fs.readFileSync(translationPath, 'utf8');
      const translations = JSON.parse(content);
      
      // 检查批次0.1节点翻译
      if (!translations.batch01Nodes || !translations.batch01Nodes.categories) {
        this.results.translations.errors.push('缺少 batch01Nodes 翻译配置');
      } else {
        const categories = translations.batch01Nodes.categories;
        
        // 检查场景管理翻译
        const sceneKeys = ['scene', 'Scene/Management', 'Scene/Editing', 'Scene/Generation', 'Scene/Transition'];
        sceneKeys.forEach(key => {
          if (!categories[key]) {
            this.results.translations.errors.push(`缺少场景分类翻译: ${key}`);
          }
        });
        
        // 检查资源管理翻译
        const resourceKeys = ['resource', 'Resource/Loading', 'Resource/Optimization', 'Resource/Management'];
        resourceKeys.forEach(key => {
          if (!categories[key]) {
            this.results.translations.errors.push(`缺少资源分类翻译: ${key}`);
          }
        });
      }
      
      this.results.translations.updated = this.results.translations.errors.length === 0;
      console.log(`✅ 翻译文件更新完成 - ${this.results.translations.updated ? '成功' : '失败'}`);
      
      return this.results.translations.updated;
      
    } catch (error) {
      this.results.translations.errors.push(`更新失败: ${error.message}`);
      return false;
    }
  }

  /**
   * 验证编辑器集成
   */
  validateIntegration() {
    console.log('🔍 验证编辑器集成...');
    
    try {
      // 检查引擎注册表
      const registryPath = path.resolve('engine/src/visual-script/registry/NodeRegistry.ts');
      if (!fs.existsSync(registryPath)) {
        this.results.validation.errors.push('NodeRegistry.ts 文件不存在');
        return false;
      }
      
      const registryContent = fs.readFileSync(registryPath, 'utf8');
      
      // 检查批次0.1注册方法
      if (!registryContent.includes('registerBatch01Nodes')) {
        this.results.validation.errors.push('NodeRegistry 中缺少 registerBatch01Nodes 方法');
      }
      
      if (!registryContent.includes('this.registerBatch01Nodes()')) {
        this.results.validation.errors.push('NodeRegistry 中未调用 registerBatch01Nodes 方法');
      }
      
      // 检查场景和资源管理节点注册
      if (!registryContent.includes('registerSceneManagementNodes')) {
        this.results.validation.errors.push('NodeRegistry 中缺少 registerSceneManagementNodes 方法');
      }
      
      if (!registryContent.includes('registerResourceManagementNodes')) {
        this.results.validation.errors.push('NodeRegistry 中缺少 registerResourceManagementNodes 方法');
      }
      
      // 检查编辑器集成文件
      const integrationPath = path.resolve('editor/src/components/visual-script/nodes/Batch01NodesIntegration.ts');
      if (fs.existsSync(integrationPath)) {
        const integrationContent = fs.readFileSync(integrationPath, 'utf8');
        
        if (!integrationContent.includes('Scene/Management') || !integrationContent.includes('Resource/Loading')) {
          this.results.validation.errors.push('编辑器集成文件中缺少场景或资源管理配置');
        }
      }
      
      this.results.validation.passed = this.results.validation.errors.length === 0;
      console.log(`✅ 编辑器集成验证完成 - ${this.results.validation.passed ? '通过' : '失败'}`);
      
      return this.results.validation.passed;
      
    } catch (error) {
      this.results.validation.errors.push(`验证失败: ${error.message}`);
      return false;
    }
  }

  /**
   * 生成更新报告
   */
  generateReport() {
    console.log('\n📋 编辑器节点面板更新报告');
    console.log('='.repeat(50));
    
    // 节点集成报告
    console.log(`\n🔗 节点集成: ${this.results.integration.updated ? '✅ 已更新' : '❌ 更新失败'}`);
    if (this.results.integration.errors.length > 0) {
      console.log('❌ 错误:');
      this.results.integration.errors.forEach(error => console.log(`   - ${error}`));
    }
    
    // 节点分类报告
    console.log(`\n📂 节点分类: ${this.results.categories.updated ? '✅ 已更新' : '❌ 更新失败'}`);
    if (this.results.categories.errors.length > 0) {
      console.log('❌ 错误:');
      this.results.categories.errors.forEach(error => console.log(`   - ${error}`));
    }
    
    // 翻译文件报告
    console.log(`\n🌐 翻译文件: ${this.results.translations.updated ? '✅ 已更新' : '❌ 更新失败'}`);
    if (this.results.translations.errors.length > 0) {
      console.log('❌ 错误:');
      this.results.translations.errors.forEach(error => console.log(`   - ${error}`));
    }
    
    // 验证报告
    console.log(`\n✅ 集成验证: ${this.results.validation.passed ? '✅ 通过' : '❌ 失败'}`);
    if (this.results.validation.errors.length > 0) {
      console.log('❌ 错误:');
      this.results.validation.errors.forEach(error => console.log(`   - ${error}`));
    }
    
    // 总结
    const allSuccess = this.results.integration.updated && 
                      this.results.categories.updated && 
                      this.results.translations.updated && 
                      this.results.validation.passed;
    
    console.log(`\n📈 更新结果: ${allSuccess ? '🎉 全部成功' : '⚠️ 需要修复'}`);
    
    if (allSuccess) {
      console.log('\n🎯 批次0.1节点面板更新完成！');
      console.log('📊 统计信息:');
      console.log('   - 场景管理节点: 33个');
      console.log('   - 资源管理节点: 22个');
      console.log('   - 节点分类: 7个新增');
      console.log('   - 翻译条目: 9个新增');
    }
    
    return allSuccess;
  }

  /**
   * 运行完整更新
   */
  async run() {
    console.log('🚀 开始编辑器节点面板更新...\n');
    
    const integrationUpdated = this.updateNodeIntegration();
    const categoriesUpdated = this.updateNodeCategories();
    const translationsUpdated = this.updateTranslations();
    const validationPassed = this.validateIntegration();
    
    const success = this.generateReport();
    
    console.log('\n' + '='.repeat(50));
    console.log(success ? '✅ 更新完成' : '❌ 更新失败');
    
    return success;
  }
}

// 运行更新
if (require.main === module) {
  const updater = new EditorNodePanelUpdater();
  updater.run().then(success => {
    process.exit(success ? 0 : 1);
  }).catch(error => {
    console.error('更新过程中发生错误:', error);
    process.exit(1);
  });
}

module.exports = EditorNodePanelUpdater;
