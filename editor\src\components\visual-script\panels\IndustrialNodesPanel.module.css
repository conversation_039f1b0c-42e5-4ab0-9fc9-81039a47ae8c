/**
 * 工业制造节点面板样式
 */

.industrialNodesPanel {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #fafafa;
}

.panelHeader {
  padding: 12px 16px;
  border-bottom: 1px solid #e8e8e8;
  background: white;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.panelTitle {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: #262626;
}

.panelContent {
  flex: 1;
  padding: 12px;
  overflow: auto;
}

.searchSection {
  margin-bottom: 16px;
}

.searchInput {
  width: 100%;
}

.statsSection {
  margin-bottom: 12px;
  padding: 8px 12px;
  background: #f0f2f5;
  border-radius: 6px;
  font-size: 12px;
  color: #666;
}

.categorySection {
  margin-bottom: 16px;
}

.categoryHeader {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 12px;
  background: white;
  border: 1px solid #e8e8e8;
  border-radius: 6px 6px 0 0;
  cursor: pointer;
  transition: all 0.2s;
}

.categoryHeader:hover {
  background: #f5f5f5;
}

.categoryTitle {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
}

.categoryIcon {
  font-size: 16px;
}

.categoryBadge {
  font-size: 12px;
  padding: 2px 6px;
  border-radius: 10px;
  color: white;
  font-weight: 500;
}

.categoryContent {
  border: 1px solid #e8e8e8;
  border-top: none;
  border-radius: 0 0 6px 6px;
  background: white;
  max-height: 300px;
  overflow: auto;
}

.nodeList {
  padding: 8px;
}

.nodeItem {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  margin-bottom: 4px;
  border: 1px solid #f0f0f0;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s;
  background: white;
}

.nodeItem:hover {
  background: #f5f5f5;
  border-color: #d9d9d9;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.nodeItem:active {
  transform: translateY(0);
}

.nodeIcon {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 14px;
  margin-right: 12px;
  flex-shrink: 0;
}

.nodeInfo {
  flex: 1;
  min-width: 0;
}

.nodeName {
  font-size: 13px;
  font-weight: 500;
  color: #262626;
  margin-bottom: 2px;
  display: flex;
  align-items: center;
  gap: 6px;
}

.nodeDescription {
  font-size: 12px;
  color: #8c8c8c;
  line-height: 1.3;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.nodeActions {
  display: flex;
  gap: 4px;
  opacity: 0;
  transition: opacity 0.2s;
}

.nodeItem:hover .nodeActions {
  opacity: 1;
}

.actionButton {
  width: 24px;
  height: 24px;
  border: none;
  background: transparent;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s;
  color: #8c8c8c;
}

.actionButton:hover {
  background: #e6f7ff;
  color: #1890ff;
}

.nodeTag {
  font-size: 10px;
  padding: 1px 4px;
  border-radius: 2px;
  color: white;
  font-weight: 500;
}

.emptyState {
  text-align: center;
  padding: 40px 20px;
  color: #8c8c8c;
}

.emptyIcon {
  font-size: 48px;
  color: #d9d9d9;
  margin-bottom: 16px;
}

.emptyText {
  font-size: 14px;
  color: #8c8c8c;
}

.loadingState {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px;
}

.categoryCollapse {
  border: none;
  background: transparent;
}

.categoryCollapse .ant-collapse-item {
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  margin-bottom: 8px;
  overflow: hidden;
}

.categoryCollapse .ant-collapse-header {
  padding: 12px 16px;
  background: white;
  border-bottom: 1px solid #e8e8e8;
}

.categoryCollapse .ant-collapse-content {
  border-top: none;
}

.categoryCollapse .ant-collapse-content-box {
  padding: 8px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .panelContent {
    padding: 8px;
  }
  
  .nodeItem {
    padding: 6px 8px;
  }
  
  .nodeIcon {
    width: 28px;
    height: 28px;
    font-size: 12px;
    margin-right: 8px;
  }
  
  .nodeName {
    font-size: 12px;
  }
  
  .nodeDescription {
    font-size: 11px;
  }
}

/* 滚动条样式 */
.panelContent::-webkit-scrollbar,
.categoryContent::-webkit-scrollbar {
  width: 6px;
}

.panelContent::-webkit-scrollbar-track,
.categoryContent::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.panelContent::-webkit-scrollbar-thumb,
.categoryContent::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.panelContent::-webkit-scrollbar-thumb:hover,
.categoryContent::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.nodeItem {
  animation: fadeIn 0.3s ease-out;
}

.categorySection {
  animation: fadeIn 0.3s ease-out;
}

/* 主题色彩变量 */
:root {
  --industrial-primary: #1890ff;
  --industrial-success: #52c41a;
  --industrial-warning: #faad14;
  --industrial-error: #ff4d4f;
  --industrial-mes: #ff9800;
  --industrial-device: #2196f3;
  --industrial-maintenance: #4caf50;
  --industrial-quality: #9c27b0;
  --industrial-supply: #ff5722;
  --industrial-energy: #4caf50;
}
