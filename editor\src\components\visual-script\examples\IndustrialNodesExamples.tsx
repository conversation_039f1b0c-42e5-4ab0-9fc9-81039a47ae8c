/**
 * 工业制造节点使用示例
 * 为每个工业制造节点子分类提供详细的使用示例和教程
 */

import React, { useState } from 'react';
import {
  Card,
  Tabs,
  Typography,
  Space,
  Button,
  Divider,
  Alert,
  Tag,
  Collapse,
  List,
  Steps,
  CodeBlock
} from 'antd';
import {
  FactoryOutlined,
  SettingOutlined,
  MonitorOutlined,
  VerifiedOutlined,
  TruckOutlined,
  ThunderboltOutlined,
  PlayCircleOutlined,
  BookOutlined,
  CodeOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { IndustrialNodeCategory, INDUSTRIAL_CATEGORY_MAP } from '../nodes/IndustrialNodesIntegration';

const { TabPane } = Tabs;
const { Title, Text, Paragraph } = Typography;
const { Panel } = Collapse;
const { Step } = Steps;

interface ExampleConfig {
  title: string;
  description: string;
  nodes: string[];
  steps: Array<{
    title: string;
    description: string;
    code?: string;
  }>;
  tips: string[];
}

// MES系统使用示例
const MES_EXAMPLES: ExampleConfig[] = [
  {
    title: '生产订单管理流程',
    description: '演示如何使用MES系统节点创建完整的生产订单管理流程',
    nodes: ['ProductionOrderNode', 'WorkflowManagementNode', 'SchedulingNode', 'ProductionTrackingNode'],
    steps: [
      {
        title: '创建生产订单',
        description: '使用ProductionOrderNode创建新的生产订单',
        code: `// 创建生产订单
const orderData = {
  orderNumber: "PO-2024-001",
  productId: "PRODUCT-123",
  quantity: 1000,
  priority: "high",
  dueDate: "2024-12-31"
};

// 连接ProductionOrderNode的输入
productionOrderNode.setInput("orderData", orderData);
productionOrderNode.setInput("action", "create");`
      },
      {
        title: '配置工作流',
        description: '使用WorkflowManagementNode设置生产工作流程',
        code: `// 配置生产工作流
const workflowConfig = {
  steps: [
    { id: "material_prep", name: "物料准备", duration: 2 },
    { id: "production", name: "生产加工", duration: 8 },
    { id: "quality_check", name: "质量检验", duration: 1 },
    { id: "packaging", name: "包装", duration: 1 }
  ]
};

workflowManagementNode.setInput("workflowConfig", workflowConfig);
workflowManagementNode.setInput("orderId", orderData.orderNumber);`
      },
      {
        title: '生产调度',
        description: '使用SchedulingNode进行生产资源调度',
        code: `// 生产调度配置
const scheduleConfig = {
  workCenter: "WC-001",
  startTime: "2024-07-05 08:00:00",
  resources: ["MACHINE-001", "OPERATOR-001"],
  priority: "high"
};

schedulingNode.setInput("scheduleConfig", scheduleConfig);
schedulingNode.setInput("orderId", orderData.orderNumber);`
      },
      {
        title: '生产跟踪',
        description: '使用ProductionTrackingNode跟踪生产进度',
        code: `// 生产跟踪
productionTrackingNode.setInput("orderId", orderData.orderNumber);
productionTrackingNode.setInput("trackingMode", "realtime");

// 获取生产进度
const progress = productionTrackingNode.getOutput("progress");
console.log("生产进度:", progress);`
      }
    ],
    tips: [
      '确保生产订单号的唯一性',
      '工作流步骤要按照实际生产流程设计',
      '合理分配生产资源避免冲突',
      '实时跟踪生产进度便于及时调整'
    ]
  },
  {
    title: '质量控制流程',
    description: '演示如何建立完整的质量控制和检验流程',
    nodes: ['QualityControlNode', 'PerformanceMonitoringNode', 'ReportGenerationNode'],
    steps: [
      {
        title: '设置质量标准',
        description: '配置产品质量检验标准和参数',
        code: `// 质量标准配置
const qualityStandards = {
  dimensions: { tolerance: 0.1, unit: "mm" },
  weight: { min: 95, max: 105, unit: "g" },
  surface: { roughness: "Ra 1.6" },
  functionality: { testRequired: true }
};

qualityControlNode.setInput("standards", qualityStandards);
qualityControlNode.setInput("productId", "PRODUCT-123");`
      },
      {
        title: '执行质量检验',
        description: '进行产品质量检验并记录结果',
        code: `// 执行质量检验
const inspectionData = {
  batchId: "BATCH-001",
  sampleSize: 10,
  inspector: "QC-001",
  testDate: new Date().toISOString()
};

qualityControlNode.setInput("inspectionData", inspectionData);
qualityControlNode.setInput("action", "inspect");

// 获取检验结果
const result = qualityControlNode.getOutput("inspectionResult");`
      }
    ],
    tips: [
      '质量标准要符合行业规范',
      '检验数据要完整准确记录',
      '不合格品要及时处理和追溯'
    ]
  }
];

// 设备管理使用示例
const DEVICE_EXAMPLES: ExampleConfig[] = [
  {
    title: '设备连接和监控',
    description: '演示如何连接工业设备并进行实时监控',
    nodes: ['DeviceConnectionNode', 'DeviceMonitoringNode', 'DeviceAlertNode'],
    steps: [
      {
        title: '建立设备连接',
        description: '配置设备连接参数并建立通信',
        code: `// 设备连接配置
const deviceConfig = {
  deviceId: "MACHINE-001",
  protocol: "MODBUS_TCP",
  ipAddress: "*************",
  port: 502,
  timeout: 5000
};

deviceConnectionNode.setInput("config", deviceConfig);
deviceConnectionNode.setInput("action", "connect");`
      },
      {
        title: '设备状态监控',
        description: '监控设备运行状态和关键参数',
        code: `// 监控配置
const monitoringConfig = {
  parameters: ["temperature", "pressure", "vibration", "speed"],
  interval: 1000, // 1秒采集一次
  alertThresholds: {
    temperature: { max: 80 },
    pressure: { max: 10 },
    vibration: { max: 5 }
  }
};

deviceMonitoringNode.setInput("deviceId", "MACHINE-001");
deviceMonitoringNode.setInput("config", monitoringConfig);`
      }
    ],
    tips: [
      '确保网络连接稳定',
      '设置合理的监控间隔',
      '配置适当的报警阈值'
    ]
  }
];

// 预测性维护使用示例
const MAINTENANCE_EXAMPLES: ExampleConfig[] = [
  {
    title: '设备健康监控和故障预测',
    description: '使用AI算法预测设备故障并制定维护计划',
    nodes: ['ConditionMonitoringNode', 'FailurePredictionNode', 'MaintenanceSchedulingNode'],
    steps: [
      {
        title: '设备状态监控',
        description: '收集设备运行数据进行健康状态评估',
        code: `// 状态监控配置
const monitoringConfig = {
  deviceId: "MACHINE-001",
  sensors: ["vibration", "temperature", "current"],
  samplingRate: 1000, // Hz
  analysisWindow: 3600 // 1小时
};

conditionMonitoringNode.setInput("config", monitoringConfig);`
      },
      {
        title: '故障预测分析',
        description: '基于历史数据和机器学习模型预测故障',
        code: `// 故障预测
const predictionConfig = {
  model: "lstm_failure_prediction",
  features: ["vibration_rms", "temperature_avg", "current_peak"],
  predictionHorizon: 168 // 7天
};

failurePredictionNode.setInput("deviceId", "MACHINE-001");
failurePredictionNode.setInput("config", predictionConfig);

// 获取预测结果
const prediction = failurePredictionNode.getOutput("prediction");`
      }
    ],
    tips: [
      '收集足够的历史数据训练模型',
      '定期更新预测模型',
      '结合专家经验验证预测结果'
    ]
  }
];

interface IndustrialNodesExamplesProps {
  category?: IndustrialNodeCategory;
  onNodeSelect?: (nodeType: string) => void;
}

export const IndustrialNodesExamples: React.FC<IndustrialNodesExamplesProps> = ({
  category,
  onNodeSelect
}) => {
  const { t } = useTranslation();
  const [activeTab, setActiveTab] = useState(category || IndustrialNodeCategory.MES_SYSTEM);

  // 获取示例数据
  const getExamplesForCategory = (cat: IndustrialNodeCategory): ExampleConfig[] => {
    switch (cat) {
      case IndustrialNodeCategory.MES_SYSTEM:
        return MES_EXAMPLES;
      case IndustrialNodeCategory.DEVICE_MANAGEMENT:
        return DEVICE_EXAMPLES;
      case IndustrialNodeCategory.PREDICTIVE_MAINTENANCE:
        return MAINTENANCE_EXAMPLES;
      default:
        return [];
    }
  };

  // 渲染示例内容
  const renderExample = (example: ExampleConfig) => (
    <Card key={example.title} style={{ marginBottom: 16 }}>
      <Title level={4}>{example.title}</Title>
      <Paragraph>{example.description}</Paragraph>
      
      {/* 使用的节点 */}
      <div style={{ marginBottom: 16 }}>
        <Text strong>使用的节点：</Text>
        <div style={{ marginTop: 8 }}>
          <Space wrap>
            {example.nodes.map(node => (
              <Tag 
                key={node} 
                color="blue" 
                style={{ cursor: 'pointer' }}
                onClick={() => onNodeSelect?.(node)}
              >
                {node}
              </Tag>
            ))}
          </Space>
        </div>
      </div>

      {/* 实施步骤 */}
      <Divider />
      <Title level={5}>实施步骤</Title>
      <Steps direction="vertical" size="small">
        {example.steps.map((step, index) => (
          <Step
            key={index}
            title={step.title}
            description={
              <div>
                <Paragraph>{step.description}</Paragraph>
                {step.code && (
                  <pre style={{ 
                    background: '#f5f5f5', 
                    padding: '12px', 
                    borderRadius: '4px',
                    fontSize: '12px',
                    overflow: 'auto'
                  }}>
                    <code>{step.code}</code>
                  </pre>
                )}
              </div>
            }
          />
        ))}
      </Steps>

      {/* 使用提示 */}
      <Divider />
      <Alert
        message="使用提示"
        description={
          <List
            size="small"
            dataSource={example.tips}
            renderItem={tip => <List.Item>• {tip}</List.Item>}
          />
        }
        type="info"
        showIcon
      />
    </Card>
  );

  return (
    <div style={{ height: '100%', overflow: 'auto' }}>
      <Card
        title={
          <Space>
            <BookOutlined />
            <Title level={3} style={{ margin: 0 }}>工业制造节点使用示例</Title>
          </Space>
        }
        style={{ height: '100%' }}
        bodyStyle={{ padding: '16px', height: 'calc(100% - 57px)', overflow: 'auto' }}
      >
        <Tabs
          activeKey={activeTab}
          onChange={setActiveTab}
          type="card"
        >
          {Object.values(IndustrialNodeCategory).map(cat => {
            const categoryInfo = INDUSTRIAL_CATEGORY_MAP[cat];
            const examples = getExamplesForCategory(cat);
            
            return (
              <TabPane
                key={cat}
                tab={
                  <Space>
                    {cat === IndustrialNodeCategory.MES_SYSTEM && <FactoryOutlined />}
                    {cat === IndustrialNodeCategory.DEVICE_MANAGEMENT && <SettingOutlined />}
                    {cat === IndustrialNodeCategory.PREDICTIVE_MAINTENANCE && <MonitorOutlined />}
                    {cat === IndustrialNodeCategory.QUALITY_MANAGEMENT && <VerifiedOutlined />}
                    {cat === IndustrialNodeCategory.SUPPLY_CHAIN_MANAGEMENT && <TruckOutlined />}
                    {cat === IndustrialNodeCategory.ENERGY_MANAGEMENT && <ThunderboltOutlined />}
                    {categoryInfo.displayName}
                  </Space>
                }
              >
                {examples.length > 0 ? (
                  examples.map(renderExample)
                ) : (
                  <Empty 
                    description={`${categoryInfo.displayName}示例正在开发中...`}
                    image={Empty.PRESENTED_IMAGE_SIMPLE}
                  />
                )}
              </TabPane>
            );
          })}
        </Tabs>
      </Card>
    </div>
  );
};

export default IndustrialNodesExamples;
