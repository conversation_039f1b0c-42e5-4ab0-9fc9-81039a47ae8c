/**
 * 视觉脚本节点注册系统
 * 统一管理所有节点的注册、分类和编辑器集成
 */
import { VisualScriptNode } from '../../visualscript/VisualScriptNode';
import { Debug } from '../../utils/Debug';

// 导入所有节点类
// 基础功能节点
// 动作捕捉节点
import { CameraInputNode } from '../nodes/mocap/CameraInputNode';
import { PoseDetectionNode } from '../nodes/mocap/PoseDetectionNode';
import { HandTrackingNode } from '../nodes/mocap/HandTrackingNode';
import { VirtualInteractionNode } from '../nodes/mocap/VirtualInteractionNode';
import { FaceDetectionNode } from '../nodes/mocap/FaceDetectionNode';
import { EyeTrackingNode } from '../nodes/mocap/EyeTrackingNode';

// 高级输入节点
import {
  MultiTouchNode,
  GestureRecognitionNode,
  VoiceInputNode,
  MotionSensorNode
} from '../nodes/input/AdvancedInputNodes';

// 传感器输入节点
import {
  AccelerometerNode,
  GyroscopeNode,
  CompassNode,
  ProximityNode,
  LightSensorNode,
  PressureSensorNode
} from '../nodes/input/SensorInputNodes';

// VR/AR输入节点
import {
  VRControllerInputNode,
  VRHeadsetTrackingNode,
  ARTouchInputNode,
  ARGestureInputNode,
  SpatialInputNode
} from '../nodes/input/VRARInputNodes';

// VR/AR高级输入节点
import {
  EyeTrackingInputNode,
  HandTrackingInputNode,
  VoiceCommandInputNode
} from '../nodes/input/VRARAdvancedNodes';

// 实体节点
import {
  CreateEntityNode,
  DestroyEntityNode,
  FindEntityNode,
  CloneEntityNode,
  EntityActiveNode
} from '../nodes/entity/EntityNodes';

// 组件节点
import {
  AddComponentNode,
  RemoveComponentNode,
  GetComponentNode,
  ComponentEnabledNode,
  GetAllComponentsNode,
  ComponentPropertyNode
} from '../nodes/entity/ComponentNodes';

// 变换节点
import {
  SetPositionNode,
  GetPositionNode,
  SetRotationNode,
  GetRotationNode,
  SetScaleNode,
  GetScaleNode,
  MoveNode,
  RotateNode
} from '../nodes/entity/TransformNodes';

// 物理节点
import {
  AddRigidBodyNode,
  AddColliderNode,
  ApplyForceNode,
  SetVelocityNode,
  CollisionDetectionNode,
  RaycastNode
} from '../nodes/physics/PhysicsNodes';

// 动画节点
import {
  TweenNode,
  KeyframeAnimationNode
} from '../nodes/animation/AnimationNodes';

import {
  KeyboardInputNode,
  MouseInputNode,
  TouchInputNode,
  GamepadInputNode
} from '../nodes/input/InputNodes';

// 音频节点
import {
  LoadAudioNode,
  PlayAudioNode,
  AudioListenerNode
} from '../nodes/audio/AudioNodes';

// 3D世界构建节点
import {
  AutoSceneGenerationNode,
  SceneLayoutNode
} from '../nodes/scene/SceneGenerationNodes';

import {
  CreateWaterBodyNode,
  WaterWaveNode
} from '../nodes/water/WaterSystemNodes';

// 粒子系统节点
import {
  ParticleEmitterNode,
  ParticleEffectNode
} from '../nodes/particles/ParticleSystemNodes';

// 后处理节点
import {
  PostProcessEffectNode
} from '../nodes/postprocessing/PostProcessingNodes';

import {
  TerrainGenerationNode,
  TerrainErosionNode
} from '../nodes/terrain/TerrainSystemNodes';

// 专业系统节点
import {
  WalletConnectNode,
  SmartContractNode,
  NFTOperationNode
} from '../nodes/blockchain/BlockchainNodes';

import {
  LearningRecordNode,
  LearningStatisticsNode,
  AchievementSystemNode
} from '../nodes/learning/LearningRecordNodes';

import {
  CreateUIElementNode,
  UILayoutNode,
  UIEventHandlerNode
} from '../nodes/ui/UINodes';

// AI增强节点
import {
  KnowledgeBaseNode,
  RAGQueryNode,
  DocumentProcessingNode,
  SemanticSearchNode
} from '../nodes/rag/RAGApplicationNodes';

import {
  GISAnalysisNode,
  SpatialQueryNode,
  GeospatialVisualizationNode,
  LocationServicesNode
} from '../nodes/spatial/SpatialInformationNodes';

// 新增的核心节点
import {
  WebSocketNode,
  WebRTCNode,
  HTTPRequestNode,
  NetworkSyncNode
} from '../nodes/network/NetworkNodes';

import {
  MaterialSystemNode,
  LightControlNode,
  CameraManagerNode,
  RenderConfigNode,
  CreateMaterialNode,
  SetMaterialPropertyNode,
  GetMaterialPropertyNode,
  MaterialBlendNode,
  MaterialAnimationNode,
  MaterialOptimizationNode,
  PBRMaterialNode,
  StandardMaterialNode,
  CustomMaterialNode,
  MaterialPresetNode
} from '../nodes/rendering/RenderingNodes';

// 光照控制节点
import {
  CreateLightNode,
  SetLightPropertyNode,
  LightAnimationNode
} from '../nodes/rendering/LightingNodes';

// 相机管理节点
import {
  CreateCameraNode
} from '../nodes/rendering/CameraNodes';

// 场景管理节点
import {
  LoadSceneNode,
  SaveSceneNode,
  CreateSceneNode,
  DestroySceneNode,
  AddObjectToSceneNode,
  RemoveObjectFromSceneNode,
  FindSceneObjectNode
} from '../nodes/scene/SceneManagementNodes';

// 场景编辑节点 - 批次3.1
import {
  SceneViewportNode,
  ObjectSelectionNode,
  ObjectTransformNode,
  ObjectDuplicationNode
} from '../nodes/scene/SceneEditingNodes';

import {
  ObjectGroupingNode,
  ObjectLayerNode,
  GridSnapNode,
  ObjectAlignmentNode,
  ObjectDistributionNode
} from '../nodes/scene/SceneEditingNodes2';

import {
  UndoRedoNode,
  HistoryManagementNode,
  SelectionFilterNode,
  ViewportNavigationNode,
  ViewportRenderingNode,
  ViewportSettingsNode
} from '../nodes/scene/SceneEditingNodes3';

// 材质编辑节点 - 批次3.1
import {
  MaterialEditorNode,
  MaterialPreviewNode,
  MaterialLibraryNode
} from '../nodes/material/MaterialEditingNodes';

import {
  MaterialImportNode,
  MaterialExportNode,
  MaterialValidationNode,
  MaterialOptimizationNode,
  MaterialVersioningNode,
  MaterialSharingNode,
  MaterialAnalyticsNode
} from '../nodes/material/MaterialEditingNodes2';

// 场景切换节点
import {
  SceneTransitionNode
} from '../nodes/scene/SceneTransitionNodes';

// 场景生成节点
import {
  AutoSceneGenerationNode,
  SceneLayoutNode
} from '../nodes/scene/SceneGenerationNodes';

import {
  AnimationStateMachineNode,
  AnimationBlendNode,
  IKSystemNode,
  AnimationEventNode
} from '../nodes/animation/AdvancedAnimationNodes';

// 第六阶段新增节点
import {
  DeviceManagerNode,
  DataCollectionNode,
  QualityInspectionNode,
  AlarmSystemNode,
  ProcessControlNode
} from '../nodes/industrial/IndustrialAutomationNodes';

// 高级音频节点
import {
  SpatialAudioNode,
  AudioFilterNode,
  AudioEffectNode
} from '../nodes/audio/AdvancedAudioNodes';

// 项目管理节点
import {
  CreateProjectNode,
  LoadProjectNode,
  SaveProjectNode,
  ProjectVersionNode,
  ProjectCollaborationNode,
  ProjectPermissionNode,
  ProjectBackupNode,
  ProjectAnalyticsNode,
  ProjectTemplateNode,
  ProjectExportNode
} from '../nodes/project/ProjectManagementNodes';

// AI服务节点
import {
  AIModelLoadNode,
  AIInferenceNode,
  AITrainingNode,
  NLPProcessingNode,
  ComputerVisionNode,
  SpeechRecognitionNode,
  SentimentAnalysisNode,
  RecommendationNode,
  ChatbotNode,
  AIOptimizationNode,
  AIMonitoringNode,
  AIModelVersionNode,
  AIDataPreprocessingNode,
  AIResultPostprocessingNode,
  AIPerformanceNode
} from '../nodes/ai/AIServiceNodes';

// 批次3.3：新增AI工具节点
import {
  ModelDeploymentNode,
  ModelMonitoringNode,
  ModelVersioningNode
} from '../nodes/ai/AIToolNodes';

import {
  AutoMLNode,
  ExplainableAINode,
  AIEthicsNode,
  ModelCompressionNode,
  QuantizationNode
} from '../nodes/ai/AIToolNodes2';

import {
  PruningNode,
  DistillationNode
} from '../nodes/ai/AIToolNodes3';

// 批次3.3：新增计算机视觉节点
import {
  ImageSegmentationNode,
  ObjectTrackingNode,
  FaceRecognitionNode,
  OpticalCharacterRecognitionNode
} from '../nodes/ai/ComputerVisionNodes2';

import {
  ImageGenerationNode,
  StyleTransferNode,
  ImageEnhancementNode,
  AugmentedRealityNode
} from '../nodes/ai/ComputerVisionNodes3';

// 批次3.3：新增自然语言处理节点
import {
  TextClassificationNode,
  NamedEntityRecognitionNode,
  SentimentAnalysisNode as SentimentAnalysisNodeNew,
  TextSummarizationNode
} from '../nodes/ai/NaturalLanguageProcessingNodes';

import {
  MachineTranslationNode,
  QuestionAnsweringNode,
  TextGenerationNode
} from '../nodes/ai/NaturalLanguageProcessingNodes2';

import {
  VRControllerNode,
  GestureRecognitionNode as VRGestureRecognitionNode,
  VoiceRecognitionNode
} from '../nodes/input/VRInputNodes';

// 批次3.3：深度学习和机器学习节点
import {
  DeepLearningModelNode,
  NeuralNetworkNode,
  ConvolutionalNetworkNode,
  RecurrentNetworkNode,
  TransformerModelNode,
  GANModelNode,
  VAEModelNode,
  AttentionMechanismNode,
  EmbeddingLayerNode,
  DropoutLayerNode,
  BatchNormalizationNode,
  ActivationFunctionNode,
  LossFunctionNode,
  OptimizerNode,
  RegularizationNode,
  ReinforcementLearningNode,
  FederatedLearningNode,
  TransferLearningNode,
  ModelEnsembleNode,
  HyperparameterTuningNode,
  ModelValidationNode,
  CrossValidationNode,
  FeatureSelectionNode,
  DimensionalityReductionNode,
  ClusteringNode
} from '../nodes/batch33/index';

// 批次3.4节点
import {
  VRControllerNode,
  ARTrackingNode,
  SpatialMappingNode,
  HandTrackingNode,
  EyeTrackingNode,
  VoiceCommandNode,
  HapticFeedbackNode,
  VRTeleportationNode,
  ARPlacementNode,
  ImmersiveUINode,
  GameStateNode,
  PlayerControllerNode,
  InventorySystemNode,
  QuestSystemNode,
  DialogueSystemNode,
  SaveLoadSystemNode,
  AchievementSystemNode,
  LeaderboardNode,
  FriendSystemNode,
  ChatSystemNode,
  GroupSystemNode,
  SocialSharingNode,
  UserGeneratedContentNode,
  CommunityFeaturesNode
} from '../nodes/batch34/index';

// 批次2.3：工业制造节点
// MES系统节点
import {
  ProductionOrderNode,
  WorkflowManagementNode,
  QualityControlNode,
  InventoryManagementNode,
  SchedulingNode,
  ResourceAllocationNode,
  ProductionTrackingNode,
  PerformanceMonitoringNode,
  ReportGenerationNode,
  AlertSystemNode,
  ComplianceCheckNode,
  MaintenanceScheduleNode,
  ProductionOptimizationNode,
  CostAnalysisNode,
  EfficiencyAnalysisNode
} from '../nodes/industrial/MESSystemNodes';

// 设备管理节点
import {
  DeviceConnectionNode,
  DeviceMonitoringNode,
  DeviceControlNode,
  DeviceMaintenanceNode,
  DeviceDiagnosticsNode,
  DeviceCalibrationNode,
  DeviceConfigurationNode,
  DevicePerformanceNode,
  DeviceAlertNode,
  DeviceLifecycleNode
} from '../nodes/industrial/DeviceManagementNodes';

// 预测性维护节点
import {
  ConditionMonitoringNode,
  FailurePredictionNode,
  MaintenanceSchedulingNode,
  PartReplacementNode,
  MaintenanceHistoryNode,
  MaintenanceCostNode,
  MaintenanceAnalyticsNode,
  MaintenanceOptimizationNode,
  MaintenanceReportingNode,
  MaintenanceWorkflowNode
} from '../nodes/industrial/PredictiveMaintenanceNodes';

// 批次2.3：质量管理节点
import {
  QualityInspectionNode as QMQualityInspectionNode,
  QualityTestingNode,
  QualityAnalysisNode,
  QualityReportingNode,
  QualityControlPlanNode,
  QualityAuditNode,
  QualityImprovementNode,
  QualityStandardsNode,
  QualityMetricsNode,
  QualityTraceabilityNode
} from '../nodes/industrial/QualityManagementNodes';

// 批次2.3：供应链管理节点
import {
  SupplierManagementNode,
  ProcurementNode,
  LogisticsNode,
  WarehouseManagementNode,
  SupplyChainOptimizationNode,
  SupplyChainAnalyticsNode,
  SupplyChainRiskNode,
  SupplyChainVisibilityNode
} from '../nodes/industrial/SupplyChainManagementNodes';

// 批次2.3：能耗管理节点
import {
  EnergyMonitoringNode,
  EnergyOptimizationNode,
  EnergyAnalyticsNode,
  EnergyReportingNode,
  EnergyForecastingNode,
  EnergyEfficiencyNode,
  CarbonFootprintNode
} from '../nodes/industrial/EnergyManagementNodes';

// 批次2.2：渲染优化节点
import {
  LODSystemNode,
  FrustumCullingNode,
  OcclusionCullingNode,
  BatchRenderingNode,
  InstancedRenderingNode,
  DrawCallOptimizationNode,
  TextureAtlasNode,
  MeshCombiningNode,
  RenderQueueNode,
  PerformanceProfilerNode,
  RenderStatisticsNode,
  GPUMemoryMonitorNode,
  RenderPipelineNode,
  CustomRenderPassNode,
  RenderTargetNode
} from '../nodes/rendering/RenderingOptimizationNodes';

// 批次2.2：后处理效果节点
import {
  BloomEffectNode,
  BlurEffectNode,
  ColorGradingNode,
  ToneMappingNode,
  SSAONode,
  SSRNode
} from '../nodes/rendering/PostProcessingEffectNodes';

import {
  MotionBlurNode,
  DepthOfFieldNode,
  FilmGrainNode,
  VignetteNode,
  ChromaticAberrationNode,
  LensDistortionNode,
  AntiAliasingNode,
  HDRProcessingNode,
  CustomPostProcessNode
} from '../nodes/rendering/AdvancedPostProcessingNodes';

// 批次2.2：着色器节点
import {
  VertexShaderNode,
  FragmentShaderNode,
  ComputeShaderNode,
  ShaderCompilerNode,
  ShaderOptimizationNode,
  ShaderVariantsNode,
  ShaderParametersNode,
  ShaderIncludeNode,
  ShaderMacroNode,
  ShaderDebugNode,
  ShaderPerformanceAnalysisNode,
  ShaderCacheNode,
  ShaderHotReloadNode,
  ShaderValidationNode,
  ShaderExportNode
} from '../nodes/rendering/ShaderNodes';

import {
  ClothSystemNode
} from '../nodes/physics/SoftBodyNodes';

// 批次1.5：物理系统增强节点
import {
  SoftBodyPhysicsNode,
  FluidSimulationNode,
  ClothSimulationNode,
  RopeSimulationNode,
  DestructionNode,
  PhysicsConstraintNode
} from '../nodes/physics/AdvancedPhysicsNodes';

import {
  PhysicsOptimizationNode,
  PhysicsLODNode,
  PhysicsPerformanceMonitorNode,
  PhysicsBatchingNode
} from '../nodes/physics/PhysicsOptimizationNodes';

// 批次1.6：音频系统增强节点
import {
  AudioMixerNode,
  AudioEffectChainNode,
  AudioReverbNode,
  AudioEQNode
} from '../nodes/audio/AdvancedAudioSystemNodes';

import {
  AudioOptimizationNode,
  AudioStreamingNode
} from '../nodes/audio/AudioOptimizationNodes';

import {
  ObjectDetectionNode,
  ImageClassificationNode,
  FeatureExtractionNode
} from '../nodes/ai/ComputerVisionNodes';

// 资源管理节点 - 批次1.3
import {
  LoadAssetNode,
  UnloadAssetNode,
  PreloadAssetNode,
  AsyncLoadAssetNode,
  LoadAssetBundleNode,
  AssetDependencyNode,
  AssetCacheNode,
  AssetCompressionNode,
  AssetEncryptionNode,
  AssetValidationNode,
  AssetMetadataNode,
  AssetVersionNode,
  AssetOptimizationNode,
  TextureCompressionNode,
  MeshOptimizationNode,
  AudioCompressionNode,
  AssetBatchingNode,
  AssetStreamingNode,
  AssetMemoryManagementNode,
  AssetGarbageCollectionNode,
  AssetPerformanceMonitorNode,
  AssetUsageAnalyticsNode
} from '../nodes/resources';

// 批次2.1 - 数据服务节点
import {
  DatabaseConnectionNode,
  DatabaseQueryNode,
  DatabaseInsertNode,
  DatabaseUpdateNode,
  DatabaseDeleteNode,
  DatabaseTransactionNode
} from '../nodes/data/DataServiceNodes';

import {
  DataValidationNode,
  DataTransformationNode,
  DataAggregationNode
} from '../nodes/data/DataServiceNodes2';

import {
  DataBackupNode,
  DataSyncNode,
  DataAnalyticsNode
} from '../nodes/data/DataServiceNodes3';

// 批次2.1 - 认证授权节点
import {
  JWTTokenNode,
  OAuth2Node,
  RBACNode
} from '../nodes/auth/AuthenticationNodes';

import {
  PermissionCheckNode,
  SecurityAuditNode,
  EncryptionNode,
  DecryptionNode,
  SecurityMonitoringNode
} from '../nodes/auth/AuthenticationNodes2';

// 批次2.1 - 文件服务节点
import {
  FileUploadNode,
  FileDownloadNode,
  FileStorageNode
} from '../nodes/file/FileServiceNodes';

import {
  FileCompressionNode,
  FileEncryptionNode,
  FileVersioningNode
} from '../nodes/file/FileServiceNodes2';

import {
  FileMetadataNode,
  FileSearchNode,
  FileSyncNode,
  FileAnalyticsNode
} from '../nodes/file/FileServiceNodes3';

/**
 * 节点分类枚举
 */
export enum NodeCategory {
  MOTION_CAPTURE = 'motion_capture',
  ENTITY_MANAGEMENT = 'entity_management',
  COMPONENT_MANAGEMENT = 'component_management',
  TRANSFORM = 'transform',
  PHYSICS = 'physics',
  ANIMATION = 'animation',
  INPUT = 'input',
  ADVANCED_INPUT = 'advanced_input',
  SENSOR_INPUT = 'sensor_input',
  VR_AR_INPUT = 'vr_ar_input',
  AUDIO = 'audio',
  SCENE_GENERATION = 'scene_generation',
  WATER_SYSTEM = 'water_system',
  PARTICLE_SYSTEM = 'particle_system',
  POST_PROCESS = 'post_process',
  TERRAIN_SYSTEM = 'terrain_system',
  BLOCKCHAIN = 'blockchain',
  LEARNING_RECORD = 'learning_record',
  UI_INTERFACE = 'ui_interface',
  RAG_APPLICATION = 'rag_application',
  SPATIAL_INFORMATION = 'spatial_information',
  NETWORK = 'network',
  RENDERING = 'rendering',
  ADVANCED_ANIMATION = 'advanced_animation',
  INDUSTRIAL_AUTOMATION = 'industrial_automation',
  ADVANCED_AUDIO = 'advanced_audio',
  VR_INPUT = 'vr_input',
  RENDERING_OPTIMIZATION = 'rendering_optimization',
  SOFT_BODY_PHYSICS = 'soft_body_physics',
  COMPUTER_VISION = 'computer_vision',
  SCENE_MANAGEMENT = 'scene_management',
  RESOURCE_MANAGEMENT = 'resource_management',
  RESOURCE_OPTIMIZATION = 'resource_optimization',
  PROJECT_MANAGEMENT = 'project_management',
  AI_SERVICE = 'ai_service',
  DATA_SERVICE = 'data_service',
  AUTHENTICATION = 'authentication',
  FILE_SERVICE = 'file_service',
  SCENE_EDITING = 'scene_editing',
  MATERIAL_EDITING = 'material_editing'
}

/**
 * 节点信息接口
 */
export interface NodeInfo {
  type: string;
  name: string;
  description: string;
  category: NodeCategory;
  nodeClass: new (nodeType: string, name: string, id?: string) => VisualScriptNode;
  icon?: string;
  color?: string;
  tags?: string[];
  deprecated?: boolean;
  experimental?: boolean;
}

/**
 * 节点注册表
 */
class NodeRegistryManager {
  private nodes: Map<string, NodeInfo> = new Map();
  private categories: Map<NodeCategory, NodeInfo[]> = new Map();
  private initialized: boolean = false;

  /**
   * 初始化节点注册表
   */
  initialize(): void {
    if (this.initialized) {
      return;
    }

    this.registerAllNodes();
    this.buildCategoryIndex();
    this.initialized = true;

    Debug.log('NodeRegistry', `节点注册完成: ${this.nodes.size}个节点, ${this.categories.size}个分类`);
  }

  /**
   * 注册所有节点
   */
  private registerAllNodes(): void {
    // 动作捕捉节点
    this.registerNode({
      type: CameraInputNode.TYPE,
      name: CameraInputNode.NAME,
      description: CameraInputNode.DESCRIPTION,
      category: NodeCategory.MOTION_CAPTURE,
      nodeClass: CameraInputNode,
      icon: 'camera',
      color: '#FF6B6B',
      tags: ['camera', 'input', 'video']
    });

    this.registerNode({
      type: PoseDetectionNode.TYPE,
      name: PoseDetectionNode.NAME,
      description: PoseDetectionNode.DESCRIPTION,
      category: NodeCategory.MOTION_CAPTURE,
      nodeClass: PoseDetectionNode,
      icon: 'person',
      color: '#FF6B6B',
      tags: ['pose', 'detection', 'mediapipe']
    });

    this.registerNode({
      type: HandTrackingNode.TYPE,
      name: HandTrackingNode.NAME,
      description: HandTrackingNode.DESCRIPTION,
      category: NodeCategory.MOTION_CAPTURE,
      nodeClass: HandTrackingNode,
      icon: 'hand',
      color: '#FF6B6B',
      tags: ['hand', 'tracking', 'gesture']
    });

    this.registerNode({
      type: VirtualInteractionNode.TYPE,
      name: VirtualInteractionNode.NAME,
      description: VirtualInteractionNode.DESCRIPTION,
      category: NodeCategory.MOTION_CAPTURE,
      nodeClass: VirtualInteractionNode,
      icon: 'touch',
      color: '#FF6B6B',
      tags: ['virtual', 'interaction', 'mapping']
    });

    this.registerNode({
      type: FaceDetectionNode.TYPE,
      name: FaceDetectionNode.NAME,
      description: FaceDetectionNode.DESCRIPTION,
      category: NodeCategory.MOTION_CAPTURE,
      nodeClass: FaceDetectionNode,
      icon: 'face',
      color: '#FF6B6B',
      tags: ['face', 'detection', 'expression']
    });

    // BodyAnalysisNode 和 MotionProcessingNode 暂时未实现
    // 等待后续开发完成后再添加

    // 实体管理节点
    this.registerNode({
      type: CreateEntityNode.TYPE,
      name: CreateEntityNode.NAME,
      description: CreateEntityNode.DESCRIPTION,
      category: NodeCategory.ENTITY_MANAGEMENT,
      nodeClass: CreateEntityNode,
      icon: 'add_box',
      color: '#4ECDC4',
      tags: ['entity', 'create', 'object']
    });

    this.registerNode({
      type: DestroyEntityNode.TYPE,
      name: DestroyEntityNode.NAME,
      description: DestroyEntityNode.DESCRIPTION,
      category: NodeCategory.ENTITY_MANAGEMENT,
      nodeClass: DestroyEntityNode,
      icon: 'delete',
      color: '#4ECDC4',
      tags: ['entity', 'destroy', 'remove']
    });

    this.registerNode({
      type: FindEntityNode.TYPE,
      name: FindEntityNode.NAME,
      description: FindEntityNode.DESCRIPTION,
      category: NodeCategory.ENTITY_MANAGEMENT,
      nodeClass: FindEntityNode,
      icon: 'search',
      color: '#4ECDC4',
      tags: ['entity', 'find', 'query']
    });

    this.registerNode({
      type: CloneEntityNode.TYPE,
      name: CloneEntityNode.NAME,
      description: CloneEntityNode.DESCRIPTION,
      category: NodeCategory.ENTITY_MANAGEMENT,
      nodeClass: CloneEntityNode,
      icon: 'content_copy',
      color: '#4ECDC4',
      tags: ['entity', 'clone', 'duplicate']
    });

    this.registerNode({
      type: EntityActiveNode.TYPE,
      name: EntityActiveNode.NAME,
      description: EntityActiveNode.DESCRIPTION,
      category: NodeCategory.ENTITY_MANAGEMENT,
      nodeClass: EntityActiveNode,
      icon: 'info',
      color: '#4ECDC4',
      tags: ['entity', 'active', 'status']
    });

    // 网络通信节点
    this.registerNode({
      type: WebSocketNode.TYPE,
      name: WebSocketNode.NAME,
      description: WebSocketNode.DESCRIPTION,
      category: NodeCategory.NETWORK,
      nodeClass: WebSocketNode,
      icon: 'wifi',
      color: '#9B59B6',
      tags: ['websocket', 'realtime', 'communication']
    });

    this.registerNode({
      type: WebRTCNode.TYPE,
      name: WebRTCNode.NAME,
      description: WebRTCNode.DESCRIPTION,
      category: NodeCategory.NETWORK,
      nodeClass: WebRTCNode,
      icon: 'video_call',
      color: '#9B59B6',
      tags: ['webrtc', 'p2p', 'video', 'audio']
    });

    this.registerNode({
      type: HTTPRequestNode.TYPE,
      name: HTTPRequestNode.NAME,
      description: HTTPRequestNode.DESCRIPTION,
      category: NodeCategory.NETWORK,
      nodeClass: HTTPRequestNode,
      icon: 'http',
      color: '#9B59B6',
      tags: ['http', 'api', 'request']
    });

    this.registerNode({
      type: NetworkSyncNode.TYPE,
      name: NetworkSyncNode.NAME,
      description: NetworkSyncNode.DESCRIPTION,
      category: NodeCategory.NETWORK,
      nodeClass: NetworkSyncNode,
      icon: 'sync',
      color: '#9B59B6',
      tags: ['sync', 'multiplayer', 'state']
    });

    // 渲染系统节点
    this.registerNode({
      type: MaterialSystemNode.TYPE,
      name: MaterialSystemNode.NAME,
      description: MaterialSystemNode.DESCRIPTION,
      category: NodeCategory.RENDERING,
      nodeClass: MaterialSystemNode,
      icon: 'palette',
      color: '#E67E22',
      tags: ['material', 'shader', 'pbr']
    });

    this.registerNode({
      type: LightControlNode.TYPE,
      name: LightControlNode.NAME,
      description: LightControlNode.DESCRIPTION,
      category: NodeCategory.RENDERING,
      nodeClass: LightControlNode,
      icon: 'lightbulb',
      color: '#E67E22',
      tags: ['light', 'illumination', 'shadow']
    });

    this.registerNode({
      type: CameraManagerNode.TYPE,
      name: CameraManagerNode.NAME,
      description: CameraManagerNode.DESCRIPTION,
      category: NodeCategory.RENDERING,
      nodeClass: CameraManagerNode,
      icon: 'videocam',
      color: '#E67E22',
      tags: ['camera', 'view', 'perspective']
    });

    this.registerNode({
      type: RenderConfigNode.TYPE,
      name: RenderConfigNode.NAME,
      description: RenderConfigNode.DESCRIPTION,
      category: NodeCategory.RENDERING,
      nodeClass: RenderConfigNode,
      icon: 'settings',
      color: '#E67E22',
      tags: ['render', 'quality', 'config']
    });

    // 高级动画节点
    this.registerNode({
      type: AnimationStateMachineNode.TYPE,
      name: AnimationStateMachineNode.NAME,
      description: AnimationStateMachineNode.DESCRIPTION,
      category: NodeCategory.ADVANCED_ANIMATION,
      nodeClass: AnimationStateMachineNode,
      icon: 'state_machine',
      color: '#8E44AD',
      tags: ['animation', 'state', 'transition']
    });

    this.registerNode({
      type: AnimationBlendNode.TYPE,
      name: AnimationBlendNode.NAME,
      description: AnimationBlendNode.DESCRIPTION,
      category: NodeCategory.ADVANCED_ANIMATION,
      nodeClass: AnimationBlendNode,
      icon: 'blend',
      color: '#8E44AD',
      tags: ['animation', 'blend', 'mix']
    });

    this.registerNode({
      type: IKSystemNode.TYPE,
      name: IKSystemNode.NAME,
      description: IKSystemNode.DESCRIPTION,
      category: NodeCategory.ADVANCED_ANIMATION,
      nodeClass: IKSystemNode,
      icon: 'skeleton',
      color: '#8E44AD',
      tags: ['ik', 'inverse', 'kinematics']
    });

    this.registerNode({
      type: AnimationEventNode.TYPE,
      name: AnimationEventNode.NAME,
      description: AnimationEventNode.DESCRIPTION,
      category: NodeCategory.ADVANCED_ANIMATION,
      nodeClass: AnimationEventNode,
      icon: 'event',
      color: '#8E44AD',
      tags: ['animation', 'event', 'callback']
    });

    // 工业自动化节点
    this.registerNode({
      type: DeviceManagerNode.TYPE,
      name: DeviceManagerNode.NAME,
      description: DeviceManagerNode.DESCRIPTION,
      category: NodeCategory.INDUSTRIAL_AUTOMATION,
      nodeClass: DeviceManagerNode,
      icon: 'device_hub',
      color: '#795548',
      tags: ['device', 'industrial', 'automation']
    });

    this.registerNode({
      type: DataCollectionNode.TYPE,
      name: DataCollectionNode.NAME,
      description: DataCollectionNode.DESCRIPTION,
      category: NodeCategory.INDUSTRIAL_AUTOMATION,
      nodeClass: DataCollectionNode,
      icon: 'data_usage',
      color: '#795548',
      tags: ['data', 'collection', 'industrial']
    });

    this.registerNode({
      type: QualityInspectionNode.TYPE,
      name: QualityInspectionNode.NAME,
      description: QualityInspectionNode.DESCRIPTION,
      category: NodeCategory.INDUSTRIAL_AUTOMATION,
      nodeClass: QualityInspectionNode,
      icon: 'quality_check',
      color: '#795548',
      tags: ['quality', 'inspection', 'defect']
    });

    this.registerNode({
      type: AlarmSystemNode.TYPE,
      name: AlarmSystemNode.NAME,
      description: AlarmSystemNode.DESCRIPTION,
      category: NodeCategory.INDUSTRIAL_AUTOMATION,
      nodeClass: AlarmSystemNode,
      icon: 'alarm',
      color: '#795548',
      tags: ['alarm', 'notification', 'alert']
    });

    this.registerNode({
      type: ProcessControlNode.TYPE,
      name: ProcessControlNode.NAME,
      description: ProcessControlNode.DESCRIPTION,
      category: NodeCategory.INDUSTRIAL_AUTOMATION,
      nodeClass: ProcessControlNode,
      icon: 'settings',
      color: '#795548',
      tags: ['process', 'control', 'workflow']
    });

    // 高级音频节点
    this.registerNode({
      type: SpatialAudioNode.TYPE,
      name: SpatialAudioNode.NAME,
      description: SpatialAudioNode.DESCRIPTION,
      category: NodeCategory.ADVANCED_AUDIO,
      nodeClass: SpatialAudioNode,
      icon: 'surround_sound',
      color: '#FF5722',
      tags: ['spatial', 'audio', '3d', 'sound']
    });

    this.registerNode({
      type: AudioFilterNode.TYPE,
      name: AudioFilterNode.NAME,
      description: AudioFilterNode.DESCRIPTION,
      category: NodeCategory.ADVANCED_AUDIO,
      nodeClass: AudioFilterNode,
      icon: 'equalizer',
      color: '#FF5722',
      tags: ['audio', 'filter', 'frequency']
    });

    this.registerNode({
      type: AudioEffectNode.TYPE,
      name: AudioEffectNode.NAME,
      description: AudioEffectNode.DESCRIPTION,
      category: NodeCategory.ADVANCED_AUDIO,
      nodeClass: AudioEffectNode,
      icon: 'graphic_eq',
      color: '#FF5722',
      tags: ['audio', 'effect', 'reverb', 'delay']
    });

    // VR输入节点
    this.registerNode({
      type: VRControllerNode.TYPE,
      name: VRControllerNode.NAME,
      description: VRControllerNode.DESCRIPTION,
      category: NodeCategory.VR_INPUT,
      nodeClass: VRControllerNode,
      icon: 'gamepad',
      color: '#3F51B5',
      tags: ['vr', 'controller', 'input', 'xr']
    });

    this.registerNode({
      type: GestureRecognitionNode.TYPE,
      name: GestureRecognitionNode.NAME,
      description: GestureRecognitionNode.DESCRIPTION,
      category: NodeCategory.VR_INPUT,
      nodeClass: GestureRecognitionNode,
      icon: 'pan_tool',
      color: '#3F51B5',
      tags: ['gesture', 'hand', 'recognition']
    });

    this.registerNode({
      type: VoiceRecognitionNode.TYPE,
      name: VoiceRecognitionNode.NAME,
      description: VoiceRecognitionNode.DESCRIPTION,
      category: NodeCategory.VR_INPUT,
      nodeClass: VoiceRecognitionNode,
      icon: 'mic',
      color: '#3F51B5',
      tags: ['voice', 'speech', 'recognition']
    });

    // 高级输入节点
    this.registerNode({
      type: MultiTouchNode.TYPE,
      name: MultiTouchNode.NAME,
      description: MultiTouchNode.DESCRIPTION,
      category: NodeCategory.ADVANCED_INPUT,
      nodeClass: MultiTouchNode,
      icon: 'touch_app',
      color: '#FF9800',
      tags: ['touch', 'multitouch', 'gesture']
    });

    this.registerNode({
      type: GestureRecognitionNode.TYPE,
      name: GestureRecognitionNode.NAME,
      description: GestureRecognitionNode.DESCRIPTION,
      category: NodeCategory.ADVANCED_INPUT,
      nodeClass: GestureRecognitionNode,
      icon: 'gesture',
      color: '#FF9800',
      tags: ['gesture', 'recognition', 'touch']
    });

    this.registerNode({
      type: VoiceInputNode.TYPE,
      name: VoiceInputNode.NAME,
      description: VoiceInputNode.DESCRIPTION,
      category: NodeCategory.ADVANCED_INPUT,
      nodeClass: VoiceInputNode,
      icon: 'mic',
      color: '#FF9800',
      tags: ['voice', 'speech', 'input']
    });

    this.registerNode({
      type: MotionSensorNode.TYPE,
      name: MotionSensorNode.NAME,
      description: MotionSensorNode.DESCRIPTION,
      category: NodeCategory.ADVANCED_INPUT,
      nodeClass: MotionSensorNode,
      icon: 'sensors',
      color: '#FF9800',
      tags: ['motion', 'sensor', 'accelerometer']
    });

    // 传感器输入节点
    this.registerNode({
      type: AccelerometerNode.TYPE,
      name: AccelerometerNode.NAME,
      description: AccelerometerNode.DESCRIPTION,
      category: NodeCategory.SENSOR_INPUT,
      nodeClass: AccelerometerNode,
      icon: 'speed',
      color: '#4CAF50',
      tags: ['accelerometer', 'motion', 'sensor']
    });

    this.registerNode({
      type: GyroscopeNode.TYPE,
      name: GyroscopeNode.NAME,
      description: GyroscopeNode.DESCRIPTION,
      category: NodeCategory.SENSOR_INPUT,
      nodeClass: GyroscopeNode,
      icon: 'rotate_right',
      color: '#4CAF50',
      tags: ['gyroscope', 'rotation', 'sensor']
    });

    this.registerNode({
      type: CompassNode.TYPE,
      name: CompassNode.NAME,
      description: CompassNode.DESCRIPTION,
      category: NodeCategory.SENSOR_INPUT,
      nodeClass: CompassNode,
      icon: 'explore',
      color: '#4CAF50',
      tags: ['compass', 'direction', 'sensor']
    });

    this.registerNode({
      type: ProximityNode.TYPE,
      name: ProximityNode.NAME,
      description: ProximityNode.DESCRIPTION,
      category: NodeCategory.SENSOR_INPUT,
      nodeClass: ProximityNode,
      icon: 'near_me',
      color: '#4CAF50',
      tags: ['proximity', 'distance', 'sensor']
    });

    this.registerNode({
      type: LightSensorNode.TYPE,
      name: LightSensorNode.NAME,
      description: LightSensorNode.DESCRIPTION,
      category: NodeCategory.SENSOR_INPUT,
      nodeClass: LightSensorNode,
      icon: 'light_mode',
      color: '#4CAF50',
      tags: ['light', 'brightness', 'sensor']
    });

    this.registerNode({
      type: PressureSensorNode.TYPE,
      name: PressureSensorNode.NAME,
      description: PressureSensorNode.DESCRIPTION,
      category: NodeCategory.SENSOR_INPUT,
      nodeClass: PressureSensorNode,
      icon: 'compress',
      color: '#4CAF50',
      tags: ['pressure', 'force', 'sensor']
    });

    // VR/AR输入节点
    this.registerNode({
      type: VRControllerInputNode.TYPE,
      name: VRControllerInputNode.NAME,
      description: VRControllerInputNode.DESCRIPTION,
      category: NodeCategory.VR_AR_INPUT,
      nodeClass: VRControllerInputNode,
      icon: 'gamepad',
      color: '#9C27B0',
      tags: ['vr', 'controller', 'input']
    });

    this.registerNode({
      type: VRHeadsetTrackingNode.TYPE,
      name: VRHeadsetTrackingNode.NAME,
      description: VRHeadsetTrackingNode.DESCRIPTION,
      category: NodeCategory.VR_AR_INPUT,
      nodeClass: VRHeadsetTrackingNode,
      icon: 'headset',
      color: '#9C27B0',
      tags: ['vr', 'headset', 'tracking']
    });

    this.registerNode({
      type: ARTouchInputNode.TYPE,
      name: ARTouchInputNode.NAME,
      description: ARTouchInputNode.DESCRIPTION,
      category: NodeCategory.VR_AR_INPUT,
      nodeClass: ARTouchInputNode,
      icon: 'touch_app',
      color: '#9C27B0',
      tags: ['ar', 'touch', 'input']
    });

    this.registerNode({
      type: ARGestureInputNode.TYPE,
      name: ARGestureInputNode.NAME,
      description: ARGestureInputNode.DESCRIPTION,
      category: NodeCategory.VR_AR_INPUT,
      nodeClass: ARGestureInputNode,
      icon: 'pan_tool',
      color: '#9C27B0',
      tags: ['ar', 'gesture', 'hand']
    });

    this.registerNode({
      type: SpatialInputNode.TYPE,
      name: SpatialInputNode.NAME,
      description: SpatialInputNode.DESCRIPTION,
      category: NodeCategory.VR_AR_INPUT,
      nodeClass: SpatialInputNode,
      icon: '3d_rotation',
      color: '#9C27B0',
      tags: ['spatial', '3d', 'tracking']
    });

    this.registerNode({
      type: EyeTrackingInputNode.TYPE,
      name: EyeTrackingInputNode.NAME,
      description: EyeTrackingInputNode.DESCRIPTION,
      category: NodeCategory.VR_AR_INPUT,
      nodeClass: EyeTrackingInputNode,
      icon: 'visibility',
      color: '#9C27B0',
      tags: ['eye', 'tracking', 'gaze']
    });

    this.registerNode({
      type: HandTrackingInputNode.TYPE,
      name: HandTrackingInputNode.NAME,
      description: HandTrackingInputNode.DESCRIPTION,
      category: NodeCategory.VR_AR_INPUT,
      nodeClass: HandTrackingInputNode,
      icon: 'back_hand',
      color: '#9C27B0',
      tags: ['hand', 'tracking', 'gesture']
    });

    this.registerNode({
      type: VoiceCommandInputNode.TYPE,
      name: VoiceCommandInputNode.NAME,
      description: VoiceCommandInputNode.DESCRIPTION,
      category: NodeCategory.VR_AR_INPUT,
      nodeClass: VoiceCommandInputNode,
      icon: 'record_voice_over',
      color: '#9C27B0',
      tags: ['voice', 'command', 'speech']
    });

    // 渲染优化节点
    this.registerNode({
      type: LODSystemNode.TYPE,
      name: LODSystemNode.NAME,
      description: LODSystemNode.DESCRIPTION,
      category: NodeCategory.RENDERING_OPTIMIZATION,
      nodeClass: LODSystemNode,
      icon: 'layers',
      color: '#607D8B',
      tags: ['lod', 'optimization', 'performance']
    });

    this.registerNode({
      type: BatchRenderingNode.TYPE,
      name: BatchRenderingNode.NAME,
      description: BatchRenderingNode.DESCRIPTION,
      category: NodeCategory.RENDERING_OPTIMIZATION,
      nodeClass: BatchRenderingNode,
      icon: 'view_module',
      color: '#607D8B',
      tags: ['batch', 'rendering', 'optimization']
    });

    this.registerNode({
      type: InstancedRenderingNode.TYPE,
      name: InstancedRenderingNode.NAME,
      description: InstancedRenderingNode.DESCRIPTION,
      category: NodeCategory.RENDERING_OPTIMIZATION,
      nodeClass: InstancedRenderingNode,
      icon: 'content_copy',
      color: '#607D8B',
      tags: ['instanced', 'rendering', 'performance']
    });

    this.registerNode({
      type: FrustumCullingNode.TYPE,
      name: FrustumCullingNode.NAME,
      description: FrustumCullingNode.DESCRIPTION,
      category: NodeCategory.RENDERING_OPTIMIZATION,
      nodeClass: FrustumCullingNode,
      icon: 'visibility_off',
      color: '#607D8B',
      tags: ['culling', 'frustum', 'optimization']
    });

    // 软体物理节点
    this.registerNode({
      type: ClothSystemNode.TYPE,
      name: ClothSystemNode.NAME,
      description: ClothSystemNode.DESCRIPTION,
      category: NodeCategory.SOFT_BODY_PHYSICS,
      nodeClass: ClothSystemNode,
      icon: 'texture',
      color: '#9C27B0',
      tags: ['cloth', 'soft', 'physics', 'simulation']
    });

    // 计算机视觉节点
    this.registerNode({
      type: ObjectDetectionNode.TYPE,
      name: ObjectDetectionNode.NAME,
      description: ObjectDetectionNode.DESCRIPTION,
      category: NodeCategory.COMPUTER_VISION,
      nodeClass: ObjectDetectionNode,
      icon: 'search',
      color: '#FF9800',
      tags: ['object', 'detection', 'ai', 'vision']
    });

    this.registerNode({
      type: ImageClassificationNode.TYPE,
      name: ImageClassificationNode.NAME,
      description: ImageClassificationNode.DESCRIPTION,
      category: NodeCategory.COMPUTER_VISION,
      nodeClass: ImageClassificationNode,
      icon: 'category',
      color: '#FF9800',
      tags: ['image', 'classification', 'ai']
    });

    this.registerNode({
      type: FeatureExtractionNode.TYPE,
      name: FeatureExtractionNode.NAME,
      description: FeatureExtractionNode.DESCRIPTION,
      category: NodeCategory.COMPUTER_VISION,
      nodeClass: FeatureExtractionNode,
      icon: 'scatter_plot',
      color: '#FF9800',
      tags: ['feature', 'extraction', 'keypoint']
    });

    // ==================== 批次1.1：材质管理节点 ====================
    this.registerNode({
      type: CreateMaterialNode.TYPE,
      name: CreateMaterialNode.NAME,
      description: CreateMaterialNode.DESCRIPTION,
      category: NodeCategory.RENDERING,
      nodeClass: CreateMaterialNode,
      icon: 'palette',
      color: '#2196F3',
      tags: ['material', 'create', 'rendering']
    });

    this.registerNode({
      type: SetMaterialPropertyNode.TYPE,
      name: SetMaterialPropertyNode.NAME,
      description: SetMaterialPropertyNode.DESCRIPTION,
      category: NodeCategory.RENDERING,
      nodeClass: SetMaterialPropertyNode,
      icon: 'tune',
      color: '#2196F3',
      tags: ['material', 'property', 'set']
    });

    this.registerNode({
      type: GetMaterialPropertyNode.TYPE,
      name: GetMaterialPropertyNode.NAME,
      description: GetMaterialPropertyNode.DESCRIPTION,
      category: NodeCategory.RENDERING,
      nodeClass: GetMaterialPropertyNode,
      icon: 'info',
      color: '#2196F3',
      tags: ['material', 'property', 'get']
    });

    this.registerNode({
      type: MaterialBlendNode.TYPE,
      name: MaterialBlendNode.NAME,
      description: MaterialBlendNode.DESCRIPTION,
      category: NodeCategory.RENDERING,
      nodeClass: MaterialBlendNode,
      icon: 'blend_mode',
      color: '#2196F3',
      tags: ['material', 'blend', 'mix']
    });

    this.registerNode({
      type: MaterialAnimationNode.TYPE,
      name: MaterialAnimationNode.NAME,
      description: MaterialAnimationNode.DESCRIPTION,
      category: NodeCategory.RENDERING,
      nodeClass: MaterialAnimationNode,
      icon: 'animation',
      color: '#2196F3',
      tags: ['material', 'animation', 'tween']
    });

    // ==================== 批次1.2：场景管理节点 ====================
    this.registerNode({
      type: LoadSceneNode.TYPE,
      name: LoadSceneNode.NAME,
      description: LoadSceneNode.DESCRIPTION,
      category: NodeCategory.SCENE_MANAGEMENT,
      nodeClass: LoadSceneNode,
      icon: 'folder_open',
      color: '#4CAF50',
      tags: ['scene', 'load', 'file']
    });

    this.registerNode({
      type: SaveSceneNode.TYPE,
      name: SaveSceneNode.NAME,
      description: SaveSceneNode.DESCRIPTION,
      category: NodeCategory.SCENE_MANAGEMENT,
      nodeClass: SaveSceneNode,
      icon: 'save',
      color: '#4CAF50',
      tags: ['scene', 'save', 'file']
    });

    this.registerNode({
      type: CreateSceneNode.TYPE,
      name: CreateSceneNode.NAME,
      description: CreateSceneNode.DESCRIPTION,
      category: NodeCategory.SCENE_MANAGEMENT,
      nodeClass: CreateSceneNode,
      icon: 'add_box',
      color: '#4CAF50',
      tags: ['scene', 'create', 'new']
    });

    this.registerNode({
      type: DestroySceneNode.TYPE,
      name: DestroySceneNode.NAME,
      description: DestroySceneNode.DESCRIPTION,
      category: NodeCategory.SCENE_MANAGEMENT,
      nodeClass: DestroySceneNode,
      icon: 'delete',
      color: '#4CAF50',
      tags: ['scene', 'destroy', 'cleanup']
    });

    // ==================== 批次1.1：剩余材质管理节点 ====================
    this.registerNode({
      type: MaterialOptimizationNode.TYPE,
      name: MaterialOptimizationNode.NAME,
      description: MaterialOptimizationNode.DESCRIPTION,
      category: NodeCategory.RENDERING,
      nodeClass: MaterialOptimizationNode,
      icon: 'speed',
      color: '#2196F3',
      tags: ['material', 'optimization', 'performance']
    });

    this.registerNode({
      type: PBRMaterialNode.TYPE,
      name: PBRMaterialNode.NAME,
      description: PBRMaterialNode.DESCRIPTION,
      category: NodeCategory.RENDERING,
      nodeClass: PBRMaterialNode,
      icon: 'auto_awesome',
      color: '#2196F3',
      tags: ['material', 'pbr', 'physical']
    });

    this.registerNode({
      type: StandardMaterialNode.TYPE,
      name: StandardMaterialNode.NAME,
      description: StandardMaterialNode.DESCRIPTION,
      category: NodeCategory.RENDERING,
      nodeClass: StandardMaterialNode,
      icon: 'texture',
      color: '#2196F3',
      tags: ['material', 'standard', 'basic']
    });

    this.registerNode({
      type: CustomMaterialNode.TYPE,
      name: CustomMaterialNode.NAME,
      description: CustomMaterialNode.DESCRIPTION,
      category: NodeCategory.RENDERING,
      nodeClass: CustomMaterialNode,
      icon: 'code',
      color: '#2196F3',
      tags: ['material', 'custom', 'shader']
    });

    this.registerNode({
      type: MaterialPresetNode.TYPE,
      name: MaterialPresetNode.NAME,
      description: MaterialPresetNode.DESCRIPTION,
      category: NodeCategory.RENDERING,
      nodeClass: MaterialPresetNode,
      icon: 'library_books',
      color: '#2196F3',
      tags: ['material', 'preset', 'template']
    });

    // ==================== 批次1.1：光照控制节点 ====================
    this.registerNode({
      type: CreateLightNode.TYPE,
      name: CreateLightNode.NAME,
      description: CreateLightNode.DESCRIPTION,
      category: NodeCategory.RENDERING,
      nodeClass: CreateLightNode,
      icon: 'lightbulb',
      color: '#FFC107',
      tags: ['light', 'create', 'illumination']
    });

    this.registerNode({
      type: SetLightPropertyNode.TYPE,
      name: SetLightPropertyNode.NAME,
      description: SetLightPropertyNode.DESCRIPTION,
      category: NodeCategory.RENDERING,
      nodeClass: SetLightPropertyNode,
      icon: 'tune',
      color: '#FFC107',
      tags: ['light', 'property', 'set']
    });

    this.registerNode({
      type: LightAnimationNode.TYPE,
      name: LightAnimationNode.NAME,
      description: LightAnimationNode.DESCRIPTION,
      category: NodeCategory.RENDERING,
      nodeClass: LightAnimationNode,
      icon: 'animation',
      color: '#FFC107',
      tags: ['light', 'animation', 'tween']
    });

    // ==================== 批次1.1：相机管理节点 ====================
    this.registerNode({
      type: CreateCameraNode.TYPE,
      name: CreateCameraNode.NAME,
      description: CreateCameraNode.DESCRIPTION,
      category: NodeCategory.RENDERING,
      nodeClass: CreateCameraNode,
      icon: 'videocam',
      color: '#9C27B0',
      tags: ['camera', 'create', 'view']
    });

    // ==================== 批次1.2：剩余场景管理节点 ====================
    this.registerNode({
      type: AddObjectToSceneNode.TYPE,
      name: AddObjectToSceneNode.NAME,
      description: AddObjectToSceneNode.DESCRIPTION,
      category: NodeCategory.SCENE_MANAGEMENT,
      nodeClass: AddObjectToSceneNode,
      icon: 'add_circle',
      color: '#4CAF50',
      tags: ['scene', 'object', 'add']
    });

    this.registerNode({
      type: RemoveObjectFromSceneNode.TYPE,
      name: RemoveObjectFromSceneNode.NAME,
      description: RemoveObjectFromSceneNode.DESCRIPTION,
      category: NodeCategory.SCENE_MANAGEMENT,
      nodeClass: RemoveObjectFromSceneNode,
      icon: 'remove_circle',
      color: '#4CAF50',
      tags: ['scene', 'object', 'remove']
    });

    this.registerNode({
      type: FindSceneObjectNode.TYPE,
      name: FindSceneObjectNode.NAME,
      description: FindSceneObjectNode.DESCRIPTION,
      category: NodeCategory.SCENE_MANAGEMENT,
      nodeClass: FindSceneObjectNode,
      icon: 'search',
      color: '#4CAF50',
      tags: ['scene', 'object', 'find']
    });

    // ==================== 批次1.2：场景切换节点 ====================
    this.registerNode({
      type: SceneTransitionNode.TYPE,
      name: SceneTransitionNode.NAME,
      description: SceneTransitionNode.DESCRIPTION,
      category: NodeCategory.SCENE_MANAGEMENT,
      nodeClass: SceneTransitionNode,
      icon: 'swap_horiz',
      color: '#4CAF50',
      tags: ['scene', 'transition', 'switch']
    });

    // ==================== 批次1.3：资源加载节点 ====================
    this.registerNode({
      type: LoadAssetNode.TYPE,
      name: LoadAssetNode.NAME,
      description: LoadAssetNode.DESCRIPTION,
      category: NodeCategory.RESOURCE_MANAGEMENT,
      nodeClass: LoadAssetNode,
      icon: 'download',
      color: '#FF9800',
      tags: ['resource', 'load', 'asset']
    });

    this.registerNode({
      type: UnloadAssetNode.TYPE,
      name: UnloadAssetNode.NAME,
      description: UnloadAssetNode.DESCRIPTION,
      category: NodeCategory.RESOURCE_MANAGEMENT,
      nodeClass: UnloadAssetNode,
      icon: 'delete',
      color: '#FF9800',
      tags: ['resource', 'unload', 'release']
    });

    this.registerNode({
      type: PreloadAssetNode.TYPE,
      name: PreloadAssetNode.NAME,
      description: PreloadAssetNode.DESCRIPTION,
      category: NodeCategory.RESOURCE_MANAGEMENT,
      nodeClass: PreloadAssetNode,
      icon: 'cloud_download',
      color: '#FF9800',
      tags: ['resource', 'preload', 'cache']
    });

    this.registerNode({
      type: AsyncLoadAssetNode.TYPE,
      name: AsyncLoadAssetNode.NAME,
      description: AsyncLoadAssetNode.DESCRIPTION,
      category: NodeCategory.RESOURCE_MANAGEMENT,
      nodeClass: AsyncLoadAssetNode,
      icon: 'sync',
      color: '#FF9800',
      tags: ['resource', 'async', 'load']
    });

    this.registerNode({
      type: LoadAssetBundleNode.TYPE,
      name: LoadAssetBundleNode.NAME,
      description: LoadAssetBundleNode.DESCRIPTION,
      category: NodeCategory.RESOURCE_MANAGEMENT,
      nodeClass: LoadAssetBundleNode,
      icon: 'archive',
      color: '#FF9800',
      tags: ['resource', 'bundle', 'package']
    });

    this.registerNode({
      type: AssetDependencyNode.TYPE,
      name: AssetDependencyNode.NAME,
      description: AssetDependencyNode.DESCRIPTION,
      category: NodeCategory.RESOURCE_MANAGEMENT,
      nodeClass: AssetDependencyNode,
      icon: 'account_tree',
      color: '#FF9800',
      tags: ['resource', 'dependency', 'tree']
    });

    this.registerNode({
      type: AssetCacheNode.TYPE,
      name: AssetCacheNode.NAME,
      description: AssetCacheNode.DESCRIPTION,
      category: NodeCategory.RESOURCE_MANAGEMENT,
      nodeClass: AssetCacheNode,
      icon: 'storage',
      color: '#FF9800',
      tags: ['resource', 'cache', 'memory']
    });

    this.registerNode({
      type: AssetCompressionNode.TYPE,
      name: AssetCompressionNode.NAME,
      description: AssetCompressionNode.DESCRIPTION,
      category: NodeCategory.RESOURCE_MANAGEMENT,
      nodeClass: AssetCompressionNode,
      icon: 'compress',
      color: '#FF9800',
      tags: ['resource', 'compression', 'optimize']
    });

    this.registerNode({
      type: AssetEncryptionNode.TYPE,
      name: AssetEncryptionNode.NAME,
      description: AssetEncryptionNode.DESCRIPTION,
      category: NodeCategory.RESOURCE_MANAGEMENT,
      nodeClass: AssetEncryptionNode,
      icon: 'lock',
      color: '#FF9800',
      tags: ['resource', 'encryption', 'security']
    });

    this.registerNode({
      type: AssetValidationNode.TYPE,
      name: AssetValidationNode.NAME,
      description: AssetValidationNode.DESCRIPTION,
      category: NodeCategory.RESOURCE_MANAGEMENT,
      nodeClass: AssetValidationNode,
      icon: 'verified',
      color: '#FF9800',
      tags: ['resource', 'validation', 'check']
    });

    this.registerNode({
      type: AssetMetadataNode.TYPE,
      name: AssetMetadataNode.NAME,
      description: AssetMetadataNode.DESCRIPTION,
      category: NodeCategory.RESOURCE_MANAGEMENT,
      nodeClass: AssetMetadataNode,
      icon: 'info',
      color: '#FF9800',
      tags: ['resource', 'metadata', 'info']
    });

    this.registerNode({
      type: AssetVersionNode.TYPE,
      name: AssetVersionNode.NAME,
      description: AssetVersionNode.DESCRIPTION,
      category: NodeCategory.RESOURCE_MANAGEMENT,
      nodeClass: AssetVersionNode,
      icon: 'history',
      color: '#FF9800',
      tags: ['resource', 'version', 'control']
    });

    // ==================== 批次1.3：资源优化节点 ====================
    this.registerNode({
      type: AssetOptimizationNode.TYPE,
      name: AssetOptimizationNode.NAME,
      description: AssetOptimizationNode.DESCRIPTION,
      category: NodeCategory.RESOURCE_OPTIMIZATION,
      nodeClass: AssetOptimizationNode,
      icon: 'tune',
      color: '#795548',
      tags: ['resource', 'optimization', 'performance']
    });

    this.registerNode({
      type: TextureCompressionNode.TYPE,
      name: TextureCompressionNode.NAME,
      description: TextureCompressionNode.DESCRIPTION,
      category: NodeCategory.RESOURCE_OPTIMIZATION,
      nodeClass: TextureCompressionNode,
      icon: 'image',
      color: '#795548',
      tags: ['texture', 'compression', 'optimize']
    });

    this.registerNode({
      type: MeshOptimizationNode.TYPE,
      name: MeshOptimizationNode.NAME,
      description: MeshOptimizationNode.DESCRIPTION,
      category: NodeCategory.RESOURCE_OPTIMIZATION,
      nodeClass: MeshOptimizationNode,
      icon: '3d_rotation',
      color: '#795548',
      tags: ['mesh', 'optimization', '3d']
    });

    this.registerNode({
      type: AudioCompressionNode.TYPE,
      name: AudioCompressionNode.NAME,
      description: AudioCompressionNode.DESCRIPTION,
      category: NodeCategory.RESOURCE_OPTIMIZATION,
      nodeClass: AudioCompressionNode,
      icon: 'audiotrack',
      color: '#795548',
      tags: ['audio', 'compression', 'sound']
    });

    this.registerNode({
      type: AssetBatchingNode.TYPE,
      name: AssetBatchingNode.NAME,
      description: AssetBatchingNode.DESCRIPTION,
      category: NodeCategory.RESOURCE_OPTIMIZATION,
      nodeClass: AssetBatchingNode,
      icon: 'batch_prediction',
      color: '#795548',
      tags: ['resource', 'batch', 'processing']
    });

    this.registerNode({
      type: AssetStreamingNode.TYPE,
      name: AssetStreamingNode.NAME,
      description: AssetStreamingNode.DESCRIPTION,
      category: NodeCategory.RESOURCE_OPTIMIZATION,
      nodeClass: AssetStreamingNode,
      icon: 'stream',
      color: '#795548',
      tags: ['resource', 'streaming', 'network']
    });

    this.registerNode({
      type: AssetMemoryManagementNode.TYPE,
      name: AssetMemoryManagementNode.NAME,
      description: AssetMemoryManagementNode.DESCRIPTION,
      category: NodeCategory.RESOURCE_OPTIMIZATION,
      nodeClass: AssetMemoryManagementNode,
      icon: 'memory',
      color: '#795548',
      tags: ['resource', 'memory', 'management']
    });

    this.registerNode({
      type: AssetGarbageCollectionNode.TYPE,
      name: AssetGarbageCollectionNode.NAME,
      description: AssetGarbageCollectionNode.DESCRIPTION,
      category: NodeCategory.RESOURCE_OPTIMIZATION,
      nodeClass: AssetGarbageCollectionNode,
      icon: 'delete_sweep',
      color: '#795548',
      tags: ['resource', 'garbage', 'cleanup']
    });

    this.registerNode({
      type: AssetPerformanceMonitorNode.TYPE,
      name: AssetPerformanceMonitorNode.NAME,
      description: AssetPerformanceMonitorNode.DESCRIPTION,
      category: NodeCategory.RESOURCE_OPTIMIZATION,
      nodeClass: AssetPerformanceMonitorNode,
      icon: 'monitor',
      color: '#795548',
      tags: ['resource', 'performance', 'monitor']
    });

    this.registerNode({
      type: AssetUsageAnalyticsNode.TYPE,
      name: AssetUsageAnalyticsNode.NAME,
      description: AssetUsageAnalyticsNode.DESCRIPTION,
      category: NodeCategory.RESOURCE_OPTIMIZATION,
      nodeClass: AssetUsageAnalyticsNode,
      icon: 'analytics',
      color: '#795548',
      tags: ['resource', 'analytics', 'usage']
    });

    // 批次1.5：物理系统增强节点
    this.registerNode({
      type: SoftBodyPhysicsNode.TYPE,
      name: SoftBodyPhysicsNode.NAME,
      description: SoftBodyPhysicsNode.DESCRIPTION,
      category: NodeCategory.PHYSICS,
      nodeClass: SoftBodyPhysicsNode,
      icon: 'waves',
      color: '#4CAF50',
      tags: ['physics', 'soft', 'body']
    });

    this.registerNode({
      type: FluidSimulationNode.TYPE,
      name: FluidSimulationNode.NAME,
      description: FluidSimulationNode.DESCRIPTION,
      category: NodeCategory.PHYSICS,
      nodeClass: FluidSimulationNode,
      icon: 'water_drop',
      color: '#2196F3',
      tags: ['physics', 'fluid', 'simulation']
    });

    this.registerNode({
      type: ClothSimulationNode.TYPE,
      name: ClothSimulationNode.NAME,
      description: ClothSimulationNode.DESCRIPTION,
      category: NodeCategory.PHYSICS,
      nodeClass: ClothSimulationNode,
      icon: 'texture',
      color: '#9C27B0',
      tags: ['physics', 'cloth', 'fabric']
    });

    this.registerNode({
      type: RopeSimulationNode.TYPE,
      name: RopeSimulationNode.NAME,
      description: RopeSimulationNode.DESCRIPTION,
      category: NodeCategory.PHYSICS,
      nodeClass: RopeSimulationNode,
      icon: 'linear_scale',
      color: '#795548',
      tags: ['physics', 'rope', 'constraint']
    });

    this.registerNode({
      type: DestructionNode.TYPE,
      name: DestructionNode.NAME,
      description: DestructionNode.DESCRIPTION,
      category: NodeCategory.PHYSICS,
      nodeClass: DestructionNode,
      icon: 'broken_image',
      color: '#F44336',
      tags: ['physics', 'destruction', 'fracture']
    });

    this.registerNode({
      type: PhysicsConstraintNode.TYPE,
      name: PhysicsConstraintNode.NAME,
      description: PhysicsConstraintNode.DESCRIPTION,
      category: NodeCategory.PHYSICS,
      nodeClass: PhysicsConstraintNode,
      icon: 'link',
      color: '#607D8B',
      tags: ['physics', 'constraint', 'joint']
    });

    this.registerNode({
      type: PhysicsOptimizationNode.TYPE,
      name: PhysicsOptimizationNode.NAME,
      description: PhysicsOptimizationNode.DESCRIPTION,
      category: NodeCategory.PHYSICS,
      nodeClass: PhysicsOptimizationNode,
      icon: 'speed',
      color: '#FF9800',
      tags: ['physics', 'optimization', 'performance']
    });

    this.registerNode({
      type: PhysicsLODNode.TYPE,
      name: PhysicsLODNode.NAME,
      description: PhysicsLODNode.DESCRIPTION,
      category: NodeCategory.PHYSICS,
      nodeClass: PhysicsLODNode,
      icon: 'layers',
      color: '#FF9800',
      tags: ['physics', 'lod', 'optimization']
    });

    this.registerNode({
      type: PhysicsPerformanceMonitorNode.TYPE,
      name: PhysicsPerformanceMonitorNode.NAME,
      description: PhysicsPerformanceMonitorNode.DESCRIPTION,
      category: NodeCategory.PHYSICS,
      nodeClass: PhysicsPerformanceMonitorNode,
      icon: 'monitor_heart',
      color: '#FF9800',
      tags: ['physics', 'performance', 'monitor']
    });

    this.registerNode({
      type: PhysicsBatchingNode.TYPE,
      name: PhysicsBatchingNode.NAME,
      description: PhysicsBatchingNode.DESCRIPTION,
      category: NodeCategory.PHYSICS,
      nodeClass: PhysicsBatchingNode,
      icon: 'batch_prediction',
      color: '#FF9800',
      tags: ['physics', 'batching', 'optimization']
    });

    // 批次1.6：音频系统增强节点
    this.registerNode({
      type: AudioMixerNode.TYPE,
      name: AudioMixerNode.NAME,
      description: AudioMixerNode.DESCRIPTION,
      category: NodeCategory.AUDIO,
      nodeClass: AudioMixerNode,
      icon: 'tune',
      color: '#E91E63',
      tags: ['audio', 'mixer', 'channel']
    });

    this.registerNode({
      type: AudioEffectChainNode.TYPE,
      name: AudioEffectChainNode.NAME,
      description: AudioEffectChainNode.DESCRIPTION,
      category: NodeCategory.AUDIO,
      nodeClass: AudioEffectChainNode,
      icon: 'link',
      color: '#E91E63',
      tags: ['audio', 'effect', 'chain']
    });

    this.registerNode({
      type: AudioReverbNode.TYPE,
      name: AudioReverbNode.NAME,
      description: AudioReverbNode.DESCRIPTION,
      category: NodeCategory.AUDIO,
      nodeClass: AudioReverbNode,
      icon: 'surround_sound',
      color: '#E91E63',
      tags: ['audio', 'reverb', 'space']
    });

    this.registerNode({
      type: AudioEQNode.TYPE,
      name: AudioEQNode.NAME,
      description: AudioEQNode.DESCRIPTION,
      category: NodeCategory.AUDIO,
      nodeClass: AudioEQNode,
      icon: 'equalizer',
      color: '#E91E63',
      tags: ['audio', 'equalizer', 'frequency']
    });

    this.registerNode({
      type: AudioOptimizationNode.TYPE,
      name: AudioOptimizationNode.NAME,
      description: AudioOptimizationNode.DESCRIPTION,
      category: NodeCategory.AUDIO,
      nodeClass: AudioOptimizationNode,
      icon: 'speed',
      color: '#E91E63',
      tags: ['audio', 'optimization', 'performance']
    });

    this.registerNode({
      type: AudioStreamingNode.TYPE,
      name: AudioStreamingNode.NAME,
      description: AudioStreamingNode.DESCRIPTION,
      category: NodeCategory.AUDIO,
      nodeClass: AudioStreamingNode,
      icon: 'stream',
      color: '#E91E63',
      tags: ['audio', 'streaming', 'network']
    });

    // 项目管理节点
    this.registerNode({
      type: CreateProjectNode.TYPE,
      name: CreateProjectNode.NAME,
      description: CreateProjectNode.DESCRIPTION,
      category: NodeCategory.PROJECT_MANAGEMENT,
      nodeClass: CreateProjectNode,
      icon: 'create_new_folder',
      color: '#2196F3',
      tags: ['project', 'create', 'management']
    });

    this.registerNode({
      type: LoadProjectNode.TYPE,
      name: LoadProjectNode.NAME,
      description: LoadProjectNode.DESCRIPTION,
      category: NodeCategory.PROJECT_MANAGEMENT,
      nodeClass: LoadProjectNode,
      icon: 'folder_open',
      color: '#2196F3',
      tags: ['project', 'load', 'management']
    });

    this.registerNode({
      type: SaveProjectNode.TYPE,
      name: SaveProjectNode.NAME,
      description: SaveProjectNode.DESCRIPTION,
      category: NodeCategory.PROJECT_MANAGEMENT,
      nodeClass: SaveProjectNode,
      icon: 'save',
      color: '#2196F3',
      tags: ['project', 'save', 'management']
    });

    this.registerNode({
      type: ProjectVersionNode.TYPE,
      name: ProjectVersionNode.NAME,
      description: ProjectVersionNode.DESCRIPTION,
      category: NodeCategory.PROJECT_MANAGEMENT,
      nodeClass: ProjectVersionNode,
      icon: 'history',
      color: '#2196F3',
      tags: ['project', 'version', 'control']
    });

    this.registerNode({
      type: ProjectCollaborationNode.TYPE,
      name: ProjectCollaborationNode.NAME,
      description: ProjectCollaborationNode.DESCRIPTION,
      category: NodeCategory.PROJECT_MANAGEMENT,
      nodeClass: ProjectCollaborationNode,
      icon: 'group',
      color: '#2196F3',
      tags: ['project', 'collaboration', 'team']
    });

    this.registerNode({
      type: ProjectPermissionNode.TYPE,
      name: ProjectPermissionNode.NAME,
      description: ProjectPermissionNode.DESCRIPTION,
      category: NodeCategory.PROJECT_MANAGEMENT,
      nodeClass: ProjectPermissionNode,
      icon: 'security',
      color: '#2196F3',
      tags: ['project', 'permission', 'security']
    });

    this.registerNode({
      type: ProjectBackupNode.TYPE,
      name: ProjectBackupNode.NAME,
      description: ProjectBackupNode.DESCRIPTION,
      category: NodeCategory.PROJECT_MANAGEMENT,
      nodeClass: ProjectBackupNode,
      icon: 'backup',
      color: '#2196F3',
      tags: ['project', 'backup', 'restore']
    });

    this.registerNode({
      type: ProjectAnalyticsNode.TYPE,
      name: ProjectAnalyticsNode.NAME,
      description: ProjectAnalyticsNode.DESCRIPTION,
      category: NodeCategory.PROJECT_MANAGEMENT,
      nodeClass: ProjectAnalyticsNode,
      icon: 'analytics',
      color: '#2196F3',
      tags: ['project', 'analytics', 'metrics']
    });

    this.registerNode({
      type: ProjectTemplateNode.TYPE,
      name: ProjectTemplateNode.NAME,
      description: ProjectTemplateNode.DESCRIPTION,
      category: NodeCategory.PROJECT_MANAGEMENT,
      nodeClass: ProjectTemplateNode,
      icon: 'template',
      color: '#2196F3',
      tags: ['project', 'template', 'preset']
    });

    this.registerNode({
      type: ProjectExportNode.TYPE,
      name: ProjectExportNode.NAME,
      description: ProjectExportNode.DESCRIPTION,
      category: NodeCategory.PROJECT_MANAGEMENT,
      nodeClass: ProjectExportNode,
      icon: 'file_download',
      color: '#2196F3',
      tags: ['project', 'export', 'download']
    });

    // AI服务节点
    this.registerNode({
      type: AIModelLoadNode.TYPE,
      name: AIModelLoadNode.NAME,
      description: AIModelLoadNode.DESCRIPTION,
      category: NodeCategory.AI_SERVICE,
      nodeClass: AIModelLoadNode,
      icon: 'model_training',
      color: '#FF9800',
      tags: ['ai', 'model', 'load']
    });

    this.registerNode({
      type: AIInferenceNode.TYPE,
      name: AIInferenceNode.NAME,
      description: AIInferenceNode.DESCRIPTION,
      category: NodeCategory.AI_SERVICE,
      nodeClass: AIInferenceNode,
      icon: 'psychology',
      color: '#FF9800',
      tags: ['ai', 'inference', 'prediction']
    });

    this.registerNode({
      type: AITrainingNode.TYPE,
      name: AITrainingNode.NAME,
      description: AITrainingNode.DESCRIPTION,
      category: NodeCategory.AI_SERVICE,
      nodeClass: AITrainingNode,
      icon: 'school',
      color: '#FF9800',
      tags: ['ai', 'training', 'learning']
    });

    this.registerNode({
      type: NLPProcessingNode.TYPE,
      name: NLPProcessingNode.NAME,
      description: NLPProcessingNode.DESCRIPTION,
      category: NodeCategory.AI_SERVICE,
      nodeClass: NLPProcessingNode,
      icon: 'translate',
      color: '#FF9800',
      tags: ['ai', 'nlp', 'text']
    });

    this.registerNode({
      type: ComputerVisionNode.TYPE,
      name: ComputerVisionNode.NAME,
      description: ComputerVisionNode.DESCRIPTION,
      category: NodeCategory.AI_SERVICE,
      nodeClass: ComputerVisionNode,
      icon: 'visibility',
      color: '#FF9800',
      tags: ['ai', 'vision', 'image']
    });

    this.registerNode({
      type: SpeechRecognitionNode.TYPE,
      name: SpeechRecognitionNode.NAME,
      description: SpeechRecognitionNode.DESCRIPTION,
      category: NodeCategory.AI_SERVICE,
      nodeClass: SpeechRecognitionNode,
      icon: 'mic',
      color: '#FF9800',
      tags: ['ai', 'speech', 'recognition']
    });

    this.registerNode({
      type: SentimentAnalysisNode.TYPE,
      name: SentimentAnalysisNode.NAME,
      description: SentimentAnalysisNode.DESCRIPTION,
      category: NodeCategory.AI_SERVICE,
      nodeClass: SentimentAnalysisNode,
      icon: 'sentiment_satisfied',
      color: '#FF9800',
      tags: ['ai', 'sentiment', 'emotion']
    });

    this.registerNode({
      type: RecommendationNode.TYPE,
      name: RecommendationNode.NAME,
      description: RecommendationNode.DESCRIPTION,
      category: NodeCategory.AI_SERVICE,
      nodeClass: RecommendationNode,
      icon: 'recommend',
      color: '#FF9800',
      tags: ['ai', 'recommendation', 'suggest']
    });

    this.registerNode({
      type: ChatbotNode.TYPE,
      name: ChatbotNode.NAME,
      description: ChatbotNode.DESCRIPTION,
      category: NodeCategory.AI_SERVICE,
      nodeClass: ChatbotNode,
      icon: 'chat',
      color: '#FF9800',
      tags: ['ai', 'chatbot', 'conversation']
    });

    this.registerNode({
      type: AIOptimizationNode.TYPE,
      name: AIOptimizationNode.NAME,
      description: AIOptimizationNode.DESCRIPTION,
      category: NodeCategory.AI_SERVICE,
      nodeClass: AIOptimizationNode,
      icon: 'tune',
      color: '#FF9800',
      tags: ['ai', 'optimization', 'performance']
    });

    this.registerNode({
      type: AIMonitoringNode.TYPE,
      name: AIMonitoringNode.NAME,
      description: AIMonitoringNode.DESCRIPTION,
      category: NodeCategory.AI_SERVICE,
      nodeClass: AIMonitoringNode,
      icon: 'monitor_heart',
      color: '#FF9800',
      tags: ['ai', 'monitoring', 'metrics']
    });

    this.registerNode({
      type: AIModelVersionNode.TYPE,
      name: AIModelVersionNode.NAME,
      description: AIModelVersionNode.DESCRIPTION,
      category: NodeCategory.AI_SERVICE,
      nodeClass: AIModelVersionNode,
      icon: 'history_toggle_off',
      color: '#FF9800',
      tags: ['ai', 'model', 'version']
    });

    this.registerNode({
      type: AIDataPreprocessingNode.TYPE,
      name: AIDataPreprocessingNode.NAME,
      description: AIDataPreprocessingNode.DESCRIPTION,
      category: NodeCategory.AI_SERVICE,
      nodeClass: AIDataPreprocessingNode,
      icon: 'data_usage',
      color: '#FF9800',
      tags: ['ai', 'data', 'preprocessing']
    });

    this.registerNode({
      type: AIResultPostprocessingNode.TYPE,
      name: AIResultPostprocessingNode.NAME,
      description: AIResultPostprocessingNode.DESCRIPTION,
      category: NodeCategory.AI_SERVICE,
      nodeClass: AIResultPostprocessingNode,
      icon: 'post_add',
      color: '#FF9800',
      tags: ['ai', 'result', 'postprocessing']
    });

    this.registerNode({
      type: AIPerformanceNode.TYPE,
      name: AIPerformanceNode.NAME,
      description: AIPerformanceNode.DESCRIPTION,
      category: NodeCategory.AI_SERVICE,
      nodeClass: AIPerformanceNode,
      icon: 'speed',
      color: '#FF9800',
      tags: ['ai', 'performance', 'monitoring']
    });

    // 批次2.1 - 数据服务节点
    this.registerNode({
      type: DatabaseConnectionNode.TYPE,
      name: DatabaseConnectionNode.NAME,
      description: DatabaseConnectionNode.DESCRIPTION,
      category: NodeCategory.DATA_SERVICE,
      nodeClass: DatabaseConnectionNode,
      icon: 'storage',
      color: '#4CAF50',
      tags: ['database', 'connection', 'data']
    });

    this.registerNode({
      type: DatabaseQueryNode.TYPE,
      name: DatabaseQueryNode.NAME,
      description: DatabaseQueryNode.DESCRIPTION,
      category: NodeCategory.DATA_SERVICE,
      nodeClass: DatabaseQueryNode,
      icon: 'search',
      color: '#4CAF50',
      tags: ['database', 'query', 'sql']
    });

    this.registerNode({
      type: DatabaseInsertNode.TYPE,
      name: DatabaseInsertNode.NAME,
      description: DatabaseInsertNode.DESCRIPTION,
      category: NodeCategory.DATA_SERVICE,
      nodeClass: DatabaseInsertNode,
      icon: 'add',
      color: '#4CAF50',
      tags: ['database', 'insert', 'data']
    });

    this.registerNode({
      type: DatabaseUpdateNode.TYPE,
      name: DatabaseUpdateNode.NAME,
      description: DatabaseUpdateNode.DESCRIPTION,
      category: NodeCategory.DATA_SERVICE,
      nodeClass: DatabaseUpdateNode,
      icon: 'edit',
      color: '#4CAF50',
      tags: ['database', 'update', 'data']
    });

    this.registerNode({
      type: DatabaseDeleteNode.TYPE,
      name: DatabaseDeleteNode.NAME,
      description: DatabaseDeleteNode.DESCRIPTION,
      category: NodeCategory.DATA_SERVICE,
      nodeClass: DatabaseDeleteNode,
      icon: 'delete',
      color: '#4CAF50',
      tags: ['database', 'delete', 'data']
    });

    this.registerNode({
      type: DatabaseTransactionNode.TYPE,
      name: DatabaseTransactionNode.NAME,
      description: DatabaseTransactionNode.DESCRIPTION,
      category: NodeCategory.DATA_SERVICE,
      nodeClass: DatabaseTransactionNode,
      icon: 'swap_horiz',
      color: '#4CAF50',
      tags: ['database', 'transaction', 'acid']
    });

    this.registerNode({
      type: DataValidationNode.TYPE,
      name: DataValidationNode.NAME,
      description: DataValidationNode.DESCRIPTION,
      category: NodeCategory.DATA_SERVICE,
      nodeClass: DataValidationNode,
      icon: 'verified',
      color: '#4CAF50',
      tags: ['data', 'validation', 'verify']
    });

    this.registerNode({
      type: DataTransformationNode.TYPE,
      name: DataTransformationNode.NAME,
      description: DataTransformationNode.DESCRIPTION,
      category: NodeCategory.DATA_SERVICE,
      nodeClass: DataTransformationNode,
      icon: 'transform',
      color: '#4CAF50',
      tags: ['data', 'transformation', 'convert']
    });

    this.registerNode({
      type: DataAggregationNode.TYPE,
      name: DataAggregationNode.NAME,
      description: DataAggregationNode.DESCRIPTION,
      category: NodeCategory.DATA_SERVICE,
      nodeClass: DataAggregationNode,
      icon: 'analytics',
      color: '#4CAF50',
      tags: ['data', 'aggregation', 'statistics']
    });

    this.registerNode({
      type: DataBackupNode.TYPE,
      name: DataBackupNode.NAME,
      description: DataBackupNode.DESCRIPTION,
      category: NodeCategory.DATA_SERVICE,
      nodeClass: DataBackupNode,
      icon: 'backup',
      color: '#4CAF50',
      tags: ['data', 'backup', 'archive']
    });

    this.registerNode({
      type: DataSyncNode.TYPE,
      name: DataSyncNode.NAME,
      description: DataSyncNode.DESCRIPTION,
      category: NodeCategory.DATA_SERVICE,
      nodeClass: DataSyncNode,
      icon: 'sync',
      color: '#4CAF50',
      tags: ['data', 'sync', 'replication']
    });

    this.registerNode({
      type: DataAnalyticsNode.TYPE,
      name: DataAnalyticsNode.NAME,
      description: DataAnalyticsNode.DESCRIPTION,
      category: NodeCategory.DATA_SERVICE,
      nodeClass: DataAnalyticsNode,
      icon: 'insights',
      color: '#4CAF50',
      tags: ['data', 'analytics', 'analysis']
    });

    // 批次2.1 - 认证授权节点
    this.registerNode({
      type: JWTTokenNode.TYPE,
      name: JWTTokenNode.NAME,
      description: JWTTokenNode.DESCRIPTION,
      category: NodeCategory.AUTHENTICATION,
      nodeClass: JWTTokenNode,
      icon: 'token',
      color: '#FF5722',
      tags: ['jwt', 'token', 'authentication']
    });

    this.registerNode({
      type: OAuth2Node.TYPE,
      name: OAuth2Node.NAME,
      description: OAuth2Node.DESCRIPTION,
      category: NodeCategory.AUTHENTICATION,
      nodeClass: OAuth2Node,
      icon: 'verified_user',
      color: '#FF5722',
      tags: ['oauth2', 'authentication', 'authorization']
    });

    this.registerNode({
      type: RBACNode.TYPE,
      name: RBACNode.NAME,
      description: RBACNode.DESCRIPTION,
      category: NodeCategory.AUTHENTICATION,
      nodeClass: RBACNode,
      icon: 'admin_panel_settings',
      color: '#FF5722',
      tags: ['rbac', 'role', 'permission']
    });

    this.registerNode({
      type: PermissionCheckNode.TYPE,
      name: PermissionCheckNode.NAME,
      description: PermissionCheckNode.DESCRIPTION,
      category: NodeCategory.AUTHENTICATION,
      nodeClass: PermissionCheckNode,
      icon: 'check_circle',
      color: '#FF5722',
      tags: ['permission', 'check', 'authorization']
    });

    this.registerNode({
      type: SecurityAuditNode.TYPE,
      name: SecurityAuditNode.NAME,
      description: SecurityAuditNode.DESCRIPTION,
      category: NodeCategory.AUTHENTICATION,
      nodeClass: SecurityAuditNode,
      icon: 'security',
      color: '#FF5722',
      tags: ['security', 'audit', 'logging']
    });

    this.registerNode({
      type: EncryptionNode.TYPE,
      name: EncryptionNode.NAME,
      description: EncryptionNode.DESCRIPTION,
      category: NodeCategory.AUTHENTICATION,
      nodeClass: EncryptionNode,
      icon: 'lock',
      color: '#FF5722',
      tags: ['encryption', 'security', 'crypto']
    });

    this.registerNode({
      type: DecryptionNode.TYPE,
      name: DecryptionNode.NAME,
      description: DecryptionNode.DESCRIPTION,
      category: NodeCategory.AUTHENTICATION,
      nodeClass: DecryptionNode,
      icon: 'lock_open',
      color: '#FF5722',
      tags: ['decryption', 'security', 'crypto']
    });

    this.registerNode({
      type: SecurityMonitoringNode.TYPE,
      name: SecurityMonitoringNode.NAME,
      description: SecurityMonitoringNode.DESCRIPTION,
      category: NodeCategory.AUTHENTICATION,
      nodeClass: SecurityMonitoringNode,
      icon: 'monitor_heart',
      color: '#FF5722',
      tags: ['security', 'monitoring', 'threat']
    });

    // 批次2.1 - 文件服务节点
    this.registerNode({
      type: FileUploadNode.TYPE,
      name: FileUploadNode.NAME,
      description: FileUploadNode.DESCRIPTION,
      category: NodeCategory.FILE_SERVICE,
      nodeClass: FileUploadNode,
      icon: 'upload',
      color: '#2196F3',
      tags: ['file', 'upload', 'storage']
    });

    this.registerNode({
      type: FileDownloadNode.TYPE,
      name: FileDownloadNode.NAME,
      description: FileDownloadNode.DESCRIPTION,
      category: NodeCategory.FILE_SERVICE,
      nodeClass: FileDownloadNode,
      icon: 'download',
      color: '#2196F3',
      tags: ['file', 'download', 'storage']
    });

    this.registerNode({
      type: FileStorageNode.TYPE,
      name: FileStorageNode.NAME,
      description: FileStorageNode.DESCRIPTION,
      category: NodeCategory.FILE_SERVICE,
      nodeClass: FileStorageNode,
      icon: 'folder',
      color: '#2196F3',
      tags: ['file', 'storage', 'management']
    });

    this.registerNode({
      type: FileCompressionNode.TYPE,
      name: FileCompressionNode.NAME,
      description: FileCompressionNode.DESCRIPTION,
      category: NodeCategory.FILE_SERVICE,
      nodeClass: FileCompressionNode,
      icon: 'compress',
      color: '#2196F3',
      tags: ['file', 'compression', 'archive']
    });

    this.registerNode({
      type: FileEncryptionNode.TYPE,
      name: FileEncryptionNode.NAME,
      description: FileEncryptionNode.DESCRIPTION,
      category: NodeCategory.FILE_SERVICE,
      nodeClass: FileEncryptionNode,
      icon: 'enhanced_encryption',
      color: '#2196F3',
      tags: ['file', 'encryption', 'security']
    });

    this.registerNode({
      type: FileVersioningNode.TYPE,
      name: FileVersioningNode.NAME,
      description: FileVersioningNode.DESCRIPTION,
      category: NodeCategory.FILE_SERVICE,
      nodeClass: FileVersioningNode,
      icon: 'history',
      color: '#2196F3',
      tags: ['file', 'version', 'control']
    });

    this.registerNode({
      type: FileMetadataNode.TYPE,
      name: FileMetadataNode.NAME,
      description: FileMetadataNode.DESCRIPTION,
      category: NodeCategory.FILE_SERVICE,
      nodeClass: FileMetadataNode,
      icon: 'info',
      color: '#2196F3',
      tags: ['file', 'metadata', 'properties']
    });

    this.registerNode({
      type: FileSearchNode.TYPE,
      name: FileSearchNode.NAME,
      description: FileSearchNode.DESCRIPTION,
      category: NodeCategory.FILE_SERVICE,
      nodeClass: FileSearchNode,
      icon: 'search',
      color: '#2196F3',
      tags: ['file', 'search', 'find']
    });

    this.registerNode({
      type: FileSyncNode.TYPE,
      name: FileSyncNode.NAME,
      description: FileSyncNode.DESCRIPTION,
      category: NodeCategory.FILE_SERVICE,
      nodeClass: FileSyncNode,
      icon: 'sync',
      color: '#2196F3',
      tags: ['file', 'sync', 'synchronization']
    });

    this.registerNode({
      type: FileAnalyticsNode.TYPE,
      name: FileAnalyticsNode.NAME,
      description: FileAnalyticsNode.DESCRIPTION,
      category: NodeCategory.FILE_SERVICE,
      nodeClass: FileAnalyticsNode,
      icon: 'analytics',
      color: '#2196F3',
      tags: ['file', 'analytics', 'statistics']
    });

    // 注册批次2.2渲染优化和着色器节点
    this.registerRenderingNodes();

    // 注册批次0.1渲染系统集成节点
    this.registerBatch01RenderingNodes();

    // 注册批次0.1场景管理和资源管理节点
    this.registerBatch01Nodes();

    // 注册批次2.3工业制造节点
    this.registerIndustrialNodes();

    // 注册批次3.4节点
    this.registerBatch34Nodes();
  }

  /**
   * 注册单个节点
   */
  private registerNode(nodeInfo: NodeInfo): void {
    this.nodes.set(nodeInfo.type, nodeInfo);
  }

  /**
   * 注册渲染优化和着色器节点
   */
  private registerRenderingNodes(): void {
    // 批次2.2：后处理效果节点（15个）
    this.registerNode({
      type: BloomEffectNode.TYPE,
      name: BloomEffectNode.NAME,
      description: BloomEffectNode.DESCRIPTION,
      category: NodeCategory.RENDERING,
      nodeClass: BloomEffectNode,
      icon: 'flare',
      color: '#FFD700',
      tags: ['postprocess', 'bloom', 'glow', 'effect']
    });

    this.registerNode({
      type: BlurEffectNode.TYPE,
      name: BlurEffectNode.NAME,
      description: BlurEffectNode.DESCRIPTION,
      category: NodeCategory.RENDERING,
      nodeClass: BlurEffectNode,
      icon: 'blur_on',
      color: '#FFD700',
      tags: ['postprocess', 'blur', 'effect', 'filter']
    });

    this.registerNode({
      type: ColorGradingNode.TYPE,
      name: ColorGradingNode.NAME,
      description: ColorGradingNode.DESCRIPTION,
      category: NodeCategory.RENDERING,
      nodeClass: ColorGradingNode,
      icon: 'palette',
      color: '#FFD700',
      tags: ['postprocess', 'color', 'grading', 'correction']
    });

    this.registerNode({
      type: ToneMappingNode.TYPE,
      name: ToneMappingNode.NAME,
      description: ToneMappingNode.DESCRIPTION,
      category: NodeCategory.RENDERING,
      nodeClass: ToneMappingNode,
      icon: 'tonality',
      color: '#FFD700',
      tags: ['postprocess', 'tone', 'mapping', 'hdr']
    });

    this.registerNode({
      type: SSAONode.TYPE,
      name: SSAONode.NAME,
      description: SSAONode.DESCRIPTION,
      category: NodeCategory.RENDERING,
      nodeClass: SSAONode,
      icon: 'gradient',
      color: '#FFD700',
      tags: ['postprocess', 'ssao', 'occlusion', 'ambient']
    });

    this.registerNode({
      type: SSRNode.TYPE,
      name: SSRNode.NAME,
      description: SSRNode.DESCRIPTION,
      category: NodeCategory.RENDERING,
      nodeClass: SSRNode,
      icon: 'mirror',
      color: '#FFD700',
      tags: ['postprocess', 'ssr', 'reflection', 'screen']
    });

    this.registerNode({
      type: MotionBlurNode.TYPE,
      name: MotionBlurNode.NAME,
      description: MotionBlurNode.DESCRIPTION,
      category: NodeCategory.RENDERING,
      nodeClass: MotionBlurNode,
      icon: 'motion_photos_on',
      color: '#FFD700',
      tags: ['postprocess', 'motion', 'blur', 'movement']
    });

    this.registerNode({
      type: DepthOfFieldNode.TYPE,
      name: DepthOfFieldNode.NAME,
      description: DepthOfFieldNode.DESCRIPTION,
      category: NodeCategory.RENDERING,
      nodeClass: DepthOfFieldNode,
      icon: 'camera_alt',
      color: '#FFD700',
      tags: ['postprocess', 'dof', 'depth', 'focus']
    });

    this.registerNode({
      type: FilmGrainNode.TYPE,
      name: FilmGrainNode.NAME,
      description: FilmGrainNode.DESCRIPTION,
      category: NodeCategory.RENDERING,
      nodeClass: FilmGrainNode,
      icon: 'grain',
      color: '#FFD700',
      tags: ['postprocess', 'film', 'grain', 'noise']
    });

    this.registerNode({
      type: VignetteNode.TYPE,
      name: VignetteNode.NAME,
      description: VignetteNode.DESCRIPTION,
      category: NodeCategory.RENDERING,
      nodeClass: VignetteNode,
      icon: 'vignette',
      color: '#FFD700',
      tags: ['postprocess', 'vignette', 'darkening', 'edge']
    });

    this.registerNode({
      type: ChromaticAberrationNode.TYPE,
      name: ChromaticAberrationNode.NAME,
      description: ChromaticAberrationNode.DESCRIPTION,
      category: NodeCategory.RENDERING,
      nodeClass: ChromaticAberrationNode,
      icon: 'colorize',
      color: '#FFD700',
      tags: ['postprocess', 'chromatic', 'aberration', 'distortion']
    });

    this.registerNode({
      type: LensDistortionNode.TYPE,
      name: LensDistortionNode.NAME,
      description: LensDistortionNode.DESCRIPTION,
      category: NodeCategory.RENDERING,
      nodeClass: LensDistortionNode,
      icon: 'lens',
      color: '#FFD700',
      tags: ['postprocess', 'lens', 'distortion', 'optical']
    });

    this.registerNode({
      type: AntiAliasingNode.TYPE,
      name: AntiAliasingNode.NAME,
      description: AntiAliasingNode.DESCRIPTION,
      category: NodeCategory.RENDERING,
      nodeClass: AntiAliasingNode,
      icon: 'auto_fix_high',
      color: '#FFD700',
      tags: ['postprocess', 'antialiasing', 'smooth', 'edge']
    });

    this.registerNode({
      type: HDRProcessingNode.TYPE,
      name: HDRProcessingNode.NAME,
      description: HDRProcessingNode.DESCRIPTION,
      category: NodeCategory.RENDERING,
      nodeClass: HDRProcessingNode,
      icon: 'hdr_on',
      color: '#FFD700',
      tags: ['postprocess', 'hdr', 'processing', 'exposure']
    });

    this.registerNode({
      type: CustomPostProcessNode.TYPE,
      name: CustomPostProcessNode.NAME,
      description: CustomPostProcessNode.DESCRIPTION,
      category: NodeCategory.RENDERING,
      nodeClass: CustomPostProcessNode,
      icon: 'tune',
      color: '#FFD700',
      tags: ['postprocess', 'custom', 'shader', 'effect']
    });

    // 批次2.2：渲染优化节点（15个）
    this.registerNode({
      type: LODSystemNode.TYPE,
      name: LODSystemNode.NAME,
      description: LODSystemNode.DESCRIPTION,
      category: NodeCategory.RENDERING,
      nodeClass: LODSystemNode,
      icon: 'layers',
      color: '#4CAF50',
      tags: ['optimization', 'lod', 'performance', 'detail']
    });

    this.registerNode({
      type: FrustumCullingNode.TYPE,
      name: FrustumCullingNode.NAME,
      description: FrustumCullingNode.DESCRIPTION,
      category: NodeCategory.RENDERING,
      nodeClass: FrustumCullingNode,
      icon: 'crop',
      color: '#4CAF50',
      tags: ['optimization', 'culling', 'frustum', 'performance']
    });

    this.registerNode({
      type: OcclusionCullingNode.TYPE,
      name: OcclusionCullingNode.NAME,
      description: OcclusionCullingNode.DESCRIPTION,
      category: NodeCategory.RENDERING,
      nodeClass: OcclusionCullingNode,
      icon: 'visibility_off',
      color: '#4CAF50',
      tags: ['optimization', 'culling', 'occlusion', 'performance']
    });

    this.registerNode({
      type: BatchRenderingNode.TYPE,
      name: BatchRenderingNode.NAME,
      description: BatchRenderingNode.DESCRIPTION,
      category: NodeCategory.RENDERING,
      nodeClass: BatchRenderingNode,
      icon: 'view_module',
      color: '#4CAF50',
      tags: ['optimization', 'batching', 'drawcalls', 'performance']
    });

    this.registerNode({
      type: InstancedRenderingNode.TYPE,
      name: InstancedRenderingNode.NAME,
      description: InstancedRenderingNode.DESCRIPTION,
      category: NodeCategory.RENDERING,
      nodeClass: InstancedRenderingNode,
      icon: 'content_copy',
      color: '#4CAF50',
      tags: ['optimization', 'instancing', 'performance', 'gpu']
    });

    this.registerNode({
      type: DrawCallOptimizationNode.TYPE,
      name: DrawCallOptimizationNode.NAME,
      description: DrawCallOptimizationNode.DESCRIPTION,
      category: NodeCategory.RENDERING,
      nodeClass: DrawCallOptimizationNode,
      icon: 'thunderbolt',
      color: '#4CAF50',
      tags: ['optimization', 'drawcall', 'performance', 'batching']
    });

    this.registerNode({
      type: TextureAtlasNode.TYPE,
      name: TextureAtlasNode.NAME,
      description: TextureAtlasNode.DESCRIPTION,
      category: NodeCategory.RENDERING,
      nodeClass: TextureAtlasNode,
      icon: 'grid_view',
      color: '#4CAF50',
      tags: ['optimization', 'texture', 'atlas', 'memory']
    });

    this.registerNode({
      type: MeshCombiningNode.TYPE,
      name: MeshCombiningNode.NAME,
      description: MeshCombiningNode.DESCRIPTION,
      category: NodeCategory.RENDERING,
      nodeClass: MeshCombiningNode,
      icon: 'merge',
      color: '#4CAF50',
      tags: ['optimization', 'mesh', 'combining', 'performance']
    });

    this.registerNode({
      type: RenderQueueNode.TYPE,
      name: RenderQueueNode.NAME,
      description: RenderQueueNode.DESCRIPTION,
      category: NodeCategory.RENDERING,
      nodeClass: RenderQueueNode,
      icon: 'queue',
      color: '#4CAF50',
      tags: ['optimization', 'queue', 'rendering', 'order']
    });

    this.registerNode({
      type: PerformanceProfilerNode.TYPE,
      name: PerformanceProfilerNode.NAME,
      description: PerformanceProfilerNode.DESCRIPTION,
      category: NodeCategory.RENDERING,
      nodeClass: PerformanceProfilerNode,
      icon: 'analytics',
      color: '#4CAF50',
      tags: ['optimization', 'profiler', 'performance', 'analysis']
    });

    this.registerNode({
      type: RenderStatisticsNode.TYPE,
      name: RenderStatisticsNode.NAME,
      description: RenderStatisticsNode.DESCRIPTION,
      category: NodeCategory.RENDERING,
      nodeClass: RenderStatisticsNode,
      icon: 'bar_chart',
      color: '#4CAF50',
      tags: ['optimization', 'statistics', 'rendering', 'metrics']
    });

    this.registerNode({
      type: GPUMemoryMonitorNode.TYPE,
      name: GPUMemoryMonitorNode.NAME,
      description: GPUMemoryMonitorNode.DESCRIPTION,
      category: NodeCategory.RENDERING,
      nodeClass: GPUMemoryMonitorNode,
      icon: 'memory',
      color: '#4CAF50',
      tags: ['optimization', 'gpu', 'memory', 'monitoring']
    });

    this.registerNode({
      type: RenderPipelineNode.TYPE,
      name: RenderPipelineNode.NAME,
      description: RenderPipelineNode.DESCRIPTION,
      category: NodeCategory.RENDERING,
      nodeClass: RenderPipelineNode,
      icon: 'linear_scale',
      color: '#4CAF50',
      tags: ['optimization', 'pipeline', 'rendering', 'workflow']
    });

    this.registerNode({
      type: CustomRenderPassNode.TYPE,
      name: CustomRenderPassNode.NAME,
      description: CustomRenderPassNode.DESCRIPTION,
      category: NodeCategory.RENDERING,
      nodeClass: CustomRenderPassNode,
      icon: 'layers',
      color: '#4CAF50',
      tags: ['optimization', 'renderpass', 'custom', 'pipeline']
    });

    this.registerNode({
      type: RenderTargetNode.TYPE,
      name: RenderTargetNode.NAME,
      description: RenderTargetNode.DESCRIPTION,
      category: NodeCategory.RENDERING,
      nodeClass: RenderTargetNode,
      icon: 'target',
      color: '#4CAF50',
      tags: ['optimization', 'rendertarget', 'framebuffer', 'texture']
    });

    // 批次2.2：着色器节点（15个）
    this.registerNode({
      type: VertexShaderNode.TYPE,
      name: VertexShaderNode.NAME,
      description: VertexShaderNode.DESCRIPTION,
      category: NodeCategory.RENDERING,
      nodeClass: VertexShaderNode,
      icon: 'code',
      color: '#9C27B0',
      tags: ['shader', 'vertex', 'glsl', 'graphics']
    });

    this.registerNode({
      type: FragmentShaderNode.TYPE,
      name: FragmentShaderNode.NAME,
      description: FragmentShaderNode.DESCRIPTION,
      category: NodeCategory.RENDERING,
      nodeClass: FragmentShaderNode,
      icon: 'code',
      color: '#9C27B0',
      tags: ['shader', 'fragment', 'pixel', 'glsl']
    });

    this.registerNode({
      type: ComputeShaderNode.TYPE,
      name: ComputeShaderNode.NAME,
      description: ComputeShaderNode.DESCRIPTION,
      category: NodeCategory.RENDERING,
      nodeClass: ComputeShaderNode,
      icon: 'memory',
      color: '#9C27B0',
      tags: ['shader', 'compute', 'gpu', 'parallel']
    });

    this.registerNode({
      type: ShaderCompilerNode.TYPE,
      name: ShaderCompilerNode.NAME,
      description: ShaderCompilerNode.DESCRIPTION,
      category: NodeCategory.RENDERING,
      nodeClass: ShaderCompilerNode,
      icon: 'build',
      color: '#9C27B0',
      tags: ['shader', 'compiler', 'build', 'glsl']
    });

    this.registerNode({
      type: ShaderOptimizationNode.TYPE,
      name: ShaderOptimizationNode.NAME,
      description: ShaderOptimizationNode.DESCRIPTION,
      category: NodeCategory.RENDERING,
      nodeClass: ShaderOptimizationNode,
      icon: 'speed',
      color: '#9C27B0',
      tags: ['shader', 'optimization', 'performance', 'compiler']
    });

    Debug.log('NodeRegistry', '渲染节点注册完成 - 共35个节点 (15个后处理效果 + 15个渲染优化 + 5个着色器)');
  }

  /**
   * 注册批次0.1渲染系统集成节点
   */
  private registerBatch01RenderingNodes(): void {
    // 导入批次0.1节点注册表
    const { registerBatch01Nodes } = require('./Batch01NodesRegistry');
    registerBatch01Nodes();

    Debug.log('NodeRegistry', '批次0.1渲染系统集成节点注册完成 - 共69个节点 (24个材质管理 + 15个后处理效果 + 15个着色器 + 15个渲染优化)');
  }

  /**
   * 注册工业制造节点
   */
  private registerIndustrialNodes(): void {
    // MES系统节点
    this.registerNode({
      type: ProductionOrderNode.TYPE,
      name: ProductionOrderNode.NAME,
      description: ProductionOrderNode.DESCRIPTION,
      category: NodeCategory.INDUSTRIAL_AUTOMATION,
      nodeClass: ProductionOrderNode,
      icon: 'assignment',
      color: '#FF9800',
      tags: ['mes', 'production', 'order', 'manufacturing']
    });

    this.registerNode({
      type: WorkflowManagementNode.TYPE,
      name: WorkflowManagementNode.NAME,
      description: WorkflowManagementNode.DESCRIPTION,
      category: NodeCategory.INDUSTRIAL_AUTOMATION,
      nodeClass: WorkflowManagementNode,
      icon: 'account_tree',
      color: '#FF9800',
      tags: ['mes', 'workflow', 'process', 'manufacturing']
    });

    this.registerNode({
      type: QualityControlNode.TYPE,
      name: QualityControlNode.NAME,
      description: QualityControlNode.DESCRIPTION,
      category: NodeCategory.INDUSTRIAL_AUTOMATION,
      nodeClass: QualityControlNode,
      icon: 'verified',
      color: '#FF9800',
      tags: ['mes', 'quality', 'control', 'inspection']
    });

    // 设备管理节点
    this.registerNode({
      type: DeviceConnectionNode.TYPE,
      name: DeviceConnectionNode.NAME,
      description: DeviceConnectionNode.DESCRIPTION,
      category: NodeCategory.INDUSTRIAL_AUTOMATION,
      nodeClass: DeviceConnectionNode,
      icon: 'device_hub',
      color: '#2196F3',
      tags: ['device', 'connection', 'management', 'industrial']
    });

    this.registerNode({
      type: DeviceMonitoringNode.TYPE,
      name: DeviceMonitoringNode.NAME,
      description: DeviceMonitoringNode.DESCRIPTION,
      category: NodeCategory.INDUSTRIAL_AUTOMATION,
      nodeClass: DeviceMonitoringNode,
      icon: 'monitor',
      color: '#2196F3',
      tags: ['device', 'monitoring', 'status', 'industrial']
    });

    this.registerNode({
      type: DeviceControlNode.TYPE,
      name: DeviceControlNode.NAME,
      description: DeviceControlNode.DESCRIPTION,
      category: NodeCategory.INDUSTRIAL_AUTOMATION,
      nodeClass: DeviceControlNode,
      icon: 'settings_remote',
      color: '#2196F3',
      tags: ['device', 'control', 'command', 'industrial']
    });

    // 预测性维护节点
    this.registerNode({
      type: ConditionMonitoringNode.TYPE,
      name: ConditionMonitoringNode.NAME,
      description: ConditionMonitoringNode.DESCRIPTION,
      category: NodeCategory.INDUSTRIAL_AUTOMATION,
      nodeClass: ConditionMonitoringNode,
      icon: 'monitor_heart',
      color: '#4CAF50',
      tags: ['maintenance', 'monitoring', 'condition', 'predictive']
    });

    this.registerNode({
      type: FailurePredictionNode.TYPE,
      name: FailurePredictionNode.NAME,
      description: FailurePredictionNode.DESCRIPTION,
      category: NodeCategory.INDUSTRIAL_AUTOMATION,
      nodeClass: FailurePredictionNode,
      icon: 'warning',
      color: '#4CAF50',
      tags: ['maintenance', 'prediction', 'failure', 'analysis']
    });

    // 新增MES系统节点（批次2.3）
    this.registerNode({
      type: InventoryManagementNode.TYPE,
      name: InventoryManagementNode.NAME,
      description: InventoryManagementNode.DESCRIPTION,
      category: NodeCategory.INDUSTRIAL_AUTOMATION,
      nodeClass: InventoryManagementNode,
      icon: 'inventory',
      color: '#FF9800',
      tags: ['mes', 'inventory', 'stock', 'warehouse']
    });

    this.registerNode({
      type: SchedulingNode.TYPE,
      name: SchedulingNode.NAME,
      description: SchedulingNode.DESCRIPTION,
      category: NodeCategory.INDUSTRIAL_AUTOMATION,
      nodeClass: SchedulingNode,
      icon: 'schedule',
      color: '#FF9800',
      tags: ['mes', 'scheduling', 'planning', 'production']
    });

    this.registerNode({
      type: ResourceAllocationNode.TYPE,
      name: ResourceAllocationNode.NAME,
      description: ResourceAllocationNode.DESCRIPTION,
      category: NodeCategory.INDUSTRIAL_AUTOMATION,
      nodeClass: ResourceAllocationNode,
      icon: 'assignment_ind',
      color: '#FF9800',
      tags: ['mes', 'resource', 'allocation', 'planning']
    });

    this.registerNode({
      type: ProductionTrackingNode.TYPE,
      name: ProductionTrackingNode.NAME,
      description: ProductionTrackingNode.DESCRIPTION,
      category: NodeCategory.INDUSTRIAL_AUTOMATION,
      nodeClass: ProductionTrackingNode,
      icon: 'track_changes',
      color: '#FF9800',
      tags: ['mes', 'tracking', 'progress', 'production']
    });

    this.registerNode({
      type: PerformanceMonitoringNode.TYPE,
      name: PerformanceMonitoringNode.NAME,
      description: PerformanceMonitoringNode.DESCRIPTION,
      category: NodeCategory.INDUSTRIAL_AUTOMATION,
      nodeClass: PerformanceMonitoringNode,
      icon: 'analytics',
      color: '#FF9800',
      tags: ['mes', 'performance', 'monitoring', 'kpi']
    });

    this.registerNode({
      type: ReportGenerationNode.TYPE,
      name: ReportGenerationNode.NAME,
      description: ReportGenerationNode.DESCRIPTION,
      category: NodeCategory.INDUSTRIAL_AUTOMATION,
      nodeClass: ReportGenerationNode,
      icon: 'assessment',
      color: '#FF9800',
      tags: ['mes', 'report', 'generation', 'analytics']
    });

    this.registerNode({
      type: AlertSystemNode.TYPE,
      name: AlertSystemNode.NAME,
      description: AlertSystemNode.DESCRIPTION,
      category: NodeCategory.INDUSTRIAL_AUTOMATION,
      nodeClass: AlertSystemNode,
      icon: 'notification_important',
      color: '#FF9800',
      tags: ['mes', 'alert', 'notification', 'alarm']
    });

    this.registerNode({
      type: ComplianceCheckNode.TYPE,
      name: ComplianceCheckNode.NAME,
      description: ComplianceCheckNode.DESCRIPTION,
      category: NodeCategory.INDUSTRIAL_AUTOMATION,
      nodeClass: ComplianceCheckNode,
      icon: 'rule',
      color: '#FF9800',
      tags: ['mes', 'compliance', 'check', 'regulation']
    });

    this.registerNode({
      type: MaintenanceScheduleNode.TYPE,
      name: MaintenanceScheduleNode.NAME,
      description: MaintenanceScheduleNode.DESCRIPTION,
      category: NodeCategory.INDUSTRIAL_AUTOMATION,
      nodeClass: MaintenanceScheduleNode,
      icon: 'event',
      color: '#FF9800',
      tags: ['mes', 'maintenance', 'schedule', 'planning']
    });

    this.registerNode({
      type: ProductionOptimizationNode.TYPE,
      name: ProductionOptimizationNode.NAME,
      description: ProductionOptimizationNode.DESCRIPTION,
      category: NodeCategory.INDUSTRIAL_AUTOMATION,
      nodeClass: ProductionOptimizationNode,
      icon: 'tune',
      color: '#FF9800',
      tags: ['mes', 'optimization', 'efficiency', 'production']
    });

    this.registerNode({
      type: CostAnalysisNode.TYPE,
      name: CostAnalysisNode.NAME,
      description: CostAnalysisNode.DESCRIPTION,
      category: NodeCategory.INDUSTRIAL_AUTOMATION,
      nodeClass: CostAnalysisNode,
      icon: 'attach_money',
      color: '#FF9800',
      tags: ['mes', 'cost', 'analysis', 'finance']
    });

    this.registerNode({
      type: EfficiencyAnalysisNode.TYPE,
      name: EfficiencyAnalysisNode.NAME,
      description: EfficiencyAnalysisNode.DESCRIPTION,
      category: NodeCategory.INDUSTRIAL_AUTOMATION,
      nodeClass: EfficiencyAnalysisNode,
      icon: 'speed',
      color: '#FF9800',
      tags: ['mes', 'efficiency', 'analysis', 'performance']
    });

    // 新增设备管理节点（批次2.3）
    this.registerNode({
      type: DeviceMaintenanceNode.TYPE,
      name: DeviceMaintenanceNode.NAME,
      description: DeviceMaintenanceNode.DESCRIPTION,
      category: NodeCategory.INDUSTRIAL_AUTOMATION,
      nodeClass: DeviceMaintenanceNode,
      icon: 'build',
      color: '#2196F3',
      tags: ['device', 'maintenance', 'repair', 'service']
    });

    this.registerNode({
      type: DeviceDiagnosticsNode.TYPE,
      name: DeviceDiagnosticsNode.NAME,
      description: DeviceDiagnosticsNode.DESCRIPTION,
      category: NodeCategory.INDUSTRIAL_AUTOMATION,
      nodeClass: DeviceDiagnosticsNode,
      icon: 'healing',
      color: '#2196F3',
      tags: ['device', 'diagnostics', 'health', 'check']
    });

    this.registerNode({
      type: DeviceCalibrationNode.TYPE,
      name: DeviceCalibrationNode.NAME,
      description: DeviceCalibrationNode.DESCRIPTION,
      category: NodeCategory.INDUSTRIAL_AUTOMATION,
      nodeClass: DeviceCalibrationNode,
      icon: 'tune',
      color: '#2196F3',
      tags: ['device', 'calibration', 'accuracy', 'precision']
    });

    this.registerNode({
      type: DeviceConfigurationNode.TYPE,
      name: DeviceConfigurationNode.NAME,
      description: DeviceConfigurationNode.DESCRIPTION,
      category: NodeCategory.INDUSTRIAL_AUTOMATION,
      nodeClass: DeviceConfigurationNode,
      icon: 'settings',
      color: '#2196F3',
      tags: ['device', 'configuration', 'setup', 'parameters']
    });

    this.registerNode({
      type: DevicePerformanceNode.TYPE,
      name: DevicePerformanceNode.NAME,
      description: DevicePerformanceNode.DESCRIPTION,
      category: NodeCategory.INDUSTRIAL_AUTOMATION,
      nodeClass: DevicePerformanceNode,
      icon: 'speed',
      color: '#2196F3',
      tags: ['device', 'performance', 'metrics', 'monitoring']
    });

    this.registerNode({
      type: DeviceAlertNode.TYPE,
      name: DeviceAlertNode.NAME,
      description: DeviceAlertNode.DESCRIPTION,
      category: NodeCategory.INDUSTRIAL_AUTOMATION,
      nodeClass: DeviceAlertNode,
      icon: 'warning',
      color: '#2196F3',
      tags: ['device', 'alert', 'alarm', 'notification']
    });

    this.registerNode({
      type: DeviceLifecycleNode.TYPE,
      name: DeviceLifecycleNode.NAME,
      description: DeviceLifecycleNode.DESCRIPTION,
      category: NodeCategory.INDUSTRIAL_AUTOMATION,
      nodeClass: DeviceLifecycleNode,
      icon: 'timeline',
      color: '#2196F3',
      tags: ['device', 'lifecycle', 'age', 'history']
    });

    // 新增预测性维护节点（批次2.3）
    this.registerNode({
      type: MaintenanceSchedulingNode.TYPE,
      name: MaintenanceSchedulingNode.NAME,
      description: MaintenanceSchedulingNode.DESCRIPTION,
      category: NodeCategory.INDUSTRIAL_AUTOMATION,
      nodeClass: MaintenanceSchedulingNode,
      icon: 'event_note',
      color: '#4CAF50',
      tags: ['maintenance', 'scheduling', 'planning', 'predictive']
    });

    this.registerNode({
      type: PartReplacementNode.TYPE,
      name: PartReplacementNode.NAME,
      description: PartReplacementNode.DESCRIPTION,
      category: NodeCategory.INDUSTRIAL_AUTOMATION,
      nodeClass: PartReplacementNode,
      icon: 'build_circle',
      color: '#4CAF50',
      tags: ['maintenance', 'parts', 'replacement', 'inventory']
    });

    this.registerNode({
      type: MaintenanceHistoryNode.TYPE,
      name: MaintenanceHistoryNode.NAME,
      description: MaintenanceHistoryNode.DESCRIPTION,
      category: NodeCategory.INDUSTRIAL_AUTOMATION,
      nodeClass: MaintenanceHistoryNode,
      icon: 'history',
      color: '#4CAF50',
      tags: ['maintenance', 'history', 'records', 'tracking']
    });

    this.registerNode({
      type: MaintenanceCostNode.TYPE,
      name: MaintenanceCostNode.NAME,
      description: MaintenanceCostNode.DESCRIPTION,
      category: NodeCategory.INDUSTRIAL_AUTOMATION,
      nodeClass: MaintenanceCostNode,
      icon: 'account_balance',
      color: '#4CAF50',
      tags: ['maintenance', 'cost', 'budget', 'finance']
    });

    this.registerNode({
      type: MaintenanceAnalyticsNode.TYPE,
      name: MaintenanceAnalyticsNode.NAME,
      description: MaintenanceAnalyticsNode.DESCRIPTION,
      category: NodeCategory.INDUSTRIAL_AUTOMATION,
      nodeClass: MaintenanceAnalyticsNode,
      icon: 'analytics',
      color: '#4CAF50',
      tags: ['maintenance', 'analytics', 'insights', 'analysis']
    });

    this.registerNode({
      type: MaintenanceOptimizationNode.TYPE,
      name: MaintenanceOptimizationNode.NAME,
      description: MaintenanceOptimizationNode.DESCRIPTION,
      category: NodeCategory.INDUSTRIAL_AUTOMATION,
      nodeClass: MaintenanceOptimizationNode,
      icon: 'auto_fix_high',
      color: '#4CAF50',
      tags: ['maintenance', 'optimization', 'efficiency', 'improvement']
    });

    this.registerNode({
      type: MaintenanceReportingNode.TYPE,
      name: MaintenanceReportingNode.NAME,
      description: MaintenanceReportingNode.DESCRIPTION,
      category: NodeCategory.INDUSTRIAL_AUTOMATION,
      nodeClass: MaintenanceReportingNode,
      icon: 'description',
      color: '#4CAF50',
      tags: ['maintenance', 'reporting', 'documentation', 'export']
    });

    this.registerNode({
      type: MaintenanceWorkflowNode.TYPE,
      name: MaintenanceWorkflowNode.NAME,
      description: MaintenanceWorkflowNode.DESCRIPTION,
      category: NodeCategory.INDUSTRIAL_AUTOMATION,
      nodeClass: MaintenanceWorkflowNode,
      icon: 'workflow',
      color: '#4CAF50',
      tags: ['maintenance', 'workflow', 'approval', 'process']
    });

    // 批次2.3：质量管理节点（10个）
    this.registerNode({
      type: QMQualityInspectionNode.TYPE,
      name: QMQualityInspectionNode.NAME,
      description: QMQualityInspectionNode.DESCRIPTION,
      category: NodeCategory.INDUSTRIAL_AUTOMATION,
      nodeClass: QMQualityInspectionNode,
      icon: 'fact_check',
      color: '#9C27B0',
      tags: ['quality', 'inspection', 'testing', 'verification']
    });

    this.registerNode({
      type: QualityTestingNode.TYPE,
      name: QualityTestingNode.NAME,
      description: QualityTestingNode.DESCRIPTION,
      category: NodeCategory.INDUSTRIAL_AUTOMATION,
      nodeClass: QualityTestingNode,
      icon: 'science',
      color: '#9C27B0',
      tags: ['quality', 'testing', 'validation', 'performance']
    });

    this.registerNode({
      type: QualityAnalysisNode.TYPE,
      name: QualityAnalysisNode.NAME,
      description: QualityAnalysisNode.DESCRIPTION,
      category: NodeCategory.INDUSTRIAL_AUTOMATION,
      nodeClass: QualityAnalysisNode,
      icon: 'analytics',
      color: '#9C27B0',
      tags: ['quality', 'analysis', 'statistics', 'trends']
    });

    this.registerNode({
      type: QualityReportingNode.TYPE,
      name: QualityReportingNode.NAME,
      description: QualityReportingNode.DESCRIPTION,
      category: NodeCategory.INDUSTRIAL_AUTOMATION,
      nodeClass: QualityReportingNode,
      icon: 'assessment',
      color: '#9C27B0',
      tags: ['quality', 'reporting', 'documentation', 'charts']
    });

    this.registerNode({
      type: QualityControlPlanNode.TYPE,
      name: QualityControlPlanNode.NAME,
      description: QualityControlPlanNode.DESCRIPTION,
      category: NodeCategory.INDUSTRIAL_AUTOMATION,
      nodeClass: QualityControlPlanNode,
      icon: 'rule',
      color: '#9C27B0',
      tags: ['quality', 'control', 'planning', 'standards']
    });

    this.registerNode({
      type: QualityAuditNode.TYPE,
      name: QualityAuditNode.NAME,
      description: QualityAuditNode.DESCRIPTION,
      category: NodeCategory.INDUSTRIAL_AUTOMATION,
      nodeClass: QualityAuditNode,
      icon: 'verified',
      color: '#9C27B0',
      tags: ['quality', 'audit', 'compliance', 'review']
    });

    this.registerNode({
      type: QualityImprovementNode.TYPE,
      name: QualityImprovementNode.NAME,
      description: QualityImprovementNode.DESCRIPTION,
      category: NodeCategory.INDUSTRIAL_AUTOMATION,
      nodeClass: QualityImprovementNode,
      icon: 'trending_up',
      color: '#9C27B0',
      tags: ['quality', 'improvement', 'optimization', 'enhancement']
    });

    this.registerNode({
      type: QualityStandardsNode.TYPE,
      name: QualityStandardsNode.NAME,
      description: QualityStandardsNode.DESCRIPTION,
      category: NodeCategory.INDUSTRIAL_AUTOMATION,
      nodeClass: QualityStandardsNode,
      icon: 'gavel',
      color: '#9C27B0',
      tags: ['quality', 'standards', 'specifications', 'requirements']
    });

    this.registerNode({
      type: QualityMetricsNode.TYPE,
      name: QualityMetricsNode.NAME,
      description: QualityMetricsNode.DESCRIPTION,
      category: NodeCategory.INDUSTRIAL_AUTOMATION,
      nodeClass: QualityMetricsNode,
      icon: 'speed',
      color: '#9C27B0',
      tags: ['quality', 'metrics', 'kpi', 'performance']
    });

    this.registerNode({
      type: QualityTraceabilityNode.TYPE,
      name: QualityTraceabilityNode.NAME,
      description: QualityTraceabilityNode.DESCRIPTION,
      category: NodeCategory.INDUSTRIAL_AUTOMATION,
      nodeClass: QualityTraceabilityNode,
      icon: 'track_changes',
      color: '#9C27B0',
      tags: ['quality', 'traceability', 'history', 'tracking']
    });

    // 批次2.3：供应链管理节点（8个）
    this.registerNode({
      type: SupplierManagementNode.TYPE,
      name: SupplierManagementNode.NAME,
      description: SupplierManagementNode.DESCRIPTION,
      category: NodeCategory.INDUSTRIAL_AUTOMATION,
      nodeClass: SupplierManagementNode,
      icon: 'business',
      color: '#FF5722',
      tags: ['supply', 'supplier', 'management', 'vendor']
    });

    this.registerNode({
      type: ProcurementNode.TYPE,
      name: ProcurementNode.NAME,
      description: ProcurementNode.DESCRIPTION,
      category: NodeCategory.INDUSTRIAL_AUTOMATION,
      nodeClass: ProcurementNode,
      icon: 'shopping_cart',
      color: '#FF5722',
      tags: ['procurement', 'purchasing', 'order', 'buying']
    });

    this.registerNode({
      type: LogisticsNode.TYPE,
      name: LogisticsNode.NAME,
      description: LogisticsNode.DESCRIPTION,
      category: NodeCategory.INDUSTRIAL_AUTOMATION,
      nodeClass: LogisticsNode,
      icon: 'local_shipping',
      color: '#FF5722',
      tags: ['logistics', 'shipping', 'transport', 'delivery']
    });

    this.registerNode({
      type: WarehouseManagementNode.TYPE,
      name: WarehouseManagementNode.NAME,
      description: WarehouseManagementNode.DESCRIPTION,
      category: NodeCategory.INDUSTRIAL_AUTOMATION,
      nodeClass: WarehouseManagementNode,
      icon: 'warehouse',
      color: '#FF5722',
      tags: ['warehouse', 'inventory', 'storage', 'stock']
    });

    this.registerNode({
      type: SupplyChainOptimizationNode.TYPE,
      name: SupplyChainOptimizationNode.NAME,
      description: SupplyChainOptimizationNode.DESCRIPTION,
      category: NodeCategory.INDUSTRIAL_AUTOMATION,
      nodeClass: SupplyChainOptimizationNode,
      icon: 'tune',
      color: '#FF5722',
      tags: ['supply', 'optimization', 'efficiency', 'cost']
    });

    this.registerNode({
      type: SupplyChainAnalyticsNode.TYPE,
      name: SupplyChainAnalyticsNode.NAME,
      description: SupplyChainAnalyticsNode.DESCRIPTION,
      category: NodeCategory.INDUSTRIAL_AUTOMATION,
      nodeClass: SupplyChainAnalyticsNode,
      icon: 'insights',
      color: '#FF5722',
      tags: ['supply', 'analytics', 'analysis', 'metrics']
    });

    this.registerNode({
      type: SupplyChainRiskNode.TYPE,
      name: SupplyChainRiskNode.NAME,
      description: SupplyChainRiskNode.DESCRIPTION,
      category: NodeCategory.INDUSTRIAL_AUTOMATION,
      nodeClass: SupplyChainRiskNode,
      icon: 'warning',
      color: '#FF5722',
      tags: ['supply', 'risk', 'assessment', 'mitigation']
    });

    this.registerNode({
      type: SupplyChainVisibilityNode.TYPE,
      name: SupplyChainVisibilityNode.NAME,
      description: SupplyChainVisibilityNode.DESCRIPTION,
      category: NodeCategory.INDUSTRIAL_AUTOMATION,
      nodeClass: SupplyChainVisibilityNode,
      icon: 'visibility',
      color: '#FF5722',
      tags: ['supply', 'visibility', 'tracking', 'monitoring']
    });

    // 批次2.3：能耗管理节点（7个）
    this.registerNode({
      type: EnergyMonitoringNode.TYPE,
      name: EnergyMonitoringNode.NAME,
      description: EnergyMonitoringNode.DESCRIPTION,
      category: NodeCategory.INDUSTRIAL_AUTOMATION,
      nodeClass: EnergyMonitoringNode,
      icon: 'electric_meter',
      color: '#4CAF50',
      tags: ['energy', 'monitoring', 'consumption', 'meter']
    });

    this.registerNode({
      type: EnergyOptimizationNode.TYPE,
      name: EnergyOptimizationNode.NAME,
      description: EnergyOptimizationNode.DESCRIPTION,
      category: NodeCategory.INDUSTRIAL_AUTOMATION,
      nodeClass: EnergyOptimizationNode,
      icon: 'eco',
      color: '#4CAF50',
      tags: ['energy', 'optimization', 'efficiency', 'savings']
    });

    this.registerNode({
      type: EnergyAnalyticsNode.TYPE,
      name: EnergyAnalyticsNode.NAME,
      description: EnergyAnalyticsNode.DESCRIPTION,
      category: NodeCategory.INDUSTRIAL_AUTOMATION,
      nodeClass: EnergyAnalyticsNode,
      icon: 'analytics',
      color: '#4CAF50',
      tags: ['energy', 'analytics', 'analysis', 'trends']
    });

    this.registerNode({
      type: EnergyReportingNode.TYPE,
      name: EnergyReportingNode.NAME,
      description: EnergyReportingNode.DESCRIPTION,
      category: NodeCategory.INDUSTRIAL_AUTOMATION,
      nodeClass: EnergyReportingNode,
      icon: 'description',
      color: '#4CAF50',
      tags: ['energy', 'reporting', 'documentation', 'charts']
    });

    this.registerNode({
      type: EnergyForecastingNode.TYPE,
      name: EnergyForecastingNode.NAME,
      description: EnergyForecastingNode.DESCRIPTION,
      category: NodeCategory.INDUSTRIAL_AUTOMATION,
      nodeClass: EnergyForecastingNode,
      icon: 'trending_up',
      color: '#4CAF50',
      tags: ['energy', 'forecasting', 'prediction', 'planning']
    });

    this.registerNode({
      type: EnergyEfficiencyNode.TYPE,
      name: EnergyEfficiencyNode.NAME,
      description: EnergyEfficiencyNode.DESCRIPTION,
      category: NodeCategory.INDUSTRIAL_AUTOMATION,
      nodeClass: EnergyEfficiencyNode,
      icon: 'speed',
      color: '#4CAF50',
      tags: ['energy', 'efficiency', 'performance', 'benchmarking']
    });

    this.registerNode({
      type: CarbonFootprintNode.TYPE,
      name: CarbonFootprintNode.NAME,
      description: CarbonFootprintNode.DESCRIPTION,
      category: NodeCategory.INDUSTRIAL_AUTOMATION,
      nodeClass: CarbonFootprintNode,
      icon: 'nature',
      color: '#4CAF50',
      tags: ['carbon', 'footprint', 'emissions', 'environment']
    });

    Debug.log('NodeRegistry', '工业制造节点注册完成 - 共60个节点 (15个MES + 10个设备管理 + 10个预测性维护 + 10个质量管理 + 8个供应链管理 + 7个能耗管理)');

    // 批次3.1：场景编辑节点（15个）
    this.registerNode({
      type: SceneViewportNode.TYPE,
      name: SceneViewportNode.NAME,
      description: SceneViewportNode.DESCRIPTION,
      category: NodeCategory.SCENE_EDITING,
      nodeClass: SceneViewportNode,
      icon: 'view_in_ar',
      color: '#9C27B0',
      tags: ['scene', 'viewport', 'camera', 'rendering']
    });

    this.registerNode({
      type: ObjectSelectionNode.TYPE,
      name: ObjectSelectionNode.NAME,
      description: ObjectSelectionNode.DESCRIPTION,
      category: NodeCategory.SCENE_EDITING,
      nodeClass: ObjectSelectionNode,
      icon: 'select_all',
      color: '#9C27B0',
      tags: ['object', 'selection', 'pick', 'choose']
    });

    this.registerNode({
      type: ObjectTransformNode.TYPE,
      name: ObjectTransformNode.NAME,
      description: ObjectTransformNode.DESCRIPTION,
      category: NodeCategory.SCENE_EDITING,
      nodeClass: ObjectTransformNode,
      icon: 'transform',
      color: '#9C27B0',
      tags: ['object', 'transform', 'position', 'rotation', 'scale']
    });

    this.registerNode({
      type: ObjectDuplicationNode.TYPE,
      name: ObjectDuplicationNode.NAME,
      description: ObjectDuplicationNode.DESCRIPTION,
      category: NodeCategory.SCENE_EDITING,
      nodeClass: ObjectDuplicationNode,
      icon: 'content_copy',
      color: '#9C27B0',
      tags: ['object', 'duplicate', 'copy', 'clone']
    });

    this.registerNode({
      type: ObjectGroupingNode.TYPE,
      name: ObjectGroupingNode.NAME,
      description: ObjectGroupingNode.DESCRIPTION,
      category: NodeCategory.SCENE_EDITING,
      nodeClass: ObjectGroupingNode,
      icon: 'group_work',
      color: '#9C27B0',
      tags: ['object', 'group', 'organize', 'hierarchy']
    });

    this.registerNode({
      type: ObjectLayerNode.TYPE,
      name: ObjectLayerNode.NAME,
      description: ObjectLayerNode.DESCRIPTION,
      category: NodeCategory.SCENE_EDITING,
      nodeClass: ObjectLayerNode,
      icon: 'layers',
      color: '#9C27B0',
      tags: ['object', 'layer', 'visibility', 'organization']
    });

    this.registerNode({
      type: GridSnapNode.TYPE,
      name: GridSnapNode.NAME,
      description: GridSnapNode.DESCRIPTION,
      category: NodeCategory.SCENE_EDITING,
      nodeClass: GridSnapNode,
      icon: 'grid_on',
      color: '#9C27B0',
      tags: ['grid', 'snap', 'alignment', 'precision']
    });

    this.registerNode({
      type: ObjectAlignmentNode.TYPE,
      name: ObjectAlignmentNode.NAME,
      description: ObjectAlignmentNode.DESCRIPTION,
      category: NodeCategory.SCENE_EDITING,
      nodeClass: ObjectAlignmentNode,
      icon: 'align_horizontal_center',
      color: '#9C27B0',
      tags: ['object', 'align', 'position', 'arrangement']
    });

    this.registerNode({
      type: ObjectDistributionNode.TYPE,
      name: ObjectDistributionNode.NAME,
      description: ObjectDistributionNode.DESCRIPTION,
      category: NodeCategory.SCENE_EDITING,
      nodeClass: ObjectDistributionNode,
      icon: 'distribute',
      color: '#9C27B0',
      tags: ['object', 'distribute', 'spacing', 'arrangement']
    });

    this.registerNode({
      type: UndoRedoNode.TYPE,
      name: UndoRedoNode.NAME,
      description: UndoRedoNode.DESCRIPTION,
      category: NodeCategory.SCENE_EDITING,
      nodeClass: UndoRedoNode,
      icon: 'undo',
      color: '#9C27B0',
      tags: ['undo', 'redo', 'history', 'revert']
    });

    this.registerNode({
      type: HistoryManagementNode.TYPE,
      name: HistoryManagementNode.NAME,
      description: HistoryManagementNode.DESCRIPTION,
      category: NodeCategory.SCENE_EDITING,
      nodeClass: HistoryManagementNode,
      icon: 'history',
      color: '#9C27B0',
      tags: ['history', 'management', 'tracking', 'operations']
    });

    this.registerNode({
      type: SelectionFilterNode.TYPE,
      name: SelectionFilterNode.NAME,
      description: SelectionFilterNode.DESCRIPTION,
      category: NodeCategory.SCENE_EDITING,
      nodeClass: SelectionFilterNode,
      icon: 'filter_list',
      color: '#9C27B0',
      tags: ['selection', 'filter', 'search', 'criteria']
    });

    this.registerNode({
      type: ViewportNavigationNode.TYPE,
      name: ViewportNavigationNode.NAME,
      description: ViewportNavigationNode.DESCRIPTION,
      category: NodeCategory.SCENE_EDITING,
      nodeClass: ViewportNavigationNode,
      icon: 'navigation',
      color: '#9C27B0',
      tags: ['viewport', 'navigation', 'camera', 'movement']
    });

    this.registerNode({
      type: ViewportRenderingNode.TYPE,
      name: ViewportRenderingNode.NAME,
      description: ViewportRenderingNode.DESCRIPTION,
      category: NodeCategory.SCENE_EDITING,
      nodeClass: ViewportRenderingNode,
      icon: 'visibility',
      color: '#9C27B0',
      tags: ['viewport', 'rendering', 'display', 'graphics']
    });

    this.registerNode({
      type: ViewportSettingsNode.TYPE,
      name: ViewportSettingsNode.NAME,
      description: ViewportSettingsNode.DESCRIPTION,
      category: NodeCategory.SCENE_EDITING,
      nodeClass: ViewportSettingsNode,
      icon: 'settings',
      color: '#9C27B0',
      tags: ['viewport', 'settings', 'configuration', 'preferences']
    });

    Debug.log('NodeRegistry', '场景编辑节点注册完成 - 共15个节点');

    // 批次3.1：材质编辑节点（10个）
    this.registerNode({
      type: MaterialEditorNode.TYPE,
      name: MaterialEditorNode.NAME,
      description: MaterialEditorNode.DESCRIPTION,
      category: NodeCategory.MATERIAL_EDITING,
      nodeClass: MaterialEditorNode,
      icon: 'palette',
      color: '#E91E63',
      tags: ['material', 'editor', 'properties', 'shader']
    });

    this.registerNode({
      type: MaterialPreviewNode.TYPE,
      name: MaterialPreviewNode.NAME,
      description: MaterialPreviewNode.DESCRIPTION,
      category: NodeCategory.MATERIAL_EDITING,
      nodeClass: MaterialPreviewNode,
      icon: 'preview',
      color: '#E91E63',
      tags: ['material', 'preview', 'render', 'visualization']
    });

    this.registerNode({
      type: MaterialLibraryNode.TYPE,
      name: MaterialLibraryNode.NAME,
      description: MaterialLibraryNode.DESCRIPTION,
      category: NodeCategory.MATERIAL_EDITING,
      nodeClass: MaterialLibraryNode,
      icon: 'library_books',
      color: '#E91E63',
      tags: ['material', 'library', 'collection', 'assets']
    });

    this.registerNode({
      type: MaterialImportNode.TYPE,
      name: MaterialImportNode.NAME,
      description: MaterialImportNode.DESCRIPTION,
      category: NodeCategory.MATERIAL_EDITING,
      nodeClass: MaterialImportNode,
      icon: 'file_download',
      color: '#E91E63',
      tags: ['material', 'import', 'load', 'external']
    });

    this.registerNode({
      type: MaterialExportNode.TYPE,
      name: MaterialExportNode.NAME,
      description: MaterialExportNode.DESCRIPTION,
      category: NodeCategory.MATERIAL_EDITING,
      nodeClass: MaterialExportNode,
      icon: 'file_upload',
      color: '#E91E63',
      tags: ['material', 'export', 'save', 'share']
    });

    this.registerNode({
      type: MaterialValidationNode.TYPE,
      name: MaterialValidationNode.NAME,
      description: MaterialValidationNode.DESCRIPTION,
      category: NodeCategory.MATERIAL_EDITING,
      nodeClass: MaterialValidationNode,
      icon: 'check_circle',
      color: '#E91E63',
      tags: ['material', 'validation', 'check', 'verify']
    });

    this.registerNode({
      type: MaterialOptimizationNode.TYPE,
      name: MaterialOptimizationNode.NAME,
      description: MaterialOptimizationNode.DESCRIPTION,
      category: NodeCategory.MATERIAL_EDITING,
      nodeClass: MaterialOptimizationNode,
      icon: 'speed',
      color: '#E91E63',
      tags: ['material', 'optimization', 'performance', 'efficiency']
    });

    this.registerNode({
      type: MaterialVersioningNode.TYPE,
      name: MaterialVersioningNode.NAME,
      description: MaterialVersioningNode.DESCRIPTION,
      category: NodeCategory.MATERIAL_EDITING,
      nodeClass: MaterialVersioningNode,
      icon: 'history',
      color: '#E91E63',
      tags: ['material', 'versioning', 'history', 'backup']
    });

    this.registerNode({
      type: MaterialSharingNode.TYPE,
      name: MaterialSharingNode.NAME,
      description: MaterialSharingNode.DESCRIPTION,
      category: NodeCategory.MATERIAL_EDITING,
      nodeClass: MaterialSharingNode,
      icon: 'share',
      color: '#E91E63',
      tags: ['material', 'sharing', 'collaboration', 'team']
    });

    this.registerNode({
      type: MaterialAnalyticsNode.TYPE,
      name: MaterialAnalyticsNode.NAME,
      description: MaterialAnalyticsNode.DESCRIPTION,
      category: NodeCategory.MATERIAL_EDITING,
      nodeClass: MaterialAnalyticsNode,
      icon: 'analytics',
      color: '#E91E63',
      tags: ['material', 'analytics', 'analysis', 'metrics']
    });

    Debug.log('NodeRegistry', '材质编辑节点注册完成 - 共10个节点');
    Debug.log('NodeRegistry', '批次3.1节点开发完成 - 场景编辑节点15个 + 材质编辑节点10个 = 共25个节点');

    // 注册批次3.3节点
    this.registerBatch33Nodes();
  }

  /**
   * 注册批次3.3节点：深度学习和机器学习节点
   */
  private registerBatch33Nodes(): void {
    // 深度学习节点（15个）
    this.registerNode({
      type: DeepLearningModelNode.TYPE,
      name: DeepLearningModelNode.NAME,
      description: DeepLearningModelNode.DESCRIPTION,
      category: NodeCategory.AI_SERVICE,
      nodeClass: DeepLearningModelNode,
      icon: 'brain',
      color: '#FF6B35',
      tags: ['deep-learning', 'neural-network', 'ai', 'model']
    });

    this.registerNode({
      type: NeuralNetworkNode.TYPE,
      name: NeuralNetworkNode.NAME,
      description: NeuralNetworkNode.DESCRIPTION,
      category: NodeCategory.AI_SERVICE,
      nodeClass: NeuralNetworkNode,
      icon: 'network',
      color: '#FF6B35',
      tags: ['neural-network', 'forward-pass', 'activation', 'weights']
    });

    this.registerNode({
      type: ConvolutionalNetworkNode.TYPE,
      name: ConvolutionalNetworkNode.NAME,
      description: ConvolutionalNetworkNode.DESCRIPTION,
      category: NodeCategory.AI_SERVICE,
      nodeClass: ConvolutionalNetworkNode,
      icon: 'grid',
      color: '#FF6B35',
      tags: ['cnn', 'convolution', 'image-processing', 'feature-maps']
    });

    this.registerNode({
      type: RecurrentNetworkNode.TYPE,
      name: RecurrentNetworkNode.NAME,
      description: RecurrentNetworkNode.DESCRIPTION,
      category: NodeCategory.AI_SERVICE,
      nodeClass: RecurrentNetworkNode,
      icon: 'repeat',
      color: '#FF6B35',
      tags: ['rnn', 'sequence', 'time-series', 'hidden-state']
    });

    this.registerNode({
      type: TransformerModelNode.TYPE,
      name: TransformerModelNode.NAME,
      description: TransformerModelNode.DESCRIPTION,
      category: NodeCategory.AI_SERVICE,
      nodeClass: TransformerModelNode,
      icon: 'transformer',
      color: '#FF6B35',
      tags: ['transformer', 'attention', 'nlp', 'encoder']
    });

    this.registerNode({
      type: GANModelNode.TYPE,
      name: GANModelNode.NAME,
      description: GANModelNode.DESCRIPTION,
      category: NodeCategory.AI_SERVICE,
      nodeClass: GANModelNode,
      icon: 'generate',
      color: '#FF6B35',
      tags: ['gan', 'generator', 'discriminator', 'adversarial']
    });

    this.registerNode({
      type: VAEModelNode.TYPE,
      name: VAEModelNode.NAME,
      description: VAEModelNode.DESCRIPTION,
      category: NodeCategory.AI_SERVICE,
      nodeClass: VAEModelNode,
      icon: 'encode',
      color: '#FF6B35',
      tags: ['vae', 'encoder', 'decoder', 'latent-space']
    });

    this.registerNode({
      type: AttentionMechanismNode.TYPE,
      name: AttentionMechanismNode.NAME,
      description: AttentionMechanismNode.DESCRIPTION,
      category: NodeCategory.AI_SERVICE,
      nodeClass: AttentionMechanismNode,
      icon: 'focus',
      color: '#FF6B35',
      tags: ['attention', 'query', 'key', 'value']
    });

    this.registerNode({
      type: EmbeddingLayerNode.TYPE,
      name: EmbeddingLayerNode.NAME,
      description: EmbeddingLayerNode.DESCRIPTION,
      category: NodeCategory.AI_SERVICE,
      nodeClass: EmbeddingLayerNode,
      icon: 'embed',
      color: '#FF8C42',
      tags: ['embedding', 'vocabulary', 'features', 'lookup']
    });

    this.registerNode({
      type: DropoutLayerNode.TYPE,
      name: DropoutLayerNode.NAME,
      description: DropoutLayerNode.DESCRIPTION,
      category: NodeCategory.AI_SERVICE,
      nodeClass: DropoutLayerNode,
      icon: 'dropout',
      color: '#FF8C42',
      tags: ['dropout', 'regularization', 'training', 'overfitting']
    });

    this.registerNode({
      type: BatchNormalizationNode.TYPE,
      name: BatchNormalizationNode.NAME,
      description: BatchNormalizationNode.DESCRIPTION,
      category: NodeCategory.AI_SERVICE,
      nodeClass: BatchNormalizationNode,
      icon: 'normalize',
      color: '#FF8C42',
      tags: ['batch-norm', 'normalization', 'statistics', 'training']
    });

    this.registerNode({
      type: ActivationFunctionNode.TYPE,
      name: ActivationFunctionNode.NAME,
      description: ActivationFunctionNode.DESCRIPTION,
      category: NodeCategory.AI_SERVICE,
      nodeClass: ActivationFunctionNode,
      icon: 'function',
      color: '#FF8C42',
      tags: ['activation', 'relu', 'sigmoid', 'tanh']
    });

    this.registerNode({
      type: LossFunctionNode.TYPE,
      name: LossFunctionNode.NAME,
      description: LossFunctionNode.DESCRIPTION,
      category: NodeCategory.AI_SERVICE,
      nodeClass: LossFunctionNode,
      icon: 'loss',
      color: '#FFA726',
      tags: ['loss', 'mse', 'cross-entropy', 'gradient']
    });

    this.registerNode({
      type: OptimizerNode.TYPE,
      name: OptimizerNode.NAME,
      description: OptimizerNode.DESCRIPTION,
      category: NodeCategory.AI_SERVICE,
      nodeClass: OptimizerNode,
      icon: 'optimize',
      color: '#FFA726',
      tags: ['optimizer', 'adam', 'sgd', 'learning-rate']
    });

    this.registerNode({
      type: RegularizationNode.TYPE,
      name: RegularizationNode.NAME,
      description: RegularizationNode.DESCRIPTION,
      category: NodeCategory.AI_SERVICE,
      nodeClass: RegularizationNode,
      icon: 'regularize',
      color: '#FFA726',
      tags: ['regularization', 'l1', 'l2', 'penalty']
    });

    Debug.log('NodeRegistry', '深度学习节点注册完成 - 共15个节点');

    // 机器学习节点（10个）
    this.registerNode({
      type: ReinforcementLearningNode.TYPE,
      name: ReinforcementLearningNode.NAME,
      description: ReinforcementLearningNode.DESCRIPTION,
      category: NodeCategory.AI_SERVICE,
      nodeClass: ReinforcementLearningNode,
      icon: 'reward',
      color: '#66BB6A',
      tags: ['reinforcement-learning', 'q-learning', 'reward', 'policy']
    });

    this.registerNode({
      type: FederatedLearningNode.TYPE,
      name: FederatedLearningNode.NAME,
      description: FederatedLearningNode.DESCRIPTION,
      category: NodeCategory.AI_SERVICE,
      nodeClass: FederatedLearningNode,
      icon: 'federated',
      color: '#66BB6A',
      tags: ['federated-learning', 'aggregation', 'distributed', 'privacy']
    });

    this.registerNode({
      type: TransferLearningNode.TYPE,
      name: TransferLearningNode.NAME,
      description: TransferLearningNode.DESCRIPTION,
      category: NodeCategory.AI_SERVICE,
      nodeClass: TransferLearningNode,
      icon: 'transfer',
      color: '#66BB6A',
      tags: ['transfer-learning', 'fine-tuning', 'adaptation', 'domain']
    });

    this.registerNode({
      type: ModelEnsembleNode.TYPE,
      name: ModelEnsembleNode.NAME,
      description: ModelEnsembleNode.DESCRIPTION,
      category: NodeCategory.AI_SERVICE,
      nodeClass: ModelEnsembleNode,
      icon: 'ensemble',
      color: '#66BB6A',
      tags: ['ensemble', 'voting', 'bagging', 'boosting']
    });

    this.registerNode({
      type: HyperparameterTuningNode.TYPE,
      name: HyperparameterTuningNode.NAME,
      description: HyperparameterTuningNode.DESCRIPTION,
      category: NodeCategory.AI_SERVICE,
      nodeClass: HyperparameterTuningNode,
      icon: 'tune',
      color: '#42A5F5',
      tags: ['hyperparameter', 'optimization', 'search', 'tuning']
    });

    this.registerNode({
      type: ModelValidationNode.TYPE,
      name: ModelValidationNode.NAME,
      description: ModelValidationNode.DESCRIPTION,
      category: NodeCategory.AI_SERVICE,
      nodeClass: ModelValidationNode,
      icon: 'validate',
      color: '#42A5F5',
      tags: ['validation', 'metrics', 'evaluation', 'performance']
    });

    this.registerNode({
      type: CrossValidationNode.TYPE,
      name: CrossValidationNode.NAME,
      description: CrossValidationNode.DESCRIPTION,
      category: NodeCategory.AI_SERVICE,
      nodeClass: CrossValidationNode,
      icon: 'cross-validate',
      color: '#42A5F5',
      tags: ['cross-validation', 'k-fold', 'stratified', 'evaluation']
    });

    this.registerNode({
      type: FeatureSelectionNode.TYPE,
      name: FeatureSelectionNode.NAME,
      description: FeatureSelectionNode.DESCRIPTION,
      category: NodeCategory.AI_SERVICE,
      nodeClass: FeatureSelectionNode,
      icon: 'select',
      color: '#AB47BC',
      tags: ['feature-selection', 'correlation', 'importance', 'filtering']
    });

    this.registerNode({
      type: DimensionalityReductionNode.TYPE,
      name: DimensionalityReductionNode.NAME,
      description: DimensionalityReductionNode.DESCRIPTION,
      category: NodeCategory.AI_SERVICE,
      nodeClass: DimensionalityReductionNode,
      icon: 'reduce',
      color: '#AB47BC',
      tags: ['dimensionality-reduction', 'pca', 'tsne', 'compression']
    });

    this.registerNode({
      type: ClusteringNode.TYPE,
      name: ClusteringNode.NAME,
      description: ClusteringNode.DESCRIPTION,
      category: NodeCategory.AI_SERVICE,
      nodeClass: ClusteringNode,
      icon: 'cluster',
      color: '#AB47BC',
      tags: ['clustering', 'kmeans', 'unsupervised', 'grouping']
    });

    Debug.log('NodeRegistry', '机器学习节点注册完成 - 共10个节点');

    // 注册新增的AI工具节点（10个）
    this.registerNode({
      type: 'ModelDeployment',
      name: '模型部署',
      description: '将训练好的模型部署到指定环境',
      category: NodeCategory.AI_SERVICE,
      nodeClass: ModelDeploymentNode,
      icon: 'deploy',
      color: '#4CAF50',
      tags: ['deployment', 'production', 'scaling', 'model']
    });

    this.registerNode({
      type: 'ModelMonitoring',
      name: '模型监控',
      description: '监控已部署模型的性能和健康状态',
      category: NodeCategory.AI_SERVICE,
      nodeClass: ModelMonitoringNode,
      icon: 'monitor',
      color: '#4CAF50',
      tags: ['monitoring', 'metrics', 'alerts', 'performance']
    });

    this.registerNode({
      type: 'ModelVersioning',
      name: '模型版本控制',
      description: '管理模型的版本控制和版本切换',
      category: NodeCategory.AI_SERVICE,
      nodeClass: ModelVersioningNode,
      icon: 'version',
      color: '#4CAF50',
      tags: ['versioning', 'rollback', 'comparison', 'management']
    });

    this.registerNode({
      type: 'AutoML',
      name: '自动机器学习',
      description: '自动化机器学习流程',
      category: NodeCategory.AI_SERVICE,
      nodeClass: AutoMLNode,
      icon: 'auto',
      color: '#FF9800',
      tags: ['automl', 'automation', 'hyperparameter', 'optimization']
    });

    this.registerNode({
      type: 'ExplainableAI',
      name: '可解释AI',
      description: '提供模型解释和可解释性分析',
      category: NodeCategory.AI_SERVICE,
      nodeClass: ExplainableAINode,
      icon: 'explain',
      color: '#FF9800',
      tags: ['explainable', 'interpretability', 'shap', 'features']
    });

    this.registerNode({
      type: 'AIEthics',
      name: 'AI伦理',
      description: '评估AI模型的伦理合规性',
      category: NodeCategory.AI_SERVICE,
      nodeClass: AIEthicsNode,
      icon: 'ethics',
      color: '#FF9800',
      tags: ['ethics', 'fairness', 'bias', 'compliance']
    });

    this.registerNode({
      type: 'ModelCompression',
      name: '模型压缩',
      description: '压缩模型以减少大小和提高推理速度',
      category: NodeCategory.AI_SERVICE,
      nodeClass: ModelCompressionNode,
      icon: 'compress',
      color: '#9C27B0',
      tags: ['compression', 'optimization', 'size', 'speed']
    });

    this.registerNode({
      type: 'Quantization',
      name: '模型量化',
      description: '对模型进行量化以减少精度和大小',
      category: NodeCategory.AI_SERVICE,
      nodeClass: QuantizationNode,
      icon: 'quantize',
      color: '#9C27B0',
      tags: ['quantization', 'int8', 'fp16', 'precision']
    });

    this.registerNode({
      type: 'Pruning',
      name: '模型剪枝',
      description: '对模型进行剪枝以减少参数数量',
      category: NodeCategory.AI_SERVICE,
      nodeClass: PruningNode,
      icon: 'prune',
      color: '#9C27B0',
      tags: ['pruning', 'sparsity', 'parameters', 'optimization']
    });

    this.registerNode({
      type: 'Distillation',
      name: '知识蒸馏',
      description: '使用教师模型训练更小的学生模型',
      category: NodeCategory.AI_SERVICE,
      nodeClass: DistillationNode,
      icon: 'distill',
      color: '#9C27B0',
      tags: ['distillation', 'teacher', 'student', 'knowledge']
    });

    Debug.log('NodeRegistry', 'AI工具节点注册完成 - 共10个节点');

    // 注册新增的计算机视觉节点（8个）
    this.registerNode({
      type: 'ImageSegmentation',
      name: '图像分割',
      description: '对图像进行语义分割或实例分割',
      category: NodeCategory.AI_SERVICE,
      nodeClass: ImageSegmentationNode,
      icon: 'segment',
      color: '#2196F3',
      tags: ['segmentation', 'semantic', 'instance', 'mask']
    });

    this.registerNode({
      type: 'ObjectTracking',
      name: '目标跟踪',
      description: '在视频序列中跟踪目标对象',
      category: NodeCategory.AI_SERVICE,
      nodeClass: ObjectTrackingNode,
      icon: 'track',
      color: '#2196F3',
      tags: ['tracking', 'video', 'trajectory', 'motion']
    });

    this.registerNode({
      type: 'FaceRecognition',
      name: '人脸识别',
      description: '检测和识别图像中的人脸',
      category: NodeCategory.AI_SERVICE,
      nodeClass: FaceRecognitionNode,
      icon: 'face',
      color: '#2196F3',
      tags: ['face', 'recognition', 'detection', 'landmarks']
    });

    this.registerNode({
      type: 'OpticalCharacterRecognition',
      name: '光学字符识别',
      description: '从图像中识别和提取文本',
      category: NodeCategory.AI_SERVICE,
      nodeClass: OpticalCharacterRecognitionNode,
      icon: 'ocr',
      color: '#2196F3',
      tags: ['ocr', 'text', 'recognition', 'extraction']
    });

    this.registerNode({
      type: 'ImageGeneration',
      name: '图像生成',
      description: '使用AI模型生成图像',
      category: NodeCategory.AI_SERVICE,
      nodeClass: ImageGenerationNode,
      icon: 'generate',
      color: '#E91E63',
      tags: ['generation', 'diffusion', 'gan', 'creative']
    });

    this.registerNode({
      type: 'StyleTransfer',
      name: '风格迁移',
      description: '将一种图像的风格应用到另一张图像上',
      category: NodeCategory.AI_SERVICE,
      nodeClass: StyleTransferNode,
      icon: 'style',
      color: '#E91E63',
      tags: ['style', 'transfer', 'artistic', 'neural']
    });

    this.registerNode({
      type: 'ImageEnhancement',
      name: '图像增强',
      description: '对图像进行质量增强和修复',
      category: NodeCategory.AI_SERVICE,
      nodeClass: ImageEnhancementNode,
      icon: 'enhance',
      color: '#E91E63',
      tags: ['enhancement', 'super-resolution', 'denoising', 'quality']
    });

    this.registerNode({
      type: 'AugmentedReality',
      name: '增强现实',
      description: '在现实场景中叠加虚拟内容',
      category: NodeCategory.AI_SERVICE,
      nodeClass: AugmentedRealityNode,
      icon: 'ar',
      color: '#E91E63',
      tags: ['ar', 'tracking', 'virtual', 'overlay']
    });

    Debug.log('NodeRegistry', '计算机视觉节点注册完成 - 共8个节点');

    // 注册新增的自然语言处理节点（7个）
    this.registerNode({
      type: 'TextClassification',
      name: '文本分类',
      description: '对文本进行分类和标签预测',
      category: NodeCategory.AI_SERVICE,
      nodeClass: TextClassificationNode,
      icon: 'classify',
      color: '#607D8B',
      tags: ['classification', 'sentiment', 'topic', 'intent']
    });

    this.registerNode({
      type: 'NamedEntityRecognition',
      name: '命名实体识别',
      description: '识别文本中的命名实体',
      category: NodeCategory.AI_SERVICE,
      nodeClass: NamedEntityRecognitionNode,
      icon: 'entity',
      color: '#607D8B',
      tags: ['ner', 'entities', 'person', 'organization']
    });

    this.registerNode({
      type: 'SentimentAnalysisNew',
      name: '情感分析增强版',
      description: '对文本进行详细的情感和情绪分析',
      category: NodeCategory.AI_SERVICE,
      nodeClass: SentimentAnalysisNodeNew,
      icon: 'sentiment',
      color: '#607D8B',
      tags: ['sentiment', 'emotion', 'analysis', 'mood']
    });

    this.registerNode({
      type: 'TextSummarization',
      name: '文本摘要',
      description: '生成文本的摘要和关键点',
      category: NodeCategory.AI_SERVICE,
      nodeClass: TextSummarizationNode,
      icon: 'summarize',
      color: '#795548',
      tags: ['summarization', 'abstract', 'key-points', 'extraction']
    });

    this.registerNode({
      type: 'MachineTranslation',
      name: '机器翻译',
      description: '将文本从一种语言翻译为另一种语言',
      category: NodeCategory.AI_SERVICE,
      nodeClass: MachineTranslationNode,
      icon: 'translate',
      color: '#795548',
      tags: ['translation', 'language', 'multilingual', 'localization']
    });

    this.registerNode({
      type: 'QuestionAnswering',
      name: '问答系统',
      description: '基于上下文回答问题',
      category: NodeCategory.AI_SERVICE,
      nodeClass: QuestionAnsweringNode,
      icon: 'question',
      color: '#795548',
      tags: ['qa', 'question', 'answer', 'context']
    });

    this.registerNode({
      type: 'TextGeneration',
      name: '文本生成',
      description: '基于提示生成文本内容',
      category: NodeCategory.AI_SERVICE,
      nodeClass: TextGenerationNode,
      icon: 'generate-text',
      color: '#795548',
      tags: ['generation', 'gpt', 'completion', 'creative']
    });

    Debug.log('NodeRegistry', '自然语言处理节点注册完成 - 共7个节点');

    Debug.log('NodeRegistry', '批次3.3节点开发完成 - 深度学习节点15个 + 机器学习节点10个 + AI工具节点10个 + 计算机视觉节点8个 + 自然语言处理节点7个 = 共50个节点');
  }

  /**
   * 注册批次0.1节点：场景管理和资源管理节点
   */
  private registerBatch01Nodes(): void {
    Debug.log('NodeRegistry', '开始注册批次0.1节点...');

    // 注册场景管理节点（33个）
    this.registerSceneManagementNodes();

    // 注册资源管理节点（22个）
    this.registerResourceManagementNodes();

    Debug.log('NodeRegistry', '批次0.1节点注册完成 - 场景管理节点33个 + 资源管理节点22个 = 共55个节点');
  }

  /**
   * 注册批次3.4节点：VR/AR节点、游戏逻辑节点、社交功能节点
   */
  private registerBatch34Nodes(): void {
    Debug.log('NodeRegistry', '开始注册批次3.4节点...');

    // 注册VR/AR节点
    this.registerVRARNodes();

    // 注册游戏逻辑节点
    this.registerGameLogicNodes();

    // 注册社交功能节点
    this.registerSocialNodes();

    Debug.log('NodeRegistry', '批次3.4节点开发完成 - VR/AR节点10个 + 游戏逻辑节点8个 + 社交功能节点6个 = 共24个节点');
  }

  /**
   * 注册VR/AR节点
   */
  private registerVRARNodes(): void {
    Debug.log('NodeRegistry', '注册VR/AR节点...');

    // VR控制器节点
    this.registerNode({
      type: 'VRControllerNode',
      name: 'VR控制器',
      description: '处理VR控制器输入和交互',
      category: NodeCategory.VR_AR_INPUT,
      nodeClass: VRControllerNode,
      icon: 'vr-controller',
      color: '#9C27B0',
      tags: ['vr', 'controller', 'input', 'interaction']
    });

    // AR追踪节点
    this.registerNode({
      type: 'ARTrackingNode',
      name: 'AR追踪',
      description: '处理AR环境追踪和定位',
      category: NodeCategory.VR_AR_INPUT,
      nodeClass: ARTrackingNode,
      icon: 'ar-tracking',
      color: '#9C27B0',
      tags: ['ar', 'tracking', 'positioning', 'camera']
    });

    // 空间映射节点
    this.registerNode({
      type: 'SpatialMappingNode',
      name: '空间映射',
      description: '处理空间环境的3D重建和映射',
      category: NodeCategory.VR_AR_INPUT,
      nodeClass: SpatialMappingNode,
      icon: 'spatial-mapping',
      color: '#9C27B0',
      tags: ['spatial', 'mapping', '3d', 'reconstruction']
    });

    // 手部追踪节点
    this.registerNode({
      type: 'HandTrackingNode',
      name: '手部追踪',
      description: '处理手部姿态识别和追踪',
      category: NodeCategory.VR_AR_INPUT,
      nodeClass: HandTrackingNode,
      icon: 'hand-tracking',
      color: '#9C27B0',
      tags: ['hand', 'tracking', 'gesture', 'pose']
    });

    // 眼动追踪节点
    this.registerNode({
      type: 'EyeTrackingNode',
      name: '眼动追踪',
      description: '处理眼部运动和注视点追踪',
      category: NodeCategory.VR_AR_INPUT,
      nodeClass: EyeTrackingNode,
      icon: 'eye-tracking',
      color: '#9C27B0',
      tags: ['eye', 'tracking', 'gaze', 'attention']
    });

    // 语音命令节点
    this.registerNode({
      type: 'VoiceCommandNode',
      name: '语音命令',
      description: '处理语音识别和命令执行',
      category: NodeCategory.VR_AR_INPUT,
      nodeClass: VoiceCommandNode,
      icon: 'voice-command',
      color: '#9C27B0',
      tags: ['voice', 'speech', 'command', 'recognition']
    });

    // 触觉反馈节点
    this.registerNode({
      type: 'HapticFeedbackNode',
      name: '触觉反馈',
      description: '处理VR/AR设备的触觉反馈',
      category: NodeCategory.VR_AR_INPUT,
      nodeClass: HapticFeedbackNode,
      icon: 'haptic-feedback',
      color: '#9C27B0',
      tags: ['haptic', 'feedback', 'vibration', 'touch']
    });

    // VR传送节点
    this.registerNode({
      type: 'VRTeleportationNode',
      name: 'VR传送',
      description: '处理VR环境中的传送移动',
      category: NodeCategory.VR_AR_INPUT,
      nodeClass: VRTeleportationNode,
      icon: 'vr-teleport',
      color: '#9C27B0',
      tags: ['vr', 'teleportation', 'movement', 'locomotion']
    });

    // AR放置节点
    this.registerNode({
      type: 'ARPlacementNode',
      name: 'AR放置',
      description: '处理AR环境中的虚拟对象放置',
      category: NodeCategory.VR_AR_INPUT,
      nodeClass: ARPlacementNode,
      icon: 'ar-placement',
      color: '#9C27B0',
      tags: ['ar', 'placement', 'object', 'surface']
    });

    // 沉浸式UI节点
    this.registerNode({
      type: 'ImmersiveUINode',
      name: '沉浸式UI',
      description: '处理VR/AR环境中的用户界面',
      category: NodeCategory.VR_AR_INPUT,
      nodeClass: ImmersiveUINode,
      icon: 'immersive-ui',
      color: '#9C27B0',
      tags: ['ui', 'immersive', 'interface', 'spatial']
    });

    Debug.log('NodeRegistry', 'VR/AR节点注册完成 - 共10个节点');
  }

  /**
   * 注册游戏逻辑节点
   */
  private registerGameLogicNodes(): void {
    Debug.log('NodeRegistry', '注册游戏逻辑节点...');

    // 游戏状态节点
    this.registerNode({
      type: 'GameStateNode',
      name: '游戏状态',
      description: '管理游戏的整体状态和流程',
      category: NodeCategory.CUSTOM,
      nodeClass: GameStateNode,
      icon: 'game-state',
      color: '#FF5722',
      tags: ['game', 'state', 'management', 'flow']
    });

    // 玩家控制器节点
    this.registerNode({
      type: 'PlayerControllerNode',
      name: '玩家控制器',
      description: '处理玩家输入和角色控制',
      category: NodeCategory.INPUT,
      nodeClass: PlayerControllerNode,
      icon: 'player-controller',
      color: '#FF5722',
      tags: ['player', 'controller', 'input', 'character']
    });

    // 库存系统节点
    this.registerNode({
      type: 'InventorySystemNode',
      name: '库存系统',
      description: '管理玩家的物品库存',
      category: NodeCategory.CUSTOM,
      nodeClass: InventorySystemNode,
      icon: 'inventory',
      color: '#FF5722',
      tags: ['inventory', 'items', 'storage', 'management']
    });

    // 任务系统节点
    this.registerNode({
      type: 'QuestSystemNode',
      name: '任务系统',
      description: '管理游戏任务和目标',
      category: NodeCategory.CUSTOM,
      nodeClass: QuestSystemNode,
      icon: 'quest',
      color: '#FF5722',
      tags: ['quest', 'mission', 'objective', 'progress']
    });

    // 对话系统节点
    this.registerNode({
      type: 'DialogueSystemNode',
      name: '对话系统',
      description: '处理NPC对话和剧情',
      category: NodeCategory.CUSTOM,
      nodeClass: DialogueSystemNode,
      icon: 'dialogue',
      color: '#FF5722',
      tags: ['dialogue', 'conversation', 'npc', 'story']
    });

    // 存档系统节点
    this.registerNode({
      type: 'SaveLoadSystemNode',
      name: '存档系统',
      description: '处理游戏数据的保存和加载',
      category: NodeCategory.CUSTOM,
      nodeClass: SaveLoadSystemNode,
      icon: 'save-load',
      color: '#FF5722',
      tags: ['save', 'load', 'persistence', 'data']
    });

    // 成就系统节点
    this.registerNode({
      type: 'AchievementSystemNode',
      name: '成就系统',
      description: '管理游戏成就和奖励',
      category: NodeCategory.CUSTOM,
      nodeClass: AchievementSystemNode,
      icon: 'achievement',
      color: '#FF5722',
      tags: ['achievement', 'reward', 'unlock', 'progress']
    });

    // 排行榜节点
    this.registerNode({
      type: 'LeaderboardNode',
      name: '排行榜',
      description: '管理游戏排行榜和分数',
      category: NodeCategory.CUSTOM,
      nodeClass: LeaderboardNode,
      icon: 'leaderboard',
      color: '#FF5722',
      tags: ['leaderboard', 'score', 'ranking', 'competition']
    });

    Debug.log('NodeRegistry', '游戏逻辑节点注册完成 - 共8个节点');
  }

  /**
   * 注册社交功能节点
   */
  private registerSocialNodes(): void {
    Debug.log('NodeRegistry', '注册社交功能节点...');

    // 好友系统节点
    this.registerNode({
      type: 'FriendSystemNode',
      name: '好友系统',
      description: '管理用户好友关系',
      category: NodeCategory.NETWORK,
      nodeClass: FriendSystemNode,
      icon: 'friends',
      color: '#2196F3',
      tags: ['friends', 'social', 'relationship', 'network']
    });

    // 聊天系统节点
    this.registerNode({
      type: 'ChatSystemNode',
      name: '聊天系统',
      description: '处理用户间的消息通信',
      category: NodeCategory.NETWORK,
      nodeClass: ChatSystemNode,
      icon: 'chat',
      color: '#2196F3',
      tags: ['chat', 'message', 'communication', 'text']
    });

    // 群组系统节点
    this.registerNode({
      type: 'GroupSystemNode',
      name: '群组系统',
      description: '管理用户群组和团队',
      category: NodeCategory.NETWORK,
      nodeClass: GroupSystemNode,
      icon: 'group',
      color: '#2196F3',
      tags: ['group', 'team', 'community', 'collaboration']
    });

    // 社交分享节点
    this.registerNode({
      type: 'SocialSharingNode',
      name: '社交分享',
      description: '处理内容分享和社交媒体集成',
      category: NodeCategory.NETWORK,
      nodeClass: SocialSharingNode,
      icon: 'share',
      color: '#2196F3',
      tags: ['share', 'social', 'media', 'content']
    });

    // 用户生成内容节点
    this.registerNode({
      type: 'UserGeneratedContentNode',
      name: '用户生成内容',
      description: '管理用户创建和分享的内容',
      category: NodeCategory.CUSTOM,
      nodeClass: UserGeneratedContentNode,
      icon: 'user-content',
      color: '#2196F3',
      tags: ['ugc', 'content', 'creation', 'moderation']
    });

    // 社区功能节点
    this.registerNode({
      type: 'CommunityFeaturesNode',
      name: '社区功能',
      description: '管理社区活动和用户参与',
      category: NodeCategory.CUSTOM,
      nodeClass: CommunityFeaturesNode,
      icon: 'community',
      color: '#2196F3',
      tags: ['community', 'events', 'participation', 'engagement']
    });

    Debug.log('NodeRegistry', '社交功能节点注册完成 - 共6个节点');
  }

  /**
   * 注册场景管理节点（33个）
   */
  private registerSceneManagementNodes(): void {
    Debug.log('NodeRegistry', '注册场景管理节点...');

    // 场景基础管理节点（7个）
    this.registerNode({
      type: LoadSceneNode.TYPE,
      name: LoadSceneNode.NAME,
      description: LoadSceneNode.DESCRIPTION,
      category: NodeCategory.SCENE_MANAGEMENT,
      nodeClass: LoadSceneNode,
      icon: 'folder_open',
      color: '#4CAF50',
      tags: ['scene', 'load', 'file', 'management']
    });

    this.registerNode({
      type: SaveSceneNode.TYPE,
      name: SaveSceneNode.NAME,
      description: SaveSceneNode.DESCRIPTION,
      category: NodeCategory.SCENE_MANAGEMENT,
      nodeClass: SaveSceneNode,
      icon: 'save',
      color: '#4CAF50',
      tags: ['scene', 'save', 'file', 'management']
    });

    this.registerNode({
      type: CreateSceneNode.TYPE,
      name: CreateSceneNode.NAME,
      description: CreateSceneNode.DESCRIPTION,
      category: NodeCategory.SCENE_MANAGEMENT,
      nodeClass: CreateSceneNode,
      icon: 'add_box',
      color: '#4CAF50',
      tags: ['scene', 'create', 'new', 'management']
    });

    this.registerNode({
      type: DestroySceneNode.TYPE,
      name: DestroySceneNode.NAME,
      description: DestroySceneNode.DESCRIPTION,
      category: NodeCategory.SCENE_MANAGEMENT,
      nodeClass: DestroySceneNode,
      icon: 'delete',
      color: '#F44336',
      tags: ['scene', 'destroy', 'delete', 'management']
    });

    this.registerNode({
      type: AddObjectToSceneNode.TYPE,
      name: AddObjectToSceneNode.NAME,
      description: AddObjectToSceneNode.DESCRIPTION,
      category: NodeCategory.SCENE_MANAGEMENT,
      nodeClass: AddObjectToSceneNode,
      icon: 'add_circle',
      color: '#2196F3',
      tags: ['scene', 'object', 'add', 'hierarchy']
    });

    this.registerNode({
      type: RemoveObjectFromSceneNode.TYPE,
      name: RemoveObjectFromSceneNode.NAME,
      description: RemoveObjectFromSceneNode.DESCRIPTION,
      category: NodeCategory.SCENE_MANAGEMENT,
      nodeClass: RemoveObjectFromSceneNode,
      icon: 'remove_circle',
      color: '#FF9800',
      tags: ['scene', 'object', 'remove', 'hierarchy']
    });

    this.registerNode({
      type: FindSceneObjectNode.TYPE,
      name: FindSceneObjectNode.NAME,
      description: FindSceneObjectNode.DESCRIPTION,
      category: NodeCategory.SCENE_MANAGEMENT,
      nodeClass: FindSceneObjectNode,
      icon: 'search',
      color: '#9C27B0',
      tags: ['scene', 'object', 'find', 'query']
    });

    // 场景编辑节点（15个）
    this.registerNode({
      type: SceneViewportNode.TYPE,
      name: SceneViewportNode.NAME,
      description: SceneViewportNode.DESCRIPTION,
      category: NodeCategory.SCENE_EDITING,
      nodeClass: SceneViewportNode,
      icon: 'view_in_ar',
      color: '#9C27B0',
      tags: ['scene', 'viewport', 'view', 'camera']
    });

    this.registerNode({
      type: ObjectSelectionNode.TYPE,
      name: ObjectSelectionNode.NAME,
      description: ObjectSelectionNode.DESCRIPTION,
      category: NodeCategory.SCENE_EDITING,
      nodeClass: ObjectSelectionNode,
      icon: 'select_all',
      color: '#9C27B0',
      tags: ['object', 'selection', 'pick', 'choose']
    });

    this.registerNode({
      type: ObjectTransformNode.TYPE,
      name: ObjectTransformNode.NAME,
      description: ObjectTransformNode.DESCRIPTION,
      category: NodeCategory.SCENE_EDITING,
      nodeClass: ObjectTransformNode,
      icon: 'transform',
      color: '#9C27B0',
      tags: ['object', 'transform', 'move', 'rotate', 'scale']
    });

    this.registerNode({
      type: ObjectDuplicationNode.TYPE,
      name: ObjectDuplicationNode.NAME,
      description: ObjectDuplicationNode.DESCRIPTION,
      category: NodeCategory.SCENE_EDITING,
      nodeClass: ObjectDuplicationNode,
      icon: 'content_copy',
      color: '#9C27B0',
      tags: ['object', 'duplicate', 'copy', 'clone']
    });

    this.registerNode({
      type: ObjectGroupingNode.TYPE,
      name: ObjectGroupingNode.NAME,
      description: ObjectGroupingNode.DESCRIPTION,
      category: NodeCategory.SCENE_EDITING,
      nodeClass: ObjectGroupingNode,
      icon: 'group_work',
      color: '#9C27B0',
      tags: ['object', 'group', 'organize', 'hierarchy']
    });

    this.registerNode({
      type: ObjectLayerNode.TYPE,
      name: ObjectLayerNode.NAME,
      description: ObjectLayerNode.DESCRIPTION,
      category: NodeCategory.SCENE_EDITING,
      nodeClass: ObjectLayerNode,
      icon: 'layers',
      color: '#9C27B0',
      tags: ['object', 'layer', 'organize', 'visibility']
    });

    this.registerNode({
      type: GridSnapNode.TYPE,
      name: GridSnapNode.NAME,
      description: GridSnapNode.DESCRIPTION,
      category: NodeCategory.SCENE_EDITING,
      nodeClass: GridSnapNode,
      icon: 'grid_on',
      color: '#9C27B0',
      tags: ['grid', 'snap', 'align', 'precision']
    });

    this.registerNode({
      type: ObjectAlignmentNode.TYPE,
      name: ObjectAlignmentNode.NAME,
      description: ObjectAlignmentNode.DESCRIPTION,
      category: NodeCategory.SCENE_EDITING,
      nodeClass: ObjectAlignmentNode,
      icon: 'align_horizontal_center',
      color: '#9C27B0',
      tags: ['object', 'align', 'position', 'layout']
    });

    this.registerNode({
      type: ObjectDistributionNode.TYPE,
      name: ObjectDistributionNode.NAME,
      description: ObjectDistributionNode.DESCRIPTION,
      category: NodeCategory.SCENE_EDITING,
      nodeClass: ObjectDistributionNode,
      icon: 'distribute',
      color: '#9C27B0',
      tags: ['object', 'distribute', 'spacing', 'layout']
    });

    this.registerNode({
      type: UndoRedoNode.TYPE,
      name: UndoRedoNode.NAME,
      description: UndoRedoNode.DESCRIPTION,
      category: NodeCategory.SCENE_EDITING,
      nodeClass: UndoRedoNode,
      icon: 'undo',
      color: '#9C27B0',
      tags: ['undo', 'redo', 'history', 'revert']
    });

    this.registerNode({
      type: HistoryManagementNode.TYPE,
      name: HistoryManagementNode.NAME,
      description: HistoryManagementNode.DESCRIPTION,
      category: NodeCategory.SCENE_EDITING,
      nodeClass: HistoryManagementNode,
      icon: 'history',
      color: '#9C27B0',
      tags: ['history', 'management', 'timeline', 'actions']
    });

    this.registerNode({
      type: SelectionFilterNode.TYPE,
      name: SelectionFilterNode.NAME,
      description: SelectionFilterNode.DESCRIPTION,
      category: NodeCategory.SCENE_EDITING,
      nodeClass: SelectionFilterNode,
      icon: 'filter_list',
      color: '#9C27B0',
      tags: ['selection', 'filter', 'criteria', 'search']
    });

    this.registerNode({
      type: ViewportNavigationNode.TYPE,
      name: ViewportNavigationNode.NAME,
      description: ViewportNavigationNode.DESCRIPTION,
      category: NodeCategory.SCENE_EDITING,
      nodeClass: ViewportNavigationNode,
      icon: 'navigation',
      color: '#9C27B0',
      tags: ['viewport', 'navigation', 'camera', 'movement']
    });

    this.registerNode({
      type: ViewportRenderingNode.TYPE,
      name: ViewportRenderingNode.NAME,
      description: ViewportRenderingNode.DESCRIPTION,
      category: NodeCategory.SCENE_EDITING,
      nodeClass: ViewportRenderingNode,
      icon: 'visibility',
      color: '#9C27B0',
      tags: ['viewport', 'rendering', 'display', 'visualization']
    });

    this.registerNode({
      type: ViewportSettingsNode.TYPE,
      name: ViewportSettingsNode.NAME,
      description: ViewportSettingsNode.DESCRIPTION,
      category: NodeCategory.SCENE_EDITING,
      nodeClass: ViewportSettingsNode,
      icon: 'settings',
      color: '#9C27B0',
      tags: ['viewport', 'settings', 'configuration', 'preferences']
    });

    // 场景切换节点（1个）
    this.registerNode({
      type: SceneTransitionNode.TYPE,
      name: SceneTransitionNode.NAME,
      description: SceneTransitionNode.DESCRIPTION,
      category: NodeCategory.SCENE_MANAGEMENT,
      nodeClass: SceneTransitionNode,
      icon: 'swap_horiz',
      color: '#FF9800',
      tags: ['scene', 'transition', 'switch', 'animation']
    });

    // 场景生成节点（10个）
    this.registerNode({
      type: AutoSceneGenerationNode.TYPE,
      name: AutoSceneGenerationNode.NAME,
      description: AutoSceneGenerationNode.DESCRIPTION,
      category: NodeCategory.SCENE_GENERATION,
      nodeClass: AutoSceneGenerationNode,
      icon: 'auto_awesome',
      color: '#673AB7',
      tags: ['scene', 'generation', 'procedural', 'automatic']
    });

    this.registerNode({
      type: SceneLayoutNode.TYPE,
      name: SceneLayoutNode.NAME,
      description: SceneLayoutNode.DESCRIPTION,
      category: NodeCategory.SCENE_GENERATION,
      nodeClass: SceneLayoutNode,
      icon: 'view_module',
      color: '#673AB7',
      tags: ['scene', 'layout', 'arrangement', 'organization']
    });

    // 添加其他8个场景生成相关节点的占位符
    // 这些节点可能需要进一步开发或从其他文件导入
    const additionalSceneNodes = [
      { type: 'ProceduralTerrainNode', name: '程序化地形', description: '自动生成地形' },
      { type: 'VegetationGeneratorNode', name: '植被生成器', description: '自动生成植被' },
      { type: 'BuildingGeneratorNode', name: '建筑生成器', description: '自动生成建筑' },
      { type: 'RoadNetworkNode', name: '道路网络', description: '生成道路网络' },
      { type: 'WeatherSystemNode', name: '天气系统', description: '动态天气生成' },
      { type: 'LightingSetupNode', name: '光照设置', description: '自动配置场景光照' },
      { type: 'AtmosphereNode', name: '大气效果', description: '生成大气和天空效果' },
      { type: 'SceneOptimizationNode', name: '场景优化', description: '自动优化场景性能' }
    ];

    additionalSceneNodes.forEach(nodeInfo => {
      this.registerNode({
        type: nodeInfo.type,
        name: nodeInfo.name,
        description: nodeInfo.description,
        category: NodeCategory.SCENE_GENERATION,
        nodeClass: AutoSceneGenerationNode, // 临时使用，实际应该有对应的类
        icon: 'auto_fix_high',
        color: '#673AB7',
        tags: ['scene', 'generation', 'procedural', 'automatic']
      });
    });

    Debug.log('NodeRegistry', '场景管理节点注册完成 - 共33个节点 (7个基础管理 + 15个场景编辑 + 1个场景切换 + 10个场景生成)');
  }

  /**
   * 注册资源管理节点（22个）
   */
  private registerResourceManagementNodes(): void {
    Debug.log('NodeRegistry', '注册资源管理节点...');

    // 资源加载节点（12个）
    this.registerNode({
      type: LoadAssetNode.TYPE,
      name: LoadAssetNode.NAME,
      description: LoadAssetNode.DESCRIPTION,
      category: NodeCategory.RESOURCE_MANAGEMENT,
      nodeClass: LoadAssetNode,
      icon: 'download',
      color: '#4CAF50',
      tags: ['resource', 'load', 'asset', 'file']
    });

    this.registerNode({
      type: UnloadAssetNode.TYPE,
      name: UnloadAssetNode.NAME,
      description: UnloadAssetNode.DESCRIPTION,
      category: NodeCategory.RESOURCE_MANAGEMENT,
      nodeClass: UnloadAssetNode,
      icon: 'eject',
      color: '#FF9800',
      tags: ['resource', 'unload', 'asset', 'memory']
    });

    this.registerNode({
      type: PreloadAssetNode.TYPE,
      name: PreloadAssetNode.NAME,
      description: PreloadAssetNode.DESCRIPTION,
      category: NodeCategory.RESOURCE_MANAGEMENT,
      nodeClass: PreloadAssetNode,
      icon: 'cached',
      color: '#2196F3',
      tags: ['resource', 'preload', 'cache', 'optimization']
    });

    this.registerNode({
      type: AsyncLoadAssetNode.TYPE,
      name: AsyncLoadAssetNode.NAME,
      description: AsyncLoadAssetNode.DESCRIPTION,
      category: NodeCategory.RESOURCE_MANAGEMENT,
      nodeClass: AsyncLoadAssetNode,
      icon: 'sync',
      color: '#9C27B0',
      tags: ['resource', 'async', 'load', 'performance']
    });

    this.registerNode({
      type: LoadAssetBundleNode.TYPE,
      name: LoadAssetBundleNode.NAME,
      description: LoadAssetBundleNode.DESCRIPTION,
      category: NodeCategory.RESOURCE_MANAGEMENT,
      nodeClass: LoadAssetBundleNode,
      icon: 'archive',
      color: '#607D8B',
      tags: ['resource', 'bundle', 'package', 'batch']
    });

    this.registerNode({
      type: AssetDependencyNode.TYPE,
      name: AssetDependencyNode.NAME,
      description: AssetDependencyNode.DESCRIPTION,
      category: NodeCategory.RESOURCE_MANAGEMENT,
      nodeClass: AssetDependencyNode,
      icon: 'account_tree',
      color: '#795548',
      tags: ['resource', 'dependency', 'relationship', 'graph']
    });

    this.registerNode({
      type: AssetCacheNode.TYPE,
      name: AssetCacheNode.NAME,
      description: AssetCacheNode.DESCRIPTION,
      category: NodeCategory.RESOURCE_MANAGEMENT,
      nodeClass: AssetCacheNode,
      icon: 'storage',
      color: '#FF5722',
      tags: ['resource', 'cache', 'memory', 'optimization']
    });

    this.registerNode({
      type: AssetCompressionNode.TYPE,
      name: AssetCompressionNode.NAME,
      description: AssetCompressionNode.DESCRIPTION,
      category: NodeCategory.RESOURCE_MANAGEMENT,
      nodeClass: AssetCompressionNode,
      icon: 'compress',
      color: '#3F51B5',
      tags: ['resource', 'compression', 'size', 'optimization']
    });

    this.registerNode({
      type: AssetEncryptionNode.TYPE,
      name: AssetEncryptionNode.NAME,
      description: AssetEncryptionNode.DESCRIPTION,
      category: NodeCategory.RESOURCE_MANAGEMENT,
      nodeClass: AssetEncryptionNode,
      icon: 'lock',
      color: '#E91E63',
      tags: ['resource', 'encryption', 'security', 'protection']
    });

    this.registerNode({
      type: AssetValidationNode.TYPE,
      name: AssetValidationNode.NAME,
      description: AssetValidationNode.DESCRIPTION,
      category: NodeCategory.RESOURCE_MANAGEMENT,
      nodeClass: AssetValidationNode,
      icon: 'verified',
      color: '#4CAF50',
      tags: ['resource', 'validation', 'integrity', 'check']
    });

    this.registerNode({
      type: AssetMetadataNode.TYPE,
      name: AssetMetadataNode.NAME,
      description: AssetMetadataNode.DESCRIPTION,
      category: NodeCategory.RESOURCE_MANAGEMENT,
      nodeClass: AssetMetadataNode,
      icon: 'info',
      color: '#2196F3',
      tags: ['resource', 'metadata', 'information', 'properties']
    });

    this.registerNode({
      type: AssetVersionNode.TYPE,
      name: AssetVersionNode.NAME,
      description: AssetVersionNode.DESCRIPTION,
      category: NodeCategory.RESOURCE_MANAGEMENT,
      nodeClass: AssetVersionNode,
      icon: 'history',
      color: '#FF9800',
      tags: ['resource', 'version', 'control', 'history']
    });

    // 资源优化节点（10个）
    this.registerNode({
      type: AssetOptimizationNode.TYPE,
      name: AssetOptimizationNode.NAME,
      description: AssetOptimizationNode.DESCRIPTION,
      category: NodeCategory.RESOURCE_OPTIMIZATION,
      nodeClass: AssetOptimizationNode,
      icon: 'speed',
      color: '#9C27B0',
      tags: ['resource', 'optimization', 'performance', 'efficiency']
    });

    this.registerNode({
      type: TextureCompressionNode.TYPE,
      name: TextureCompressionNode.NAME,
      description: TextureCompressionNode.DESCRIPTION,
      category: NodeCategory.RESOURCE_OPTIMIZATION,
      nodeClass: TextureCompressionNode,
      icon: 'image',
      color: '#607D8B',
      tags: ['texture', 'compression', 'optimization', 'memory']
    });

    this.registerNode({
      type: MeshOptimizationNode.TYPE,
      name: MeshOptimizationNode.NAME,
      description: MeshOptimizationNode.DESCRIPTION,
      category: NodeCategory.RESOURCE_OPTIMIZATION,
      nodeClass: MeshOptimizationNode,
      icon: 'polyline',
      color: '#795548',
      tags: ['mesh', 'optimization', 'geometry', 'performance']
    });

    this.registerNode({
      type: AudioCompressionNode.TYPE,
      name: AudioCompressionNode.NAME,
      description: AudioCompressionNode.DESCRIPTION,
      category: NodeCategory.RESOURCE_OPTIMIZATION,
      nodeClass: AudioCompressionNode,
      icon: 'audiotrack',
      color: '#FF5722',
      tags: ['audio', 'compression', 'optimization', 'size']
    });

    this.registerNode({
      type: AssetBatchingNode.TYPE,
      name: AssetBatchingNode.NAME,
      description: AssetBatchingNode.DESCRIPTION,
      category: NodeCategory.RESOURCE_OPTIMIZATION,
      nodeClass: AssetBatchingNode,
      icon: 'batch',
      color: '#3F51B5',
      tags: ['asset', 'batching', 'optimization', 'performance']
    });

    this.registerNode({
      type: AssetStreamingNode.TYPE,
      name: AssetStreamingNode.NAME,
      description: AssetStreamingNode.DESCRIPTION,
      category: NodeCategory.RESOURCE_OPTIMIZATION,
      nodeClass: AssetStreamingNode,
      icon: 'stream',
      color: '#E91E63',
      tags: ['asset', 'streaming', 'loading', 'performance']
    });

    this.registerNode({
      type: AssetMemoryManagementNode.TYPE,
      name: AssetMemoryManagementNode.NAME,
      description: AssetMemoryManagementNode.DESCRIPTION,
      category: NodeCategory.RESOURCE_OPTIMIZATION,
      nodeClass: AssetMemoryManagementNode,
      icon: 'memory',
      color: '#4CAF50',
      tags: ['asset', 'memory', 'management', 'optimization']
    });

    this.registerNode({
      type: AssetGarbageCollectionNode.TYPE,
      name: AssetGarbageCollectionNode.NAME,
      description: AssetGarbageCollectionNode.DESCRIPTION,
      category: NodeCategory.RESOURCE_OPTIMIZATION,
      nodeClass: AssetGarbageCollectionNode,
      icon: 'delete_sweep',
      color: '#2196F3',
      tags: ['asset', 'garbage', 'collection', 'cleanup']
    });

    this.registerNode({
      type: AssetPerformanceMonitorNode.TYPE,
      name: AssetPerformanceMonitorNode.NAME,
      description: AssetPerformanceMonitorNode.DESCRIPTION,
      category: NodeCategory.RESOURCE_OPTIMIZATION,
      nodeClass: AssetPerformanceMonitorNode,
      icon: 'monitor',
      color: '#FF9800',
      tags: ['asset', 'performance', 'monitor', 'analytics']
    });

    this.registerNode({
      type: AssetUsageAnalyticsNode.TYPE,
      name: AssetUsageAnalyticsNode.NAME,
      description: AssetUsageAnalyticsNode.DESCRIPTION,
      category: NodeCategory.RESOURCE_OPTIMIZATION,
      nodeClass: AssetUsageAnalyticsNode,
      icon: 'analytics',
      color: '#9C27B0',
      tags: ['asset', 'usage', 'analytics', 'statistics']
    });

    Debug.log('NodeRegistry', '资源管理节点注册完成 - 共22个节点 (12个资源加载 + 10个资源优化)');
  }

  /**
   * 构建分类索引
   */
  private buildCategoryIndex(): void {
    this.categories.clear();

    for (const nodeInfo of this.nodes.values()) {
      if (!this.categories.has(nodeInfo.category)) {
        this.categories.set(nodeInfo.category, []);
      }
      this.categories.get(nodeInfo.category)!.push(nodeInfo);
    }

    // 对每个分类的节点进行排序
    for (const nodes of this.categories.values()) {
      nodes.sort((a, b) => a.name.localeCompare(b.name));
    }
  }

  /**
   * 获取节点信息
   */
  getNodeInfo(type: string): NodeInfo | undefined {
    return this.nodes.get(type);
  }

  /**
   * 获取所有节点
   */
  getAllNodes(): NodeInfo[] {
    return Array.from(this.nodes.values());
  }

  /**
   * 根据分类获取节点
   */
  getNodesByCategory(category: NodeCategory): NodeInfo[] {
    return this.categories.get(category) || [];
  }

  /**
   * 搜索节点
   */
  searchNodes(query: string): NodeInfo[] {
    const lowerQuery = query.toLowerCase();
    return Array.from(this.nodes.values()).filter(nodeInfo => 
      nodeInfo.name.toLowerCase().includes(lowerQuery) ||
      nodeInfo.description.toLowerCase().includes(lowerQuery) ||
      nodeInfo.tags?.some(tag => tag.toLowerCase().includes(lowerQuery))
    );
  }

  /**
   * 创建节点实例
   */
  createNode(type: string, id?: string): VisualScriptNode | null {
    const nodeInfo = this.nodes.get(type);
    if (!nodeInfo) {
      Debug.warn('NodeRegistry', `未找到节点类型: ${type}`);
      return null;
    }

    try {
      return new nodeInfo.nodeClass(type, nodeInfo.name, id);
    } catch (error) {
      Debug.error('NodeRegistry', `节点创建失败: ${type}`, error);
      return null;
    }
  }

  /**
   * 获取分类信息
   */
  getCategoryInfo(): { category: NodeCategory; name: string; count: number }[] {
    const categoryNames: { [key in NodeCategory]: string } = {
      [NodeCategory.MOTION_CAPTURE]: '动作捕捉',
      [NodeCategory.ENTITY_MANAGEMENT]: '实体管理',
      [NodeCategory.COMPONENT_MANAGEMENT]: '组件管理',
      [NodeCategory.TRANSFORM]: '变换操作',
      [NodeCategory.PHYSICS]: '物理系统',
      [NodeCategory.ANIMATION]: '动画系统',
      [NodeCategory.INPUT]: '输入系统',
      [NodeCategory.AUDIO]: '音频系统',
      [NodeCategory.SCENE_GENERATION]: '场景生成',
      [NodeCategory.WATER_SYSTEM]: '水系统',
      [NodeCategory.PARTICLE_SYSTEM]: '粒子系统',
      [NodeCategory.POST_PROCESS]: '后处理',
      [NodeCategory.TERRAIN_SYSTEM]: '地形系统',
      [NodeCategory.BLOCKCHAIN]: '区块链',
      [NodeCategory.LEARNING_RECORD]: '学习记录',
      [NodeCategory.UI_INTERFACE]: 'UI界面',
      [NodeCategory.RAG_APPLICATION]: 'RAG应用',
      [NodeCategory.SPATIAL_INFORMATION]: '空间信息',
      [NodeCategory.NETWORK]: '网络通信',
      [NodeCategory.RENDERING]: '渲染系统',
      [NodeCategory.ADVANCED_ANIMATION]: '高级动画',
      [NodeCategory.INDUSTRIAL_AUTOMATION]: '工业自动化',
      [NodeCategory.ADVANCED_AUDIO]: '高级音频',
      [NodeCategory.VR_INPUT]: 'VR输入',
      [NodeCategory.RENDERING_OPTIMIZATION]: '渲染优化',
      [NodeCategory.SOFT_BODY_PHYSICS]: '软体物理',
      [NodeCategory.COMPUTER_VISION]: '计算机视觉',
      [NodeCategory.SCENE_MANAGEMENT]: '场景管理',
      [NodeCategory.RESOURCE_MANAGEMENT]: '资源管理',
      [NodeCategory.RESOURCE_OPTIMIZATION]: '资源优化',
      [NodeCategory.ADVANCED_INPUT]: '高级输入',
      [NodeCategory.SENSOR_INPUT]: '传感器输入',
      [NodeCategory.VR_AR_INPUT]: 'VR/AR输入',
      [NodeCategory.PROJECT_MANAGEMENT]: '项目管理',
      [NodeCategory.AI_SERVICE]: 'AI服务',
      [NodeCategory.DATA_SERVICE]: '数据服务',
      [NodeCategory.AUTHENTICATION]: '认证授权',
      [NodeCategory.FILE_SERVICE]: '文件服务',
      [NodeCategory.SCENE_EDITING]: '场景编辑',
      [NodeCategory.MATERIAL_EDITING]: '材质编辑'
    };

    return Array.from(this.categories.entries()).map(([category, nodes]) => ({
      category,
      name: categoryNames[category],
      count: nodes.length
    }));
  }

  /**
   * 获取统计信息
   */
  getStatistics(): {
    totalNodes: number;
    categoriesCount: number;
    nodesByCategory: { [key: string]: number };
  } {
    const nodesByCategory: { [key: string]: number } = {};
    
    for (const [category, nodes] of this.categories.entries()) {
      nodesByCategory[category] = nodes.length;
    }

    return {
      totalNodes: this.nodes.size,
      categoriesCount: this.categories.size,
      nodesByCategory
    };
  }
}

// 导入批次节点注册表
import { registerBatch01Nodes } from './Batch01NodesRegistry';
import { registerBatch02Nodes } from './Batch02NodesRegistry';

// 导出单例实例
export const NodeRegistry = new NodeRegistryManager();

// 注册批次节点
export function registerBatchNodes(): void {
  console.log('开始注册批次节点...');

  // 注册批次0.1节点 (200个)
  registerBatch01Nodes();

  // 注册批次0.2节点 (50个)
  registerBatch02Nodes();

  console.log('批次节点注册完成 - 总计250个节点');
}
