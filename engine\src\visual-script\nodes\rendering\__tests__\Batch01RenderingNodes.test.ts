/**
 * 批次0.1渲染系统节点测试套件
 * 测试69个渲染系统节点的功能和集成
 */
import { describe, test, expect, beforeEach, afterEach, jest } from '@jest/globals';

// 导入材质管理节点
import {
  MaterialSystemNode,
  CreateMaterialNode,
  SetMaterialPropertyNode,
  GetMaterialPropertyNode,
  MaterialBlendNode,
  MaterialAnimationNode,
  MaterialOptimizationNode,
  PBRMaterialNode,
  StandardMaterialNode,
  CustomMaterialNode,
  MaterialPresetNode
} from '../MaterialNodes';

import {
  MaterialEditorNode,
  MaterialPreviewNode,
  MaterialLibraryNode
} from '../MaterialEditingNodes';

// 导入后处理效果节点
import {
  BloomEffectNode,
  BlurEffectNode,
  ColorGradingNode,
  ToneMappingNode,
  SSAONode,
  SSRNode
} from '../PostProcessingEffectNodes';

import {
  MotionBlurNode,
  DepthOfFieldNode,
  FilmGrainNode,
  VignetteNode,
  ChromaticAberrationNode,
  LensDistortionNode,
  AntiAliasingNode,
  HDRProcessingNode,
  CustomPostProcessNode
} from '../AdvancedPostProcessingNodes';

// 导入补充后处理效果节点
import {
  SSGINode,
  TAANode
} from '../AdditionalPostProcessingNodes';

// 导入着色器节点
import {
  VertexShaderNode,
  FragmentShaderNode,
  ComputeShaderNode,
  ShaderCompilerNode,
  ShaderOptimizationNode
} from '../ShaderNodes';

import {
  ShaderVariantNode,
  ShaderParameterNode,
  ShaderIncludeNode,
  ShaderMacroNode
} from '../AdvancedShaderNodes';

import {
  ShaderDebugNode,
  ShaderPerformanceAnalysisNode,
  ShaderValidationNode,
  ShaderCacheNode,
  ShaderHotReloadNode,
  ShaderExportNode
} from '../ShaderUtilityNodes';

// 导入补充着色器节点
import {
  GeometryShaderNode,
  ShaderLinkerNode,
  ShaderPreprocessorNode
} from '../AdditionalShaderNodes';

// 导入渲染优化节点
import {
  LODSystemNode,
  FrustumCullingNode,
  OcclusionCullingNode,
  BatchRenderingNode,
  InstancedRenderingNode,
  DrawCallOptimizationNode,
  TextureAtlasNode,
  MeshCombiningNode,
  RenderQueueNode,
  PerformanceProfilerNode,
  RenderStatisticsNode,
  GPUMemoryMonitorNode,
  RenderPipelineNode,
  CustomRenderPassNode,
  RenderTargetNode
} from '../RenderingOptimizationNodes';

// 导入节点注册表
import { Batch01NodesRegistry } from '../../../registry/Batch01NodesRegistry';

/**
 * 测试工具函数
 */
class TestUtils {
  /**
   * 创建模拟的节点输入
   */
  static createMockInputs(overrides: any = {}): any {
    return {
      trigger: true,
      enabled: true,
      ...overrides
    };
  }

  /**
   * 验证节点输出
   */
  static validateNodeOutput(output: any, expectedKeys: string[]): void {
    expect(output).toBeDefined();
    expect(typeof output).toBe('object');
    
    expectedKeys.forEach(key => {
      expect(output).toHaveProperty(key);
    });
  }

  /**
   * 创建模拟的材质对象
   */
  static createMockMaterial(): any {
    return {
      id: 'test-material',
      name: 'Test Material',
      type: 'standard',
      properties: {
        color: { r: 1, g: 1, b: 1 },
        metalness: 0.5,
        roughness: 0.5
      }
    };
  }

  /**
   * 创建模拟的着色器对象
   */
  static createMockShader(): any {
    return {
      id: 'test-shader',
      type: 'vertex',
      source: 'void main() { gl_Position = vec4(0.0); }',
      compiled: false
    };
  }

  /**
   * 创建模拟的渲染对象
   */
  static createMockRenderObject(): any {
    return {
      id: 'test-object',
      geometry: {},
      material: this.createMockMaterial(),
      position: { x: 0, y: 0, z: 0 },
      visible: true
    };
  }
}

describe('批次0.1渲染系统节点测试', () => {
  let nodeRegistry: Batch01NodesRegistry;

  beforeEach(() => {
    // 重置所有模拟
    jest.clearAllMocks();
    
    // 创建节点注册表实例
    nodeRegistry = Batch01NodesRegistry.getInstance();
  });

  afterEach(() => {
    // 清理资源
    jest.restoreAllMocks();
  });

  describe('节点注册表测试', () => {
    test('应该正确创建单例实例', () => {
      const instance1 = Batch01NodesRegistry.getInstance();
      const instance2 = Batch01NodesRegistry.getInstance();
      
      expect(instance1).toBe(instance2);
      expect(instance1).toBeInstanceOf(Batch01NodesRegistry);
    });

    test('应该注册所有74个节点', () => {
      nodeRegistry.registerAllNodes();
      const registeredTypes = nodeRegistry.getAllRegisteredNodeTypes();

      expect(registeredTypes).toHaveLength(74);
      expect(registeredTypes).toContain('MaterialSystem');
      expect(registeredTypes).toContain('BloomEffect');
      expect(registeredTypes).toContain('VertexShader');
      expect(registeredTypes).toContain('LODSystem');
      expect(registeredTypes).toContain('SSGI');
      expect(registeredTypes).toContain('TAA');
      expect(registeredTypes).toContain('GeometryShader');
    });

    test('应该正确验证节点完整性', () => {
      nodeRegistry.registerAllNodes();
      const isValid = nodeRegistry.validateBatch01Nodes();
      
      expect(isValid).toBe(true);
    });

    test('应该返回正确的统计信息', () => {
      const stats = nodeRegistry.getBatch01Statistics();

      expect(stats.totalNodes).toBe(74);
      expect(stats.categories.materialManagement).toBe(24);
      expect(stats.categories.postProcessing).toBe(17);
      expect(stats.categories.shader).toBe(18);
      expect(stats.categories.renderingOptimization).toBe(15);
    });
  });

  describe('材质管理节点测试', () => {
    test('MaterialSystemNode - 应该正确管理材质系统', () => {
      const node = new MaterialSystemNode();
      const inputs = TestUtils.createMockInputs({
        initialize: true,
        materialCount: 10
      });

      const output = node.execute(inputs);

      TestUtils.validateNodeOutput(output, [
        'materialSystem',
        'materialCount',
        'onInitialized',
        'onError'
      ]);
      expect(output.onInitialized).toBe(true);
      expect(output.onError).toBe(false);
    });

    test('CreateMaterialNode - 应该正确创建材质', () => {
      const node = new CreateMaterialNode();
      const inputs = TestUtils.createMockInputs({
        create: true,
        materialName: 'TestMaterial',
        materialType: 'standard'
      });

      const output = node.execute(inputs);

      TestUtils.validateNodeOutput(output, [
        'material',
        'materialId',
        'materialName',
        'onCreated',
        'onError'
      ]);
      expect(output.materialName).toBe('TestMaterial');
      expect(output.onCreated).toBe(true);
    });

    test('SetMaterialPropertyNode - 应该正确设置材质属性', () => {
      const node = new SetMaterialPropertyNode();
      const material = TestUtils.createMockMaterial();
      const inputs = TestUtils.createMockInputs({
        set: true,
        material: material,
        propertyName: 'color',
        propertyValue: { r: 0.5, g: 0.5, b: 0.5 }
      });

      const output = node.execute(inputs);

      TestUtils.validateNodeOutput(output, [
        'material',
        'propertyName',
        'propertyValue',
        'onSet',
        'onError'
      ]);
      expect(output.onSet).toBe(true);
    });

    test('PBRMaterialNode - 应该正确创建PBR材质', () => {
      const node = new PBRMaterialNode();
      const inputs = TestUtils.createMockInputs({
        create: true,
        baseColor: { r: 1, g: 0, b: 0 },
        metalness: 0.8,
        roughness: 0.2
      });

      const output = node.execute(inputs);

      TestUtils.validateNodeOutput(output, [
        'material',
        'materialType',
        'properties',
        'onCreated',
        'onError'
      ]);
      expect(output.materialType).toBe('pbr');
      expect(output.onCreated).toBe(true);
    });
  });

  describe('后处理效果节点测试', () => {
    test('BloomEffectNode - 应该正确应用泛光效果', () => {
      const node = new BloomEffectNode();
      const inputs = TestUtils.createMockInputs({
        apply: true,
        intensity: 1.5,
        threshold: 0.8,
        radius: 0.5
      });

      const output = node.execute(inputs);

      TestUtils.validateNodeOutput(output, [
        'effect',
        'intensity',
        'threshold',
        'radius',
        'onApplied',
        'onError'
      ]);
      expect(output.intensity).toBe(1.5);
      expect(output.onApplied).toBe(true);
    });

    test('ColorGradingNode - 应该正确应用颜色分级', () => {
      const node = new ColorGradingNode();
      const inputs = TestUtils.createMockInputs({
        apply: true,
        exposure: 0.5,
        contrast: 1.2,
        saturation: 1.1
      });

      const output = node.execute(inputs);

      TestUtils.validateNodeOutput(output, [
        'effect',
        'exposure',
        'contrast',
        'saturation',
        'onApplied',
        'onError'
      ]);
      expect(output.exposure).toBe(0.5);
      expect(output.onApplied).toBe(true);
    });

    test('SSAONode - 应该正确应用SSAO效果', () => {
      const node = new SSAONode();
      const inputs = TestUtils.createMockInputs({
        apply: true,
        radius: 0.1,
        intensity: 1.0,
        bias: 0.025
      });

      const output = node.execute(inputs);

      TestUtils.validateNodeOutput(output, [
        'effect',
        'radius',
        'intensity',
        'bias',
        'onApplied',
        'onError'
      ]);
      expect(output.radius).toBe(0.1);
      expect(output.onApplied).toBe(true);
    });

    test('SSGINode - 应该正确应用屏幕空间全局光照', () => {
      const node = new SSGINode();
      const inputs = TestUtils.createMockInputs({
        apply: true,
        intensity: 1.2,
        radius: 0.6,
        steps: 20
      });

      const output = node.execute(inputs);

      TestUtils.validateNodeOutput(output, [
        'effect',
        'intensity',
        'radius',
        'steps',
        'onApplied',
        'onError'
      ]);
      expect(output.intensity).toBe(1.2);
      expect(output.onApplied).toBe(true);
    });

    test('TAANode - 应该正确应用时间抗锯齿', () => {
      const node = new TAANode();
      const inputs = TestUtils.createMockInputs({
        apply: true,
        sharpness: 0.7,
        feedback: 0.85,
        motionBlending: 0.9
      });

      const output = node.execute(inputs);

      TestUtils.validateNodeOutput(output, [
        'effect',
        'sharpness',
        'feedback',
        'motionBlending',
        'onApplied',
        'onError'
      ]);
      expect(output.sharpness).toBe(0.7);
      expect(output.onApplied).toBe(true);
    });
  });

  describe('着色器节点测试', () => {
    test('VertexShaderNode - 应该正确编译顶点着色器', () => {
      const node = new VertexShaderNode();
      const inputs = TestUtils.createMockInputs({
        compile: true,
        source: 'attribute vec3 position; void main() { gl_Position = vec4(position, 1.0); }',
        shaderId: 'test-vertex-shader'
      });

      const output = node.execute(inputs);

      TestUtils.validateNodeOutput(output, [
        'shader',
        'shaderId',
        'compiled',
        'compilationTime',
        'onCompiled',
        'onError'
      ]);
      expect(output.compiled).toBe(true);
      expect(output.onCompiled).toBe(true);
    });

    test('ShaderCompilerNode - 应该正确编译着色器程序', () => {
      const node = new ShaderCompilerNode();
      const vertexShader = TestUtils.createMockShader();
      const fragmentShader = { ...TestUtils.createMockShader(), type: 'fragment' };
      
      const inputs = TestUtils.createMockInputs({
        compile: true,
        vertexShader: vertexShader,
        fragmentShader: fragmentShader,
        programId: 'test-program'
      });

      const output = node.execute(inputs);

      TestUtils.validateNodeOutput(output, [
        'program',
        'programId',
        'compiled',
        'linkTime',
        'onCompiled',
        'onError'
      ]);
      expect(output.compiled).toBe(true);
      expect(output.onCompiled).toBe(true);
    });

    test('ShaderDebugNode - 应该正确设置调试断点', () => {
      const node = new ShaderDebugNode();
      const inputs = TestUtils.createMockInputs({
        setBreakpoint: true,
        shaderId: 'test-shader',
        line: 10,
        condition: 'gl_FragCoord.x > 100.0'
      });

      const output = node.execute(inputs);

      TestUtils.validateNodeOutput(output, [
        'breakpoint',
        'shaderId',
        'line',
        'condition',
        'onBreakpointSet',
        'onError'
      ]);
      expect(output.line).toBe(10);
      expect(output.onBreakpointSet).toBe(true);
    });

    test('GeometryShaderNode - 应该正确编译几何着色器', () => {
      const node = new GeometryShaderNode();
      const inputs = TestUtils.createMockInputs({
        compile: true,
        source: 'layout(triangles) in; layout(triangle_strip, max_vertices = 3) out; void main() { }',
        shaderId: 'test-geometry-shader',
        inputPrimitive: 'triangles',
        outputPrimitive: 'triangle_strip',
        maxVertices: 3
      });

      const output = node.execute(inputs);

      TestUtils.validateNodeOutput(output, [
        'shader',
        'shaderId',
        'compiled',
        'compilationTime',
        'onCompiled',
        'onError'
      ]);
      expect(output.compiled).toBe(true);
      expect(output.onCompiled).toBe(true);
    });

    test('ShaderLinkerNode - 应该正确链接着色器程序', () => {
      const node = new ShaderLinkerNode();
      const vertexShader = TestUtils.createMockShader();
      const fragmentShader = { ...TestUtils.createMockShader(), type: 'fragment' };

      const inputs = TestUtils.createMockInputs({
        link: true,
        vertexShader: vertexShader,
        fragmentShader: fragmentShader,
        programId: 'test-linked-program'
      });

      const output = node.execute(inputs);

      TestUtils.validateNodeOutput(output, [
        'program',
        'programId',
        'linked',
        'linkTime',
        'onLinked',
        'onError'
      ]);
      expect(output.linked).toBe(true);
      expect(output.onLinked).toBe(true);
    });

    test('ShaderPreprocessorNode - 应该正确预处理着色器源码', () => {
      const node = new ShaderPreprocessorNode();
      const inputs = TestUtils.createMockInputs({
        process: true,
        source: '#define TEST_MACRO 1\n#include "common.glsl"\nvoid main() { }',
        defines: { TEST_MACRO: '2' },
        includes: ['common.glsl']
      });

      const output = node.execute(inputs);

      TestUtils.validateNodeOutput(output, [
        'processedSource',
        'defines',
        'includes',
        'onProcessed',
        'onError'
      ]);
      expect(output.onProcessed).toBe(true);
      expect(output.processedSource).toContain('TEST_MACRO');
    });
  });

  describe('渲染优化节点测试', () => {
    test('LODSystemNode - 应该正确管理LOD系统', () => {
      const node = new LODSystemNode();
      const inputs = TestUtils.createMockInputs({
        addLOD: true,
        object: TestUtils.createMockRenderObject(),
        distances: [10, 50, 100],
        lodLevels: ['high', 'medium', 'low']
      });

      const output = node.execute(inputs);

      TestUtils.validateNodeOutput(output, [
        'lodObject',
        'distances',
        'lodLevels',
        'currentLOD',
        'onLODAdded',
        'onError'
      ]);
      expect(output.lodLevels).toEqual(['high', 'medium', 'low']);
      expect(output.onLODAdded).toBe(true);
    });

    test('FrustumCullingNode - 应该正确执行视锥体剔除', () => {
      const node = new FrustumCullingNode();
      const objects = [
        TestUtils.createMockRenderObject(),
        TestUtils.createMockRenderObject(),
        TestUtils.createMockRenderObject()
      ];
      
      const inputs = TestUtils.createMockInputs({
        cull: true,
        objects: objects,
        camera: { position: { x: 0, y: 0, z: 0 }, fov: 75 },
        enabled: true
      });

      const output = node.execute(inputs);

      TestUtils.validateNodeOutput(output, [
        'visibleObjects',
        'culledObjects',
        'visibleCount',
        'culledCount',
        'onCulled',
        'onError'
      ]);
      expect(Array.isArray(output.visibleObjects)).toBe(true);
      expect(output.onCulled).toBe(true);
    });

    test('BatchRenderingNode - 应该正确执行批处理渲染', () => {
      const node = new BatchRenderingNode();
      const inputs = TestUtils.createMockInputs({
        batch: true,
        objects: [
          TestUtils.createMockRenderObject(),
          TestUtils.createMockRenderObject()
        ],
        batchType: 'static',
        maxBatchSize: 100
      });

      const output = node.execute(inputs);

      TestUtils.validateNodeOutput(output, [
        'batchGroup',
        'batchType',
        'objectCount',
        'batched',
        'onBatched',
        'onError'
      ]);
      expect(output.batchType).toBe('static');
      expect(output.onBatched).toBe(true);
    });

    test('PerformanceProfilerNode - 应该正确分析性能', () => {
      const node = new PerformanceProfilerNode();
      const inputs = TestUtils.createMockInputs({
        startProfiling: true,
        duration: 5000,
        sampleRate: 60
      });

      const output = node.execute(inputs);

      TestUtils.validateNodeOutput(output, [
        'profiler',
        'duration',
        'sampleRate',
        'isRunning',
        'onStarted',
        'onError'
      ]);
      expect(output.duration).toBe(5000);
      expect(output.onStarted).toBe(true);
    });
  });

  describe('节点集成测试', () => {
    test('应该正确处理材质创建到渲染的完整流程', () => {
      // 1. 创建材质
      const createMaterialNode = new CreateMaterialNode();
      const materialOutput = createMaterialNode.execute(TestUtils.createMockInputs({
        create: true,
        materialName: 'IntegrationTestMaterial',
        materialType: 'pbr'
      }));

      expect(materialOutput.onCreated).toBe(true);

      // 2. 设置材质属性
      const setPropertyNode = new SetMaterialPropertyNode();
      const propertyOutput = setPropertyNode.execute(TestUtils.createMockInputs({
        set: true,
        material: materialOutput.material,
        propertyName: 'color',
        propertyValue: { r: 1, g: 0, b: 0 }
      }));

      expect(propertyOutput.onSet).toBe(true);

      // 3. 应用后处理效果
      const bloomNode = new BloomEffectNode();
      const bloomOutput = bloomNode.execute(TestUtils.createMockInputs({
        apply: true,
        intensity: 1.0,
        threshold: 0.5
      }));

      expect(bloomOutput.onApplied).toBe(true);
    });

    test('应该正确处理复杂渲染管线流程', () => {
      // 1. 创建PBR材质
      const pbrMaterialNode = new PBRMaterialNode();
      const pbrOutput = pbrMaterialNode.execute(TestUtils.createMockInputs({
        create: true,
        baseColor: { r: 0.8, g: 0.2, b: 0.2 },
        metalness: 0.9,
        roughness: 0.1
      }));

      expect(pbrOutput.onCreated).toBe(true);
      expect(pbrOutput.materialType).toBe('pbr');

      // 2. 设置LOD系统
      const lodNode = new LODSystemNode();
      const lodOutput = lodNode.execute(TestUtils.createMockInputs({
        addLOD: true,
        object: TestUtils.createMockRenderObject(),
        distances: [5, 25, 100],
        lodLevels: ['high', 'medium', 'low']
      }));

      expect(lodOutput.onLODAdded).toBe(true);

      // 3. 应用多个后处理效果
      const ssaoNode = new SSAONode();
      const ssaoOutput = ssaoNode.execute(TestUtils.createMockInputs({
        apply: true,
        radius: 0.15,
        intensity: 1.2,
        bias: 0.02
      }));

      expect(ssaoOutput.onApplied).toBe(true);

      const colorGradingNode = new ColorGradingNode();
      const colorGradingOutput = colorGradingNode.execute(TestUtils.createMockInputs({
        apply: true,
        exposure: 0.3,
        contrast: 1.1,
        saturation: 1.05
      }));

      expect(colorGradingOutput.onApplied).toBe(true);

      // 4. 性能分析
      const profilerNode = new PerformanceProfilerNode();
      const profilerOutput = profilerNode.execute(TestUtils.createMockInputs({
        analyze: true,
        renderData: {
          materials: [pbrOutput.material],
          lodObjects: [lodOutput.lodObject],
          effects: [ssaoOutput.effect, colorGradingOutput.effect]
        }
      }));

      expect(profilerOutput.onAnalyzed).toBe(true);
    });

    test('应该正确处理着色器编译到优化的完整流程', () => {
      // 1. 编译顶点着色器
      const vertexShaderNode = new VertexShaderNode();
      const vertexOutput = vertexShaderNode.execute(TestUtils.createMockInputs({
        compile: true,
        source: 'attribute vec3 position; void main() { gl_Position = vec4(position, 1.0); }',
        shaderId: 'integration-vertex'
      }));

      expect(vertexOutput.onCompiled).toBe(true);

      // 2. 编译片段着色器
      const fragmentShaderNode = new FragmentShaderNode();
      const fragmentOutput = fragmentShaderNode.execute(TestUtils.createMockInputs({
        compile: true,
        source: 'void main() { gl_FragColor = vec4(1.0, 0.0, 0.0, 1.0); }',
        shaderId: 'integration-fragment'
      }));

      expect(fragmentOutput.onCompiled).toBe(true);

      // 3. 优化着色器
      const optimizationNode = new ShaderOptimizationNode();
      const optimizationOutput = optimizationNode.execute(TestUtils.createMockInputs({
        optimize: true,
        shader: vertexOutput.shader,
        optimizationLevel: 'high'
      }));

      expect(optimizationOutput.onOptimized).toBe(true);
    });

    test('应该正确处理渲染优化的完整流程', () => {
      const objects = [
        TestUtils.createMockRenderObject(),
        TestUtils.createMockRenderObject(),
        TestUtils.createMockRenderObject()
      ];

      // 1. 视锥体剔除
      const frustumCullingNode = new FrustumCullingNode();
      const cullingOutput = frustumCullingNode.execute(TestUtils.createMockInputs({
        cull: true,
        objects: objects,
        camera: { position: { x: 0, y: 0, z: 0 }, fov: 75 }
      }));

      expect(cullingOutput.onCulled).toBe(true);

      // 2. 批处理渲染
      const batchRenderingNode = new BatchRenderingNode();
      const batchOutput = batchRenderingNode.execute(TestUtils.createMockInputs({
        batch: true,
        objects: cullingOutput.visibleObjects,
        batchType: 'dynamic'
      }));

      expect(batchOutput.onBatched).toBe(true);

      // 3. 性能分析
      const profilerNode = new PerformanceProfilerNode();
      const profilerOutput = profilerNode.execute(TestUtils.createMockInputs({
        analyze: true,
        renderData: batchOutput.batchGroup
      }));

      expect(profilerOutput.onAnalyzed).toBe(true);
    });
  });

  describe('错误处理测试', () => {
    test('应该正确处理无效输入', () => {
      const node = new MaterialSystemNode();
      const output = node.execute(null);

      expect(output.onError).toBe(true);
      expect(output.onInitialized).toBe(false);
    });

    test('应该正确处理缺失参数', () => {
      const node = new CreateMaterialNode();
      const output = node.execute(TestUtils.createMockInputs({
        create: true
        // 缺少 materialName 和 materialType
      }));

      expect(output.onError).toBe(true);
      expect(output.onCreated).toBe(false);
    });

    test('应该正确处理着色器编译错误', () => {
      const node = new VertexShaderNode();
      const output = node.execute(TestUtils.createMockInputs({
        compile: true,
        source: 'invalid shader source code',
        shaderId: 'error-test'
      }));

      expect(output.onError).toBe(true);
      expect(output.compiled).toBe(false);
    });
  });
});
