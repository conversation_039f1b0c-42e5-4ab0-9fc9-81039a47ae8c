/**
 * 工业制造节点集成类
 * 负责将批次0.1的60个工业制造节点注册并集成到编辑器中
 * 包括MES系统、设备管理、预测性维护、质量管理、供应链管理、能耗管理等6个子分类
 */

import { NodeRegistry } from '../../../libs/dl-engine-types';

// 工业制造节点分类定义
export enum IndustrialNodeCategory {
  MES_SYSTEM = 'mes_system',
  DEVICE_MANAGEMENT = 'device_management', 
  PREDICTIVE_MAINTENANCE = 'predictive_maintenance',
  QUALITY_MANAGEMENT = 'quality_management',
  SUPPLY_CHAIN_MANAGEMENT = 'supply_chain_management',
  ENERGY_MANAGEMENT = 'energy_management'
}

// 工业制造节点分类信息
export const INDUSTRIAL_CATEGORY_MAP = {
  [IndustrialNodeCategory.MES_SYSTEM]: {
    displayName: 'MES系统',
    icon: 'factory',
    color: '#FF9800',
    description: '制造执行系统节点，包括生产订单、工作流管理、质量控制等',
    nodeCount: 15
  },
  [IndustrialNodeCategory.DEVICE_MANAGEMENT]: {
    displayName: '设备管理',
    icon: 'device_hub',
    color: '#2196F3',
    description: '设备管理节点，包括设备连接、监控、控制、维护等',
    nodeCount: 10
  },
  [IndustrialNodeCategory.PREDICTIVE_MAINTENANCE]: {
    displayName: '预测性维护',
    icon: 'monitor_heart',
    color: '#4CAF50',
    description: '预测性维护节点，包括状态监控、故障预测、维护调度等',
    nodeCount: 10
  },
  [IndustrialNodeCategory.QUALITY_MANAGEMENT]: {
    displayName: '质量管理',
    icon: 'verified',
    color: '#9C27B0',
    description: '质量管理节点，包括质量检验、测试、分析、报告等',
    nodeCount: 10
  },
  [IndustrialNodeCategory.SUPPLY_CHAIN_MANAGEMENT]: {
    displayName: '供应链管理',
    icon: 'local_shipping',
    color: '#FF5722',
    description: '供应链管理节点，包括供应商管理、采购、物流、仓库等',
    nodeCount: 8
  },
  [IndustrialNodeCategory.ENERGY_MANAGEMENT]: {
    displayName: '能耗管理',
    icon: 'electric_meter',
    color: '#4CAF50',
    description: '能耗管理节点，包括能耗监控、优化、分析、报告等',
    nodeCount: 7
  }
};

// MES系统节点列表
export const MES_SYSTEM_NODES = [
  'ProductionOrderNode',
  'WorkflowManagementNode', 
  'QualityControlNode',
  'InventoryManagementNode',
  'SchedulingNode',
  'ResourceAllocationNode',
  'ProductionTrackingNode',
  'PerformanceMonitoringNode',
  'ReportGenerationNode',
  'AlertSystemNode',
  'ComplianceCheckNode',
  'MaintenanceScheduleNode',
  'ProductionOptimizationNode',
  'CostAnalysisNode',
  'EfficiencyAnalysisNode'
];

// 设备管理节点列表
export const DEVICE_MANAGEMENT_NODES = [
  'DeviceConnectionNode',
  'DeviceMonitoringNode',
  'DeviceControlNode',
  'DeviceMaintenanceNode',
  'DeviceDiagnosticsNode',
  'DeviceCalibrationNode',
  'DeviceConfigurationNode',
  'DevicePerformanceNode',
  'DeviceAlertNode',
  'DeviceLifecycleNode'
];

// 预测性维护节点列表
export const PREDICTIVE_MAINTENANCE_NODES = [
  'ConditionMonitoringNode',
  'FailurePredictionNode',
  'MaintenanceSchedulingNode',
  'PartReplacementNode',
  'MaintenanceHistoryNode',
  'MaintenanceCostNode',
  'MaintenanceAnalyticsNode',
  'MaintenanceOptimizationNode',
  'MaintenanceReportingNode',
  'MaintenanceWorkflowNode'
];

// 质量管理节点列表
export const QUALITY_MANAGEMENT_NODES = [
  'QualityInspectionNode',
  'QualityTestingNode',
  'QualityAnalysisNode',
  'QualityReportingNode',
  'QualityControlPlanNode',
  'QualityAuditNode',
  'QualityImprovementNode',
  'QualityStandardsNode',
  'QualityMetricsNode',
  'QualityTraceabilityNode'
];

// 供应链管理节点列表
export const SUPPLY_CHAIN_MANAGEMENT_NODES = [
  'SupplierManagementNode',
  'ProcurementNode',
  'LogisticsNode',
  'WarehouseManagementNode',
  'SupplyChainOptimizationNode',
  'SupplyChainAnalyticsNode',
  'SupplyChainRiskNode',
  'SupplyChainVisibilityNode'
];

// 能耗管理节点列表
export const ENERGY_MANAGEMENT_NODES = [
  'EnergyMonitoringNode',
  'EnergyOptimizationNode',
  'EnergyAnalyticsNode',
  'EnergyReportingNode',
  'EnergyForecastingNode',
  'EnergyEfficiencyNode',
  'CarbonFootprintNode'
];

// 所有工业制造节点映射
export const ALL_INDUSTRIAL_NODES = {
  [IndustrialNodeCategory.MES_SYSTEM]: MES_SYSTEM_NODES,
  [IndustrialNodeCategory.DEVICE_MANAGEMENT]: DEVICE_MANAGEMENT_NODES,
  [IndustrialNodeCategory.PREDICTIVE_MAINTENANCE]: PREDICTIVE_MAINTENANCE_NODES,
  [IndustrialNodeCategory.QUALITY_MANAGEMENT]: QUALITY_MANAGEMENT_NODES,
  [IndustrialNodeCategory.SUPPLY_CHAIN_MANAGEMENT]: SUPPLY_CHAIN_MANAGEMENT_NODES,
  [IndustrialNodeCategory.ENERGY_MANAGEMENT]: ENERGY_MANAGEMENT_NODES
};

/**
 * 工业制造节点集成接口
 */
export interface IIndustrialNodesIntegration {
  integrateAllNodes(): void;
  integrateMESNodes(): void;
  integrateDeviceManagementNodes(): void;
  integratePredictiveMaintenanceNodes(): void;
  integrateQualityManagementNodes(): void;
  integrateSupplyChainManagementNodes(): void;
  integrateEnergyManagementNodes(): void;
  getRegisteredNodes(): Map<string, any>;
  getNodesByCategory(category: IndustrialNodeCategory): string[];
  isNodeRegistered(nodeType: string): boolean;
  getNodeConfig(nodeType: string): any;
}

/**
 * 工业制造节点集成类
 */
export class IndustrialNodesIntegration implements IIndustrialNodesIntegration {
  private nodeEditor: any;
  private registeredNodes: Map<string, any> = new Map();
  private categoryNodes: Map<string, string[]> = new Map();
  private nodeRegistry: NodeRegistry;

  constructor(nodeEditor: any) {
    this.nodeEditor = nodeEditor;
    this.nodeRegistry = NodeRegistry.getInstance();
    this.initializeCategoryNodes();
  }

  /**
   * 初始化分类节点映射
   */
  private initializeCategoryNodes(): void {
    for (const [category, nodes] of Object.entries(ALL_INDUSTRIAL_NODES)) {
      this.categoryNodes.set(category, nodes);
    }
  }

  /**
   * 集成所有工业制造节点
   */
  public integrateAllNodes(): void {
    console.log('开始集成工业制造节点...');
    
    try {
      // 按分类集成节点
      this.integrateMESNodes();
      this.integrateDeviceManagementNodes();
      this.integratePredictiveMaintenanceNodes();
      this.integrateQualityManagementNodes();
      this.integrateSupplyChainManagementNodes();
      this.integrateEnergyManagementNodes();

      // 设置节点面板
      this.setupNodePalette();
      
      // 设置节点分类
      this.setupNodeCategories();

      console.log(`工业制造节点集成完成: 共${this.registeredNodes.size}个节点已添加到编辑器`);
      console.log('分类统计:', this.getCategoryStatistics());
      
    } catch (error) {
      console.error('工业制造节点集成失败:', error);
      throw error;
    }
  }

  /**
   * 集成MES系统节点
   */
  public integrateMESNodes(): void {
    this.integrateNodesByCategory(IndustrialNodeCategory.MES_SYSTEM, MES_SYSTEM_NODES);
    console.log(`MES系统节点集成完成: ${MES_SYSTEM_NODES.length}个节点`);
  }

  /**
   * 集成设备管理节点
   */
  public integrateDeviceManagementNodes(): void {
    this.integrateNodesByCategory(IndustrialNodeCategory.DEVICE_MANAGEMENT, DEVICE_MANAGEMENT_NODES);
    console.log(`设备管理节点集成完成: ${DEVICE_MANAGEMENT_NODES.length}个节点`);
  }

  /**
   * 集成预测性维护节点
   */
  public integratePredictiveMaintenanceNodes(): void {
    this.integrateNodesByCategory(IndustrialNodeCategory.PREDICTIVE_MAINTENANCE, PREDICTIVE_MAINTENANCE_NODES);
    console.log(`预测性维护节点集成完成: ${PREDICTIVE_MAINTENANCE_NODES.length}个节点`);
  }

  /**
   * 集成质量管理节点
   */
  public integrateQualityManagementNodes(): void {
    this.integrateNodesByCategory(IndustrialNodeCategory.QUALITY_MANAGEMENT, QUALITY_MANAGEMENT_NODES);
    console.log(`质量管理节点集成完成: ${QUALITY_MANAGEMENT_NODES.length}个节点`);
  }

  /**
   * 集成供应链管理节点
   */
  public integrateSupplyChainManagementNodes(): void {
    this.integrateNodesByCategory(IndustrialNodeCategory.SUPPLY_CHAIN_MANAGEMENT, SUPPLY_CHAIN_MANAGEMENT_NODES);
    console.log(`供应链管理节点集成完成: ${SUPPLY_CHAIN_MANAGEMENT_NODES.length}个节点`);
  }

  /**
   * 集成能耗管理节点
   */
  public integrateEnergyManagementNodes(): void {
    this.integrateNodesByCategory(IndustrialNodeCategory.ENERGY_MANAGEMENT, ENERGY_MANAGEMENT_NODES);
    console.log(`能耗管理节点集成完成: ${ENERGY_MANAGEMENT_NODES.length}个节点`);
  }

  /**
   * 按分类集成节点
   */
  private integrateNodesByCategory(category: IndustrialNodeCategory, nodeTypes: string[]): void {
    const categoryInfo = INDUSTRIAL_CATEGORY_MAP[category];

    for (const nodeType of nodeTypes) {
      try {
        // 从NodeRegistry获取节点信息
        const nodeInfo = this.nodeRegistry.getNodeInfo(nodeType);

        if (nodeInfo) {
          // 创建节点配置
          const nodeConfig = {
            type: nodeType,
            name: nodeInfo.name,
            description: nodeInfo.description,
            category: category,
            icon: nodeInfo.icon || categoryInfo.icon,
            color: nodeInfo.color || categoryInfo.color,
            tags: nodeInfo.tags || [],
            nodeClass: nodeInfo.nodeClass,
            inputs: nodeInfo.inputs || [],
            outputs: nodeInfo.outputs || []
          };

          // 注册到本地映射
          this.registeredNodes.set(nodeType, nodeConfig);

          console.log(`节点 ${nodeType} 已集成到分类 ${categoryInfo.displayName}`);
        } else {
          console.warn(`节点 ${nodeType} 在NodeRegistry中未找到`);
        }
      } catch (error) {
        console.error(`集成节点 ${nodeType} 失败:`, error);
      }
    }
  }

  /**
   * 设置节点面板
   */
  private setupNodePalette(): void {
    // 将节点添加到编辑器的节点面板
    for (const [nodeType, nodeConfig] of this.registeredNodes.entries()) {
      try {
        if (this.nodeEditor && this.nodeEditor.addNodeToPalette) {
          this.nodeEditor.addNodeToPalette(nodeType, nodeConfig);
        }
      } catch (error) {
        console.error(`添加节点 ${nodeType} 到面板失败:`, error);
      }
    }

    console.log(`工业制造节点面板设置完成: ${this.registeredNodes.size}个节点已添加到编辑器`);
  }

  /**
   * 设置节点分类
   */
  private setupNodeCategories(): void {
    // 为编辑器添加工业制造节点分类
    for (const [category, categoryInfo] of Object.entries(INDUSTRIAL_CATEGORY_MAP)) {
      try {
        if (this.nodeEditor && this.nodeEditor.addNodeCategory) {
          this.nodeEditor.addNodeCategory(category, {
            displayName: categoryInfo.displayName,
            icon: categoryInfo.icon,
            color: categoryInfo.color,
            description: categoryInfo.description,
            nodes: this.categoryNodes.get(category) || []
          });
        }
      } catch (error) {
        console.error(`添加分类 ${category} 失败:`, error);
      }
    }

    console.log('工业制造节点分类设置完成: 6个分类已添加到编辑器');
  }

  /**
   * 获取已注册的节点
   */
  public getRegisteredNodes(): Map<string, any> {
    return this.registeredNodes;
  }

  /**
   * 根据分类获取节点
   */
  public getNodesByCategory(category: IndustrialNodeCategory): string[] {
    return this.categoryNodes.get(category) || [];
  }

  /**
   * 检查节点是否已注册
   */
  public isNodeRegistered(nodeType: string): boolean {
    return this.registeredNodes.has(nodeType);
  }

  /**
   * 获取节点配置
   */
  public getNodeConfig(nodeType: string): any {
    return this.registeredNodes.get(nodeType);
  }

  /**
   * 获取分类统计信息
   */
  public getCategoryStatistics(): { [key: string]: number } {
    const stats: { [key: string]: number } = {};

    for (const [category, nodes] of this.categoryNodes.entries()) {
      const categoryInfo = INDUSTRIAL_CATEGORY_MAP[category as IndustrialNodeCategory];
      stats[categoryInfo.displayName] = nodes.length;
    }

    return stats;
  }

  /**
   * 获取总节点数
   */
  public getTotalNodeCount(): number {
    return this.registeredNodes.size;
  }

  /**
   * 验证所有节点是否正确集成
   */
  public validateIntegration(): { success: boolean; errors: string[] } {
    const errors: string[] = [];
    let totalExpected = 0;

    // 计算预期总数
    for (const categoryInfo of Object.values(INDUSTRIAL_CATEGORY_MAP)) {
      totalExpected += categoryInfo.nodeCount;
    }

    // 检查总数
    if (this.registeredNodes.size !== totalExpected) {
      errors.push(`节点总数不匹配: 预期 ${totalExpected}, 实际 ${this.registeredNodes.size}`);
    }

    // 检查每个分类的节点数
    for (const [category, nodes] of this.categoryNodes.entries()) {
      const categoryInfo = INDUSTRIAL_CATEGORY_MAP[category as IndustrialNodeCategory];
      if (nodes.length !== categoryInfo.nodeCount) {
        errors.push(`${categoryInfo.displayName} 节点数不匹配: 预期 ${categoryInfo.nodeCount}, 实际 ${nodes.length}`);
      }
    }

    // 检查节点是否都有配置
    for (const [nodeType, nodeConfig] of this.registeredNodes.entries()) {
      if (!nodeConfig.name || !nodeConfig.description) {
        errors.push(`节点 ${nodeType} 缺少必要配置`);
      }
    }

    return {
      success: errors.length === 0,
      errors
    };
  }
}

// 导出集成函数
export function integrateIndustrialNodes(nodeEditor: any): IndustrialNodesIntegration {
  const integration = new IndustrialNodesIntegration(nodeEditor);
  integration.integrateAllNodes();
  return integration;
}
