/**
 * 工业制造节点搜索组件
 * 提供高级搜索和过滤功能
 */

import React, { useState, useMemo, useCallback } from 'react';
import {
  Input,
  Select,
  Space,
  Tag,
  Button,
  Dropdown,
  Menu,
  Tooltip,
  Badge,
  Divider,
  Checkbox
} from 'antd';
import {
  SearchOutlined,
  FilterOutlined,
  ClearOutlined,
  SortAscendingOutlined,
  SortDescendingOutlined,
  TagsOutlined,
  AppstoreOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import {
  IndustrialNodeCategory,
  INDUSTRIAL_CATEGORY_MAP
} from '../nodes/IndustrialNodesIntegration';

const { Search } = Input;
const { Option } = Select;
const { CheckableTag } = Tag;

interface NodeItem {
  type: string;
  name: string;
  description: string;
  category: IndustrialNodeCategory;
  icon: string;
  color: string;
  tags: string[];
}

interface IndustrialNodesSearchProps {
  nodes: NodeItem[];
  onFilteredNodesChange: (filteredNodes: NodeItem[]) => void;
  onSearchTextChange?: (searchText: string) => void;
}

// 排序选项
enum SortOption {
  NAME_ASC = 'name_asc',
  NAME_DESC = 'name_desc',
  CATEGORY = 'category',
  RECENT = 'recent'
}

// 过滤选项
interface FilterOptions {
  categories: IndustrialNodeCategory[];
  tags: string[];
  searchText: string;
  sortBy: SortOption;
}

export const IndustrialNodesSearch: React.FC<IndustrialNodesSearchProps> = ({
  nodes,
  onFilteredNodesChange,
  onSearchTextChange
}) => {
  const { t } = useTranslation();
  const [searchText, setSearchText] = useState('');
  const [selectedCategories, setSelectedCategories] = useState<IndustrialNodeCategory[]>([]);
  const [selectedTags, setSelectedTags] = useState<string[]>([]);
  const [sortBy, setSortBy] = useState<SortOption>(SortOption.NAME_ASC);
  const [showAdvancedFilters, setShowAdvancedFilters] = useState(false);

  // 获取所有可用标签
  const availableTags = useMemo(() => {
    const tagSet = new Set<string>();
    nodes.forEach(node => {
      node.tags.forEach(tag => tagSet.add(tag));
    });
    return Array.from(tagSet).sort();
  }, [nodes]);

  // 过滤和排序节点
  const filteredAndSortedNodes = useMemo(() => {
    let filtered = nodes;

    // 按搜索文本过滤
    if (searchText.trim()) {
      const searchLower = searchText.toLowerCase();
      filtered = filtered.filter(node =>
        node.name.toLowerCase().includes(searchLower) ||
        node.description.toLowerCase().includes(searchLower) ||
        node.type.toLowerCase().includes(searchLower) ||
        node.tags.some(tag => tag.toLowerCase().includes(searchLower))
      );
    }

    // 按分类过滤
    if (selectedCategories.length > 0) {
      filtered = filtered.filter(node => selectedCategories.includes(node.category));
    }

    // 按标签过滤
    if (selectedTags.length > 0) {
      filtered = filtered.filter(node =>
        selectedTags.some(tag => node.tags.includes(tag))
      );
    }

    // 排序
    switch (sortBy) {
      case SortOption.NAME_ASC:
        filtered.sort((a, b) => a.name.localeCompare(b.name));
        break;
      case SortOption.NAME_DESC:
        filtered.sort((a, b) => b.name.localeCompare(a.name));
        break;
      case SortOption.CATEGORY:
        filtered.sort((a, b) => {
          const categoryA = INDUSTRIAL_CATEGORY_MAP[a.category].displayName;
          const categoryB = INDUSTRIAL_CATEGORY_MAP[b.category].displayName;
          return categoryA.localeCompare(categoryB);
        });
        break;
      case SortOption.RECENT:
        // 这里可以根据使用历史排序，暂时保持原顺序
        break;
    }

    return filtered;
  }, [nodes, searchText, selectedCategories, selectedTags, sortBy]);

  // 通知父组件过滤结果变化
  React.useEffect(() => {
    onFilteredNodesChange(filteredAndSortedNodes);
  }, [filteredAndSortedNodes, onFilteredNodesChange]);

  // 处理搜索文本变化
  const handleSearchChange = useCallback((value: string) => {
    setSearchText(value);
    onSearchTextChange?.(value);
  }, [onSearchTextChange]);

  // 处理分类选择
  const handleCategoryChange = useCallback((category: IndustrialNodeCategory, checked: boolean) => {
    setSelectedCategories(prev => 
      checked 
        ? [...prev, category]
        : prev.filter(c => c !== category)
    );
  }, []);

  // 处理标签选择
  const handleTagChange = useCallback((tag: string, checked: boolean) => {
    setSelectedTags(prev => 
      checked 
        ? [...prev, tag]
        : prev.filter(t => t !== tag)
    );
  }, []);

  // 清除所有过滤器
  const clearAllFilters = useCallback(() => {
    setSearchText('');
    setSelectedCategories([]);
    setSelectedTags([]);
    setSortBy(SortOption.NAME_ASC);
    onSearchTextChange?.('');
  }, [onSearchTextChange]);

  // 排序菜单
  const sortMenu = (
    <Menu
      selectedKeys={[sortBy]}
      onClick={({ key }) => setSortBy(key as SortOption)}
    >
      <Menu.Item key={SortOption.NAME_ASC} icon={<SortAscendingOutlined />}>
        按名称升序
      </Menu.Item>
      <Menu.Item key={SortOption.NAME_DESC} icon={<SortDescendingOutlined />}>
        按名称降序
      </Menu.Item>
      <Menu.Item key={SortOption.CATEGORY} icon={<AppstoreOutlined />}>
        按分类排序
      </Menu.Item>
    </Menu>
  );

  // 过滤器菜单
  const filterMenu = (
    <div style={{ padding: '12px', width: '300px' }}>
      {/* 分类过滤 */}
      <div style={{ marginBottom: '12px' }}>
        <div style={{ marginBottom: '8px', fontWeight: 'bold' }}>分类过滤</div>
        <Space wrap>
          {Object.values(IndustrialNodeCategory).map(category => {
            const categoryInfo = INDUSTRIAL_CATEGORY_MAP[category];
            return (
              <CheckableTag
                key={category}
                checked={selectedCategories.includes(category)}
                onChange={(checked) => handleCategoryChange(category, checked)}
              >
                {categoryInfo.displayName}
              </CheckableTag>
            );
          })}
        </Space>
      </div>

      <Divider style={{ margin: '8px 0' }} />

      {/* 标签过滤 */}
      <div>
        <div style={{ marginBottom: '8px', fontWeight: 'bold' }}>标签过滤</div>
        <div style={{ maxHeight: '120px', overflow: 'auto' }}>
          <Space wrap>
            {availableTags.slice(0, 20).map(tag => (
              <CheckableTag
                key={tag}
                checked={selectedTags.includes(tag)}
                onChange={(checked) => handleTagChange(tag, checked)}
              >
                {tag}
              </CheckableTag>
            ))}
          </Space>
        </div>
      </div>
    </div>
  );

  return (
    <div style={{ marginBottom: '16px' }}>
      {/* 主搜索栏 */}
      <Space.Compact style={{ width: '100%', marginBottom: '8px' }}>
        <Search
          placeholder="搜索工业制造节点..."
          value={searchText}
          onChange={(e) => handleSearchChange(e.target.value)}
          style={{ flex: 1 }}
          allowClear
        />
        <Dropdown overlay={sortMenu} trigger={['click']}>
          <Button icon={<SortAscendingOutlined />} />
        </Dropdown>
        <Dropdown overlay={filterMenu} trigger={['click']}>
          <Button icon={<FilterOutlined />}>
            <Badge count={selectedCategories.length + selectedTags.length} size="small" />
          </Button>
        </Dropdown>
        <Tooltip title="清除所有过滤器">
          <Button 
            icon={<ClearOutlined />} 
            onClick={clearAllFilters}
            disabled={!searchText && selectedCategories.length === 0 && selectedTags.length === 0}
          />
        </Tooltip>
      </Space.Compact>

      {/* 活动过滤器显示 */}
      {(selectedCategories.length > 0 || selectedTags.length > 0) && (
        <div style={{ marginBottom: '8px' }}>
          <Space wrap size="small">
            {selectedCategories.map(category => {
              const categoryInfo = INDUSTRIAL_CATEGORY_MAP[category];
              return (
                <Tag
                  key={category}
                  closable
                  color={categoryInfo.color}
                  onClose={() => handleCategoryChange(category, false)}
                >
                  {categoryInfo.displayName}
                </Tag>
              );
            })}
            {selectedTags.map(tag => (
              <Tag
                key={tag}
                closable
                onClose={() => handleTagChange(tag, false)}
              >
                {tag}
              </Tag>
            ))}
          </Space>
        </div>
      )}

      {/* 搜索结果统计 */}
      <div style={{ fontSize: '12px', color: '#666' }}>
        显示 {filteredAndSortedNodes.length} / {nodes.length} 个节点
        {searchText && ` (搜索: "${searchText}")`}
      </div>
    </div>
  );
};

export default IndustrialNodesSearch;
