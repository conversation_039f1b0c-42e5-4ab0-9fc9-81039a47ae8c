/**
 * 批次3.4扩展节点编辑器集成
 * 支付系统节点和第三方集成节点在编辑器中的集成
 */

import { NodeEditor } from '../NodeEditor';
import { VisualScriptNode } from '../../../libs/dl-engine-types';
import { batch34ExtensionNodesRegistry } from '../../../libs/dl-engine';

/**
 * 批次3.4扩展节点编辑器集成类
 */
export class Batch34ExtensionNodesIntegration {
  private nodeEditor: NodeEditor;
  private registeredNodes: Map<string, any> = new Map();

  constructor(nodeEditor: NodeEditor) {
    this.nodeEditor = nodeEditor;
    this.initializeNodes();
  }

  /**
   * 初始化批次3.4扩展节点
   */
  private initializeNodes(): void {
    this.registerPaymentNodes();
    this.registerThirdPartyNodes();
    this.setupNodeCategories();
    this.setupNodePalette();
  }

  /**
   * 注册支付系统节点
   */
  private registerPaymentNodes(): void {
    const paymentNodes = [
      {
        type: 'PaymentGateway',
        name: '支付网关',
        description: '处理各种支付方式的统一接口',
        category: '支付系统',
        icon: 'payment',
        color: '#4CAF50'
      },
      {
        type: 'Subscription',
        name: '订阅系统',
        description: '处理订阅服务的创建、管理和计费',
        category: '支付系统',
        icon: 'subscription',
        color: '#2196F3'
      },
      {
        type: 'InAppPurchase',
        name: '应用内购买',
        description: '处理移动应用内购买和虚拟商品交易',
        category: '支付系统',
        icon: 'shopping_cart',
        color: '#FF9800'
      },
      {
        type: 'WalletSystem',
        name: '钱包系统',
        description: '处理用户钱包余额、充值、提现等操作',
        category: '支付系统',
        icon: 'account_balance_wallet',
        color: '#9C27B0'
      },
      {
        type: 'TransactionHistory',
        name: '交易历史',
        description: '查询和管理用户的交易历史记录',
        category: '支付系统',
        icon: 'history',
        color: '#607D8B'
      },
      {
        type: 'PaymentAnalytics',
        name: '支付分析',
        description: '提供支付数据的统计分析和报表功能',
        category: '支付系统',
        icon: 'analytics',
        color: '#795548'
      }
    ];

    paymentNodes.forEach(nodeConfig => {
      this.registeredNodes.set(nodeConfig.type, nodeConfig);
      this.nodeEditor.registerNodeType(nodeConfig.type, nodeConfig);
    });

    console.log('支付系统节点已注册到编辑器');
  }

  /**
   * 注册第三方集成节点
   */
  private registerThirdPartyNodes(): void {
    const thirdPartyNodes = [
      {
        type: 'GoogleServices',
        name: 'Google服务',
        description: '集成Google各种服务API',
        category: '第三方集成',
        icon: 'google',
        color: '#4285F4'
      },
      {
        type: 'FacebookIntegration',
        name: 'Facebook集成',
        description: '集成Facebook Graph API和社交功能',
        category: '第三方集成',
        icon: 'facebook',
        color: '#1877F2'
      },
      {
        type: 'TwitterIntegration',
        name: 'Twitter集成',
        description: '集成Twitter API v2功能',
        category: '第三方集成',
        icon: 'twitter',
        color: '#1DA1F2'
      },
      {
        type: 'CloudStorage',
        name: '云存储',
        description: '集成各种云存储服务',
        category: '第三方集成',
        icon: 'cloud_upload',
        color: '#FF6F00'
      },
      {
        type: 'AnalyticsIntegration',
        name: '分析集成',
        description: '集成各种分析服务',
        category: '第三方集成',
        icon: 'trending_up',
        color: '#E91E63'
      }
    ];

    thirdPartyNodes.forEach(nodeConfig => {
      this.registeredNodes.set(nodeConfig.type, nodeConfig);
      this.nodeEditor.registerNodeType(nodeConfig.type, nodeConfig);
    });

    console.log('第三方集成节点已注册到编辑器');
  }

  /**
   * 设置节点分类
   */
  private setupNodeCategories(): void {
    // 添加支付系统分类
    this.nodeEditor.addNodeCategory({
      name: '支付系统',
      description: '支付、订阅、钱包等支付相关功能',
      icon: 'payment',
      color: '#4CAF50',
      order: 15
    });

    // 添加第三方集成分类
    this.nodeEditor.addNodeCategory({
      name: '第三方集成',
      description: 'Google、Facebook、Twitter等第三方服务集成',
      icon: 'integration_instructions',
      color: '#2196F3',
      order: 16
    });
  }

  /**
   * 设置节点面板
   */
  private setupNodePalette(): void {
    // 支付系统节点面板
    const paymentPalette = {
      category: '支付系统',
      nodes: [
        'PaymentGateway',
        'Subscription',
        'InAppPurchase',
        'WalletSystem',
        'TransactionHistory',
        'PaymentAnalytics'
      ]
    };

    // 第三方集成节点面板
    const thirdPartyPalette = {
      category: '第三方集成',
      nodes: [
        'GoogleServices',
        'FacebookIntegration',
        'TwitterIntegration',
        'CloudStorage',
        'AnalyticsIntegration'
      ]
    };

    this.nodeEditor.addNodePalette(paymentPalette);
    this.nodeEditor.addNodePalette(thirdPartyPalette);
  }

  /**
   * 创建节点实例
   */
  public createNode(nodeType: string, position: { x: number; y: number }): VisualScriptNode | null {
    const nodeConfig = this.registeredNodes.get(nodeType);
    if (!nodeConfig) {
      console.error(`未知的节点类型: ${nodeType}`);
      return null;
    }

    try {
      const node = this.nodeEditor.createNode(nodeType, position);
      if (node) {
        // 设置节点的UI属性
        this.nodeEditor.setNodeUIProperties(node.id, {
          color: nodeConfig.color,
          icon: nodeConfig.icon,
          category: nodeConfig.category
        });
      }
      return node;
    } catch (error) {
      console.error(`创建节点失败: ${nodeType}`, error);
      return null;
    }
  }

  /**
   * 获取节点配置
   */
  public getNodeConfig(nodeType: string): any {
    return this.registeredNodes.get(nodeType);
  }

  /**
   * 获取所有已注册的节点类型
   */
  public getRegisteredNodeTypes(): string[] {
    return Array.from(this.registeredNodes.keys());
  }

  /**
   * 检查节点类型是否已注册
   */
  public isNodeTypeRegistered(nodeType: string): boolean {
    return this.registeredNodes.has(nodeType);
  }

  /**
   * 获取节点分类统计
   */
  public getNodeCategoryStats(): { [category: string]: number } {
    const stats: { [category: string]: number } = {};
    
    this.registeredNodes.forEach(nodeConfig => {
      const category = nodeConfig.category;
      stats[category] = (stats[category] || 0) + 1;
    });

    return stats;
  }

  /**
   * 搜索节点
   */
  public searchNodes(query: string): any[] {
    const results: any[] = [];
    const lowerQuery = query.toLowerCase();

    this.registeredNodes.forEach((nodeConfig, nodeType) => {
      if (
        nodeConfig.name.toLowerCase().includes(lowerQuery) ||
        nodeConfig.description.toLowerCase().includes(lowerQuery) ||
        nodeConfig.category.toLowerCase().includes(lowerQuery) ||
        nodeType.toLowerCase().includes(lowerQuery)
      ) {
        results.push({
          type: nodeType,
          ...nodeConfig
        });
      }
    });

    return results;
  }

  /**
   * 获取节点使用统计
   */
  public getNodeUsageStats(): { [nodeType: string]: number } {
    // 这里可以实现节点使用统计逻辑
    // 目前返回模拟数据
    const stats: { [nodeType: string]: number } = {};
    
    this.registeredNodes.forEach((_, nodeType) => {
      stats[nodeType] = Math.floor(Math.random() * 100);
    });

    return stats;
  }

  /**
   * 导出节点配置
   */
  public exportNodeConfigs(): any {
    const configs: any = {};
    
    this.registeredNodes.forEach((nodeConfig, nodeType) => {
      configs[nodeType] = {
        ...nodeConfig,
        registeredAt: new Date().toISOString()
      };
    });

    return {
      version: '1.0.0',
      batch: '3.4-extension',
      totalNodes: this.registeredNodes.size,
      categories: ['支付系统', '第三方集成'],
      nodes: configs,
      exportedAt: new Date().toISOString()
    };
  }

  /**
   * 验证节点完整性
   */
  public validateNodes(): { valid: boolean; errors: string[] } {
    const errors: string[] = [];
    
    this.registeredNodes.forEach((nodeConfig, nodeType) => {
      // 检查必需属性
      if (!nodeConfig.name) {
        errors.push(`节点 ${nodeType} 缺少名称`);
      }
      if (!nodeConfig.description) {
        errors.push(`节点 ${nodeType} 缺少描述`);
      }
      if (!nodeConfig.category) {
        errors.push(`节点 ${nodeType} 缺少分类`);
      }
      if (!nodeConfig.color) {
        errors.push(`节点 ${nodeType} 缺少颜色`);
      }
    });

    return {
      valid: errors.length === 0,
      errors
    };
  }

  /**
   * 销毁集成
   */
  public destroy(): void {
    this.registeredNodes.clear();
    console.log('批次3.4扩展节点编辑器集成已销毁');
  }
}
