/**
 * 批次0.2节点注册表
 * 注册AI系统扩展集成(50个)节点到编辑器
 */

import { NodeRegistry } from './NodeRegistry';

/**
 * 批次0.2节点注册表类
 */
export class Batch02NodesRegistry {
  private static instance: Batch02NodesRegistry;
  private nodeRegistry: NodeRegistry;
  private registered: boolean = false;
  private registeredNodes: Map<string, number> = new Map();

  private constructor() {
    this.nodeRegistry = NodeRegistry.getInstance();
  }

  /**
   * 获取单例实例
   */
  public static getInstance(): Batch02NodesRegistry {
    if (!Batch02NodesRegistry.instance) {
      Batch02NodesRegistry.instance = new Batch02NodesRegistry();
    }
    return Batch02NodesRegistry.instance;
  }

  /**
   * 注册所有批次0.2节点
   */
  public registerAllNodes(): void {
    if (this.registered) {
      console.log('批次0.2节点已注册，跳过重复注册');
      return;
    }

    console.log('开始注册批次0.2节点...');

    // 注册AI系统扩展集成节点 (50个)
    this.registerAISystemNodes();

    this.registered = true;
    console.log('批次0.2节点注册完成');
    console.log(`AI系统扩展集成节点：50个`);
    console.log(`总计：50个节点`);
  }

  /**
   * 注册AI系统扩展集成节点 (50个)
   */
  private registerAISystemNodes(): void {
    console.log('批次0.2：注册AI系统扩展集成节点...');

    // 深度学习节点 (15个)
    this.registerDeepLearningNodes();
    
    // 机器学习节点 (10个)
    this.registerMachineLearningNodes();
    
    // 计算机视觉节点 (12个)
    this.registerComputerVisionNodes();
    
    // 自然语言处理节点 (11个)
    this.registerNLPNodes();
    
    // AI工具节点 (12个)
    this.registerAIToolNodes();

    this.registeredNodes.set('aiSystem', 50);
    console.log('批次0.2：AI系统扩展集成节点注册完成 - 50个节点');
  }

  /**
   * 注册深度学习节点 (15个)
   */
  private registerDeepLearningNodes(): void {
    console.log('注册深度学习节点...');

    const deepLearningNodes = [
      { name: 'DeepLearningModelNode', desc: '深度学习模型管理' },
      { name: 'NeuralNetworkNode', desc: '神经网络构建' },
      { name: 'CNNNode', desc: '卷积神经网络' },
      { name: 'RNNNode', desc: '循环神经网络' },
      { name: 'LSTMNode', desc: '长短期记忆网络' },
      { name: 'TransformerNode', desc: 'Transformer模型' },
      { name: 'GANNode', desc: '生成对抗网络' },
      { name: 'AutoencoderNode', desc: '自编码器' },
      { name: 'VAENode', desc: '变分自编码器' },
      { name: 'ResNetNode', desc: 'ResNet残差网络' },
      { name: 'UNetNode', desc: 'U-Net网络' },
      { name: 'AttentionNode', desc: '注意力机制' },
      { name: 'EmbeddingNode', desc: '嵌入层' },
      { name: 'DropoutNode', desc: 'Dropout层' },
      { name: 'BatchNormNode', desc: '批量归一化' }
    ];

    deepLearningNodes.forEach(node => {
      this.nodeRegistry.registerNode(
        node.name,
        null,
        'AI系统',
        node.desc,
        'deep_learning',
        '#E91E63'
      );
    });

    console.log('深度学习节点注册完成 - 15个节点');
  }

  /**
   * 注册机器学习节点 (10个)
   */
  private registerMachineLearningNodes(): void {
    console.log('注册机器学习节点...');

    const mlNodes = [
      { name: 'ReinforcementLearningNode', desc: '强化学习' },
      { name: 'DecisionTreeNode', desc: '决策树' },
      { name: 'RandomForestNode', desc: '随机森林' },
      { name: 'SVMNode', desc: '支持向量机' },
      { name: 'KMeansNode', desc: 'K均值聚类' },
      { name: 'LinearRegressionNode', desc: '线性回归' },
      { name: 'LogisticRegressionNode', desc: '逻辑回归' },
      { name: 'NaiveBayesNode', desc: '朴素贝叶斯' },
      { name: 'PCANode', desc: '主成分分析' },
      { name: 'GradientBoostingNode', desc: '梯度提升' }
    ];

    mlNodes.forEach(node => {
      this.nodeRegistry.registerNode(
        node.name,
        null,
        'AI系统',
        node.desc,
        'machine_learning',
        '#9C27B0'
      );
    });

    console.log('机器学习节点注册完成 - 10个节点');
  }

  /**
   * 注册计算机视觉节点 (12个)
   */
  private registerComputerVisionNodes(): void {
    console.log('注册计算机视觉节点...');

    const cvNodes = [
      { name: 'ImageSegmentationNode', desc: '图像分割' },
      { name: 'ObjectDetectionNode', desc: '目标检测' },
      { name: 'FaceRecognitionNode', desc: '人脸识别' },
      { name: 'OpticalFlowNode', desc: '光流估计' },
      { name: 'FeatureExtractionNode', desc: '特征提取' },
      { name: 'ImageClassificationNode', desc: '图像分类' },
      { name: 'EdgeDetectionNode', desc: '边缘检测' },
      { name: 'ImageFilterNode', desc: '图像滤波' },
      { name: 'StereoVisionNode', desc: '立体视觉' },
      { name: 'MotionTrackingNode', desc: '运动跟踪' },
      { name: 'ImageRegistrationNode', desc: '图像配准' },
      { name: 'DepthEstimationNode', desc: '深度估计' }
    ];

    cvNodes.forEach(node => {
      this.nodeRegistry.registerNode(
        node.name,
        null,
        'AI系统',
        node.desc,
        'computer_vision',
        '#2196F3'
      );
    });

    console.log('计算机视觉节点注册完成 - 12个节点');
  }

  /**
   * 注册自然语言处理节点 (11个)
   */
  private registerNLPNodes(): void {
    console.log('注册自然语言处理节点...');

    const nlpNodes = [
      { name: 'TextClassificationNode', desc: '文本分类' },
      { name: 'SentimentAnalysisNode', desc: '情感分析' },
      { name: 'NamedEntityRecognitionNode', desc: '命名实体识别' },
      { name: 'TextSummarizationNode', desc: '文本摘要' },
      { name: 'MachineTranslationNode', desc: '机器翻译' },
      { name: 'QuestionAnsweringNode', desc: '问答系统' },
      { name: 'TextGenerationNode', desc: '文本生成' },
      { name: 'TokenizationNode', desc: '分词处理' },
      { name: 'POSTaggingNode', desc: '词性标注' },
      { name: 'SyntaxParsingNode', desc: '语法解析' },
      { name: 'SemanticAnalysisNode', desc: '语义分析' }
    ];

    nlpNodes.forEach(node => {
      this.nodeRegistry.registerNode(
        node.name,
        null,
        'AI系统',
        node.desc,
        'nlp',
        '#4CAF50'
      );
    });

    console.log('自然语言处理节点注册完成 - 11个节点');
  }

  /**
   * 注册AI工具节点 (12个)
   */
  private registerAIToolNodes(): void {
    console.log('注册AI工具节点...');

    const aiToolNodes = [
      { name: 'ModelDeploymentNode', desc: '模型部署' },
      { name: 'ModelTrainingNode', desc: '模型训练' },
      { name: 'ModelEvaluationNode', desc: '模型评估' },
      { name: 'DataPreprocessingNode', desc: '数据预处理' },
      { name: 'FeatureEngineeringNode', desc: '特征工程' },
      { name: 'HyperparameterTuningNode', desc: '超参数调优' },
      { name: 'ModelOptimizationNode', desc: '模型优化' },
      { name: 'ModelVersioningNode', desc: '模型版本管理' },
      { name: 'ModelMonitoringNode', desc: '模型监控' },
      { name: 'AutoMLNode', desc: '自动机器学习' },
      { name: 'MLOpsNode', desc: 'MLOps工具' },
      { name: 'ModelInterpretabilityNode', desc: '模型可解释性' }
    ];

    aiToolNodes.forEach(node => {
      this.nodeRegistry.registerNode(
        node.name,
        null,
        'AI系统',
        node.desc,
        'ai_tools',
        '#FF5722'
      );
    });

    console.log('AI工具节点注册完成 - 12个节点');
  }

  /**
   * 获取批次0.2统计信息
   */
  public getBatch02Statistics(): any {
    return {
      totalNodes: 50,
      categories: {
        deepLearning: 15,
        machineLearning: 10,
        computerVision: 12,
        nlp: 11,
        aiTools: 12
      },
      features: [
        '深度学习模型',
        '机器学习算法',
        '计算机视觉',
        '自然语言处理',
        'AI工具集成',
        '模型部署',
        '数据预处理',
        '特征工程'
      ],
      compatibility: {
        editor: true,
        runtime: true,
        webgl: true,
        mobile: true
      }
    };
  }

  /**
   * 验证批次0.2节点完整性
   */
  public validateBatch02Nodes(): boolean {
    const expectedNodes = 50;
    const totalRegistered = Array.from(this.registeredNodes.values()).reduce((sum, count) => sum + count, 0);

    if (totalRegistered !== expectedNodes) {
      console.error(`批次0.2节点注册不完整：期望 ${expectedNodes} 个，实际 ${totalRegistered} 个`);
      return false;
    }

    console.log('批次0.2节点验证通过：所有50个节点已正确注册');
    return true;
  }

  /**
   * 获取所有已注册的节点类型
   */
  public getAllRegisteredNodeTypes(): string[] {
    return [
      // 深度学习节点
      'DeepLearningModelNode', 'NeuralNetworkNode', 'CNNNode', 'RNNNode', 'LSTMNode',
      'TransformerNode', 'GANNode', 'AutoencoderNode', 'VAENode', 'ResNetNode',
      'UNetNode', 'AttentionNode', 'EmbeddingNode', 'DropoutNode', 'BatchNormNode',
      
      // 机器学习节点
      'ReinforcementLearningNode', 'DecisionTreeNode', 'RandomForestNode', 'SVMNode', 'KMeansNode',
      'LinearRegressionNode', 'LogisticRegressionNode', 'NaiveBayesNode', 'PCANode', 'GradientBoostingNode',
      
      // 计算机视觉节点
      'ImageSegmentationNode', 'ObjectDetectionNode', 'FaceRecognitionNode', 'OpticalFlowNode', 'FeatureExtractionNode',
      'ImageClassificationNode', 'EdgeDetectionNode', 'ImageFilterNode', 'StereoVisionNode', 'MotionTrackingNode',
      'ImageRegistrationNode', 'DepthEstimationNode',
      
      // 自然语言处理节点
      'TextClassificationNode', 'SentimentAnalysisNode', 'NamedEntityRecognitionNode', 'TextSummarizationNode', 'MachineTranslationNode',
      'QuestionAnsweringNode', 'TextGenerationNode', 'TokenizationNode', 'POSTaggingNode', 'SyntaxParsingNode',
      'SemanticAnalysisNode',
      
      // AI工具节点
      'ModelDeploymentNode', 'ModelTrainingNode', 'ModelEvaluationNode', 'DataPreprocessingNode', 'FeatureEngineeringNode',
      'HyperparameterTuningNode', 'ModelOptimizationNode', 'ModelVersioningNode', 'ModelMonitoringNode', 'AutoMLNode',
      'MLOpsNode', 'ModelInterpretabilityNode'
    ];
  }

  /**
   * 检查节点是否已注册
   */
  public isRegistered(): boolean {
    return this.registered;
  }

  /**
   * 重置注册状态（用于测试）
   */
  public resetRegistration(): void {
    this.registered = false;
    this.registeredNodes.clear();
  }
}

/**
 * 导出批次0.2节点注册函数
 */
export function registerBatch02Nodes(): void {
  const registry = Batch02NodesRegistry.getInstance();
  registry.registerAllNodes();
}

/**
 * 导出批次0.2节点验证函数
 */
export function validateBatch02Nodes(): boolean {
  const registry = Batch02NodesRegistry.getInstance();
  return registry.validateBatch02Nodes();
}

// 导出单例实例
export const batch02NodesRegistry = Batch02NodesRegistry.getInstance();
