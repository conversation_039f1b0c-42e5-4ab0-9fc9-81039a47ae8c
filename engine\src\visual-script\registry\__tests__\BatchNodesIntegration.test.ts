/**
 * 批次节点集成测试
 * 测试批次0.1和批次0.2节点的注册和集成功能
 */

import { NodeRegistry } from '../NodeRegistry';
import { batch01NodesRegistry, registerBatch01Nodes, validateBatch01Nodes } from '../Batch01NodesRegistry';
import { batch02NodesRegistry, registerBatch02Nodes, validateBatch02Nodes } from '../Batch02NodesRegistry';
import { registerBatchNodes } from '../NodeRegistry';

describe('批次节点集成测试', () => {
  beforeEach(() => {
    // 重置注册状态
    batch01NodesRegistry.resetRegistration();
    batch02NodesRegistry.resetRegistration();
  });

  describe('批次0.1节点注册测试', () => {
    test('应该成功注册批次0.1的200个节点', () => {
      // 注册批次0.1节点
      registerBatch01Nodes();

      // 验证注册状态
      expect(batch01NodesRegistry.isRegistered()).toBe(true);

      // 验证节点完整性
      expect(validateBatch01Nodes()).toBe(true);

      // 验证统计信息
      const stats = batch01NodesRegistry.getBatch01Statistics();
      expect(stats.totalNodes).toBe(200);
      expect(stats.categories.materialManagement).toBe(24);
      expect(stats.categories.postProcessing).toBe(17);
      expect(stats.categories.shader).toBe(18);
      expect(stats.categories.renderingOptimization).toBe(15);
      expect(stats.categories.sceneManagement).toBe(33);
      expect(stats.categories.resourceManagement).toBe(22);
      expect(stats.categories.industrialManufacturing).toBe(60);
      expect(stats.categories.otherCore).toBe(11);
    });

    test('应该防止重复注册批次0.1节点', () => {
      // 第一次注册
      registerBatch01Nodes();
      expect(batch01NodesRegistry.isRegistered()).toBe(true);

      // 第二次注册应该被跳过
      const consoleSpy = jest.spyOn(console, 'log');
      registerBatch01Nodes();
      expect(consoleSpy).toHaveBeenCalledWith('批次0.1节点已注册，跳过重复注册');
      
      consoleSpy.mockRestore();
    });

    test('应该正确分类批次0.1节点', () => {
      registerBatch01Nodes();
      
      const stats = batch01NodesRegistry.getBatch01Statistics();
      expect(stats.features).toContain('材质系统管理');
      expect(stats.features).toContain('后处理效果链');
      expect(stats.features).toContain('着色器编译器');
      expect(stats.features).toContain('场景管理系统');
      expect(stats.features).toContain('资源加载优化');
      expect(stats.features).toContain('工业制造集成');
      expect(stats.features).toContain('交互系统');
      expect(stats.features).toContain('头像系统');
      expect(stats.features).toContain('动作捕捉');
      expect(stats.features).toContain('粒子系统');
    });
  });

  describe('批次0.2节点注册测试', () => {
    test('应该成功注册批次0.2的50个AI节点', () => {
      // 注册批次0.2节点
      registerBatch02Nodes();

      // 验证注册状态
      expect(batch02NodesRegistry.isRegistered()).toBe(true);

      // 验证节点完整性
      expect(validateBatch02Nodes()).toBe(true);

      // 验证统计信息
      const stats = batch02NodesRegistry.getBatch02Statistics();
      expect(stats.totalNodes).toBe(50);
      expect(stats.categories.deepLearning).toBe(15);
      expect(stats.categories.machineLearning).toBe(10);
      expect(stats.categories.computerVision).toBe(12);
      expect(stats.categories.nlp).toBe(11);
      expect(stats.categories.aiTools).toBe(12);
    });

    test('应该防止重复注册批次0.2节点', () => {
      // 第一次注册
      registerBatch02Nodes();
      expect(batch02NodesRegistry.isRegistered()).toBe(true);

      // 第二次注册应该被跳过
      const consoleSpy = jest.spyOn(console, 'log');
      registerBatch02Nodes();
      expect(consoleSpy).toHaveBeenCalledWith('批次0.2节点已注册，跳过重复注册');
      
      consoleSpy.mockRestore();
    });

    test('应该正确分类批次0.2 AI节点', () => {
      registerBatch02Nodes();
      
      const stats = batch02NodesRegistry.getBatch02Statistics();
      expect(stats.features).toContain('深度学习模型');
      expect(stats.features).toContain('机器学习算法');
      expect(stats.features).toContain('计算机视觉');
      expect(stats.features).toContain('自然语言处理');
      expect(stats.features).toContain('AI工具集成');
      expect(stats.features).toContain('模型部署');
      expect(stats.features).toContain('数据预处理');
      expect(stats.features).toContain('特征工程');
    });

    test('应该返回所有已注册的AI节点类型', () => {
      registerBatch02Nodes();
      
      const nodeTypes = batch02NodesRegistry.getAllRegisteredNodeTypes();
      expect(nodeTypes).toHaveLength(50);
      
      // 验证深度学习节点
      expect(nodeTypes).toContain('DeepLearningModelNode');
      expect(nodeTypes).toContain('NeuralNetworkNode');
      expect(nodeTypes).toContain('CNNNode');
      expect(nodeTypes).toContain('TransformerNode');
      
      // 验证机器学习节点
      expect(nodeTypes).toContain('ReinforcementLearningNode');
      expect(nodeTypes).toContain('DecisionTreeNode');
      expect(nodeTypes).toContain('RandomForestNode');
      
      // 验证计算机视觉节点
      expect(nodeTypes).toContain('ImageSegmentationNode');
      expect(nodeTypes).toContain('ObjectDetectionNode');
      expect(nodeTypes).toContain('FaceRecognitionNode');
      
      // 验证NLP节点
      expect(nodeTypes).toContain('TextClassificationNode');
      expect(nodeTypes).toContain('SentimentAnalysisNode');
      expect(nodeTypes).toContain('MachineTranslationNode');
      
      // 验证AI工具节点
      expect(nodeTypes).toContain('ModelDeploymentNode');
      expect(nodeTypes).toContain('ModelTrainingNode');
      expect(nodeTypes).toContain('AutoMLNode');
    });
  });

  describe('批次节点统一注册测试', () => {
    test('应该成功注册所有批次节点', () => {
      // 使用统一注册函数
      registerBatchNodes();

      // 验证两个批次都已注册
      expect(batch01NodesRegistry.isRegistered()).toBe(true);
      expect(batch02NodesRegistry.isRegistered()).toBe(true);

      // 验证节点完整性
      expect(validateBatch01Nodes()).toBe(true);
      expect(validateBatch02Nodes()).toBe(true);
    });

    test('应该正确统计总节点数量', () => {
      registerBatchNodes();

      const batch01Stats = batch01NodesRegistry.getBatch01Statistics();
      const batch02Stats = batch02NodesRegistry.getBatch02Statistics();

      const totalNodes = batch01Stats.totalNodes + batch02Stats.totalNodes;
      expect(totalNodes).toBe(250); // 200 + 50
    });

    test('应该支持兼容性检查', () => {
      registerBatchNodes();

      const batch01Stats = batch01NodesRegistry.getBatch01Statistics();
      const batch02Stats = batch02NodesRegistry.getBatch02Statistics();

      // 验证编辑器兼容性
      expect(batch01Stats.compatibility.editor).toBe(true);
      expect(batch02Stats.compatibility.editor).toBe(true);

      // 验证运行时兼容性
      expect(batch01Stats.compatibility.runtime).toBe(true);
      expect(batch02Stats.compatibility.runtime).toBe(true);

      // 验证WebGL兼容性
      expect(batch01Stats.compatibility.webgl).toBe(true);
      expect(batch02Stats.compatibility.webgl).toBe(true);

      // 验证移动端兼容性
      expect(batch01Stats.compatibility.mobile).toBe(true);
      expect(batch02Stats.compatibility.mobile).toBe(true);
    });
  });

  describe('错误处理测试', () => {
    test('应该正确处理验证失败的情况', () => {
      // 模拟验证失败
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation();
      
      // 重置注册状态但不实际注册节点
      batch01NodesRegistry.resetRegistration();
      
      // 验证应该失败
      expect(validateBatch01Nodes()).toBe(false);
      
      consoleSpy.mockRestore();
    });

    test('应该正确处理重置注册状态', () => {
      // 先注册节点
      registerBatch01Nodes();
      expect(batch01NodesRegistry.isRegistered()).toBe(true);

      // 重置注册状态
      batch01NodesRegistry.resetRegistration();
      expect(batch01NodesRegistry.isRegistered()).toBe(false);

      // 重新注册应该成功
      registerBatch01Nodes();
      expect(batch01NodesRegistry.isRegistered()).toBe(true);
    });
  });
});

describe('节点注册表集成测试', () => {
  test('应该能够从主NodeRegistry访问批次节点', () => {
    // 注册批次节点
    registerBatchNodes();

    // 验证NodeRegistry能够访问注册的节点
    // 注意：这里需要根据实际的NodeRegistry实现来调整测试
    expect(batch01NodesRegistry.isRegistered()).toBe(true);
    expect(batch02NodesRegistry.isRegistered()).toBe(true);
  });
});
