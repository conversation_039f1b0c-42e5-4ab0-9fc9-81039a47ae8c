/**
 * 批次0.1补充后处理效果节点
 * 补充缺失的2个后处理节点，使总数达到17个
 */
import { BaseNode } from '../BaseNode';
import { NodeInput, NodeOutput } from '../../types/NodeTypes';

/**
 * 屏幕空间全局光照节点
 */
export class SSGINode extends BaseNode {
  public static readonly TYPE = 'SSGINode';
  public static readonly NAME = '屏幕空间全局光照';
  public static readonly DESCRIPTION = '实现屏幕空间全局光照效果，提供间接光照';

  constructor() {
    super(SSGINode.TYPE, SSGINode.NAME, SSGINode.DESCRIPTION);
    this.setupInputsAndOutputs();
  }

  private setupInputsAndOutputs(): void {
    // 输入
    this.addInput('apply', 'boolean', '应用效果', false);
    this.addInput('intensity', 'number', '强度', 1.0);
    this.addInput('radius', 'number', '半径', 0.5);
    this.addInput('steps', 'number', '步数', 16);
    this.addInput('thickness', 'number', '厚度', 0.1);
    this.addInput('maxDistance', 'number', '最大距离', 10.0);
    this.addInput('enabled', 'boolean', '启用', true);

    // 输出
    this.addOutput('effect', 'object', '效果对象');
    this.addOutput('intensity', 'number', '实际强度');
    this.addOutput('radius', 'number', '实际半径');
    this.addOutput('steps', 'number', '实际步数');
    this.addOutput('onApplied', 'boolean', '应用完成');
    this.addOutput('onError', 'boolean', '发生错误');
  }

  public execute(inputs: Record<string, any>): Record<string, any> {
    try {
      if (!inputs.apply || !inputs.enabled) {
        return {
          effect: null,
          intensity: 0,
          radius: 0,
          steps: 0,
          onApplied: false,
          onError: false
        };
      }

      const intensity = Math.max(0, Math.min(5, inputs.intensity || 1.0));
      const radius = Math.max(0.1, Math.min(2.0, inputs.radius || 0.5));
      const steps = Math.max(4, Math.min(64, inputs.steps || 16));
      const thickness = Math.max(0.01, Math.min(1.0, inputs.thickness || 0.1));
      const maxDistance = Math.max(1, Math.min(100, inputs.maxDistance || 10.0));

      const effect = {
        type: 'ssgi',
        intensity,
        radius,
        steps,
        thickness,
        maxDistance,
        timestamp: Date.now()
      };

      return {
        effect,
        intensity,
        radius,
        steps,
        onApplied: true,
        onError: false
      };

    } catch (error) {
      console.error('SSGI节点执行错误:', error);
      return {
        effect: null,
        intensity: 0,
        radius: 0,
        steps: 0,
        onApplied: false,
        onError: true
      };
    }
  }
}

/**
 * 时间抗锯齿节点 (TAA)
 */
export class TAANode extends BaseNode {
  public static readonly TYPE = 'TAANode';
  public static readonly NAME = '时间抗锯齿';
  public static readonly DESCRIPTION = '实现时间抗锯齿效果，减少闪烁和锯齿';

  constructor() {
    super(TAANode.TYPE, TAANode.NAME, TAANode.DESCRIPTION);
    this.setupInputsAndOutputs();
  }

  private setupInputsAndOutputs(): void {
    // 输入
    this.addInput('apply', 'boolean', '应用效果', false);
    this.addInput('sharpness', 'number', '锐化强度', 0.5);
    this.addInput('feedback', 'number', '反馈强度', 0.9);
    this.addInput('motionBlending', 'number', '运动混合', 0.8);
    this.addInput('jitterScale', 'number', '抖动缩放', 1.0);
    this.addInput('varianceClipping', 'boolean', '方差裁剪', true);
    this.addInput('enabled', 'boolean', '启用', true);

    // 输出
    this.addOutput('effect', 'object', '效果对象');
    this.addOutput('sharpness', 'number', '实际锐化强度');
    this.addOutput('feedback', 'number', '实际反馈强度');
    this.addOutput('motionBlending', 'number', '实际运动混合');
    this.addOutput('onApplied', 'boolean', '应用完成');
    this.addOutput('onError', 'boolean', '发生错误');
  }

  public execute(inputs: Record<string, any>): Record<string, any> {
    try {
      if (!inputs.apply || !inputs.enabled) {
        return {
          effect: null,
          sharpness: 0,
          feedback: 0,
          motionBlending: 0,
          onApplied: false,
          onError: false
        };
      }

      const sharpness = Math.max(0, Math.min(2, inputs.sharpness || 0.5));
      const feedback = Math.max(0, Math.min(1, inputs.feedback || 0.9));
      const motionBlending = Math.max(0, Math.min(1, inputs.motionBlending || 0.8));
      const jitterScale = Math.max(0.1, Math.min(2, inputs.jitterScale || 1.0));
      const varianceClipping = inputs.varianceClipping !== false;

      const effect = {
        type: 'taa',
        sharpness,
        feedback,
        motionBlending,
        jitterScale,
        varianceClipping,
        timestamp: Date.now()
      };

      return {
        effect,
        sharpness,
        feedback,
        motionBlending,
        onApplied: true,
        onError: false
      };

    } catch (error) {
      console.error('TAA节点执行错误:', error);
      return {
        effect: null,
        sharpness: 0,
        feedback: 0,
        motionBlending: 0,
        onApplied: false,
        onError: true
      };
    }
  }
}
