/**
 * 工业制造节点面板组件
 * 显示和管理工业制造相关的节点，包括6个子分类
 */

import React, { useState, useEffect, useMemo, useCallback } from 'react';
import {
  Card,
  Collapse,
  List,
  Tag,
  Space,
  Button,
  Tooltip,
  Badge,
  Typography,
  Divider,
  Empty,
  Spin
} from 'antd';
import {
  FactoryOutlined,
  SettingOutlined,
  MonitorOutlined,
  VerifiedOutlined,
  TruckOutlined,
  ThunderboltOutlined,
  InfoCircleOutlined,
  PlusOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import {
  IndustrialNodeCategory,
  INDUSTRIAL_CATEGORY_MAP,
  ALL_INDUSTRIAL_NODES,
  IndustrialNodesIntegration
} from '../nodes/IndustrialNodesIntegration';
import IndustrialNodesSearch from './IndustrialNodesSearch';

const { Panel } = Collapse;
const { Text, Title } = Typography;
const { Search } = Input;

// 分类图标映射
const CATEGORY_ICONS = {
  [IndustrialNodeCategory.MES_SYSTEM]: <FactoryOutlined />,
  [IndustrialNodeCategory.DEVICE_MANAGEMENT]: <SettingOutlined />,
  [IndustrialNodeCategory.PREDICTIVE_MAINTENANCE]: <MonitorOutlined />,
  [IndustrialNodeCategory.QUALITY_MANAGEMENT]: <VerifiedOutlined />,
  [IndustrialNodeCategory.SUPPLY_CHAIN_MANAGEMENT]: <TruckOutlined />,
  [IndustrialNodeCategory.ENERGY_MANAGEMENT]: <ThunderboltOutlined />
};

interface IndustrialNodesPanelProps {
  integration?: IndustrialNodesIntegration;
  onNodeSelect?: (nodeType: string, nodeConfig: any) => void;
  onNodeAdd?: (nodeType: string, position?: { x: number; y: number }) => void;
  visible?: boolean;
  loading?: boolean;
}

interface NodeItem {
  type: string;
  name: string;
  description: string;
  category: IndustrialNodeCategory;
  icon: string;
  color: string;
  tags: string[];
}

export const IndustrialNodesPanel: React.FC<IndustrialNodesPanelProps> = ({
  integration,
  onNodeSelect,
  onNodeAdd,
  visible = true,
  loading = false
}) => {
  const { t } = useTranslation();
  const [expandedPanels, setExpandedPanels] = useState<string[]>([]);
  const [nodeItems, setNodeItems] = useState<NodeItem[]>([]);
  const [filteredNodes, setFilteredNodes] = useState<NodeItem[]>([]);

  // 初始化节点数据
  useEffect(() => {
    if (integration) {
      const items: NodeItem[] = [];
      const registeredNodes = integration.getRegisteredNodes();
      
      for (const [nodeType, nodeConfig] of registeredNodes.entries()) {
        items.push({
          type: nodeType,
          name: nodeConfig.name || nodeType,
          description: nodeConfig.description || '',
          category: nodeConfig.category,
          icon: nodeConfig.icon || 'setting',
          color: nodeConfig.color || '#666',
          tags: nodeConfig.tags || []
        });
      }
      
      setNodeItems(items);
      
      // 默认展开所有面板
      setExpandedPanels(Object.keys(INDUSTRIAL_CATEGORY_MAP));
    }
  }, [integration]);

  // 处理过滤节点变化
  const handleFilteredNodesChange = useCallback((filtered: NodeItem[]) => {
    setFilteredNodes(filtered);
  }, []);

  // 按分类分组节点
  const nodesByCategory = useMemo(() => {
    const grouped: { [key in IndustrialNodeCategory]: NodeItem[] } = {
      [IndustrialNodeCategory.MES_SYSTEM]: [],
      [IndustrialNodeCategory.DEVICE_MANAGEMENT]: [],
      [IndustrialNodeCategory.PREDICTIVE_MAINTENANCE]: [],
      [IndustrialNodeCategory.QUALITY_MANAGEMENT]: [],
      [IndustrialNodeCategory.SUPPLY_CHAIN_MANAGEMENT]: [],
      [IndustrialNodeCategory.ENERGY_MANAGEMENT]: []
    };

    filteredNodes.forEach(node => {
      if (grouped[node.category]) {
        grouped[node.category].push(node);
      }
    });

    return grouped;
  }, [filteredNodes]);

  // 处理节点点击
  const handleNodeClick = (node: NodeItem) => {
    if (onNodeSelect && integration) {
      const nodeConfig = integration.getNodeConfig(node.type);
      onNodeSelect(node.type, nodeConfig);
    }
  };

  // 处理节点添加
  const handleNodeAdd = (node: NodeItem) => {
    if (onNodeAdd) {
      onNodeAdd(node.type);
    }
  };

  // 渲染节点项
  const renderNodeItem = (node: NodeItem) => (
    <List.Item
      key={node.type}
      className="industrial-node-item"
      style={{
        padding: '8px 12px',
        cursor: 'pointer',
        borderRadius: '4px',
        marginBottom: '4px',
        border: '1px solid #f0f0f0',
        transition: 'all 0.2s'
      }}
      onClick={() => handleNodeClick(node)}
      onMouseEnter={(e) => {
        e.currentTarget.style.backgroundColor = '#f5f5f5';
        e.currentTarget.style.borderColor = node.color;
      }}
      onMouseLeave={(e) => {
        e.currentTarget.style.backgroundColor = 'transparent';
        e.currentTarget.style.borderColor = '#f0f0f0';
      }}
    >
      <List.Item.Meta
        avatar={
          <div
            style={{
              width: 32,
              height: 32,
              borderRadius: '50%',
              backgroundColor: node.color,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              color: 'white',
              fontSize: '14px'
            }}
          >
            {node.icon}
          </div>
        }
        title={
          <Space>
            <Text strong style={{ fontSize: '13px' }}>
              {node.name}
            </Text>
            {node.tags.length > 0 && (
              <Tag size="small" color={node.color}>
                {node.tags[0]}
              </Tag>
            )}
          </Space>
        }
        description={
          <Text type="secondary" style={{ fontSize: '12px' }}>
            {node.description.length > 50 
              ? `${node.description.substring(0, 50)}...` 
              : node.description
            }
          </Text>
        }
      />
      <Space>
        <Tooltip title="查看详情">
          <Button
            type="text"
            size="small"
            icon={<InfoCircleOutlined />}
            onClick={(e) => {
              e.stopPropagation();
              handleNodeClick(node);
            }}
          />
        </Tooltip>
        <Tooltip title="添加到画布">
          <Button
            type="text"
            size="small"
            icon={<PlusOutlined />}
            onClick={(e) => {
              e.stopPropagation();
              handleNodeAdd(node);
            }}
          />
        </Tooltip>
      </Space>
    </List.Item>
  );

  // 渲染分类面板
  const renderCategoryPanel = (category: IndustrialNodeCategory) => {
    const categoryInfo = INDUSTRIAL_CATEGORY_MAP[category];
    const categoryNodes = nodesByCategory[category];
    const icon = CATEGORY_ICONS[category];

    return (
      <Panel
        key={category}
        header={
          <Space>
            {icon}
            <Text strong>{categoryInfo.displayName}</Text>
            <Badge count={categoryNodes.length} style={{ backgroundColor: categoryInfo.color }} />
          </Space>
        }
        extra={
          <Tooltip title={categoryInfo.description}>
            <InfoCircleOutlined style={{ color: categoryInfo.color }} />
          </Tooltip>
        }
      >
        {categoryNodes.length > 0 ? (
          <List
            dataSource={categoryNodes}
            renderItem={renderNodeItem}
            size="small"
          />
        ) : (
          <Empty
            image={Empty.PRESENTED_IMAGE_SIMPLE}
            description="暂无节点"
            style={{ margin: '20px 0' }}
          />
        )}
      </Panel>
    );
  };

  if (!visible) {
    return null;
  }

  return (
    <Card
      title={
        <Space>
          <FactoryOutlined />
          <Title level={4} style={{ margin: 0 }}>
            工业制造节点
          </Title>
          <Badge count={filteredNodes.length} style={{ backgroundColor: '#52c41a' }} />
        </Space>
      }
      size="small"
      style={{ height: '100%', overflow: 'hidden' }}
      bodyStyle={{ padding: '12px', height: 'calc(100% - 57px)', overflow: 'auto' }}
    >
      <Spin spinning={loading}>
        {/* 搜索和过滤组件 */}
        <IndustrialNodesSearch
          nodes={nodeItems}
          onFilteredNodesChange={handleFilteredNodesChange}
        />

        <Divider style={{ margin: '8px 0' }} />

        {/* 节点分类面板 */}
        <Collapse
          activeKey={expandedPanels}
          onChange={(keys) => setExpandedPanels(keys as string[])}
          size="small"
          ghost
        >
          {Object.values(IndustrialNodeCategory).map(renderCategoryPanel)}
        </Collapse>
      </Spin>
    </Card>
  );
};

export default IndustrialNodesPanel;
