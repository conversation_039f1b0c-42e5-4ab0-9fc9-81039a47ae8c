/**
 * 批次0.1节点注册表测试
 * 测试节点注册表的功能和完整性
 */
import { describe, test, expect, beforeEach, afterEach, jest } from '@jest/globals';
import { Batch01NodesRegistry, registerBatch01Nodes, validateBatch01Nodes } from '../Batch01NodesRegistry';

// 模拟NodeRegistry
jest.mock('../NodeRegistry', () => ({
  NodeRegistry: {
    getInstance: jest.fn().mockReturnValue({
      registerNode: jest.fn()
    })
  }
}));

describe('批次0.1节点注册表测试', () => {
  let registry: Batch01NodesRegistry;

  beforeEach(() => {
    jest.clearAllMocks();
    // 重置单例实例
    (Batch01NodesRegistry as any).instance = undefined;
    registry = Batch01NodesRegistry.getInstance();
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  describe('单例模式测试', () => {
    test('应该正确实现单例模式', () => {
      const instance1 = Batch01NodesRegistry.getInstance();
      const instance2 = Batch01NodesRegistry.getInstance();
      
      expect(instance1).toBe(instance2);
      expect(instance1).toBeInstanceOf(Batch01NodesRegistry);
    });

    test('应该在多次调用时返回同一实例', () => {
      const instances = [];
      for (let i = 0; i < 10; i++) {
        instances.push(Batch01NodesRegistry.getInstance());
      }
      
      const firstInstance = instances[0];
      instances.forEach(instance => {
        expect(instance).toBe(firstInstance);
      });
    });
  });

  describe('节点注册测试', () => {
    test('应该正确注册所有节点', () => {
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();
      
      registry.registerAllNodes();
      
      expect(consoleSpy).toHaveBeenCalledWith('批次0.1渲染系统节点注册完成');
      expect(consoleSpy).toHaveBeenCalledWith('材质管理节点：24个');
      expect(consoleSpy).toHaveBeenCalledWith('后处理效果节点：15个');
      expect(consoleSpy).toHaveBeenCalledWith('着色器节点：15个');
      expect(consoleSpy).toHaveBeenCalledWith('渲染优化节点：15个');
      expect(consoleSpy).toHaveBeenCalledWith('总计：69个节点');
      
      consoleSpy.mockRestore();
    });

    test('应该返回正确的已注册节点类型', () => {
      registry.registerAllNodes();
      const registeredTypes = registry.getAllRegisteredNodeTypes();
      
      expect(Array.isArray(registeredTypes)).toBe(true);
      expect(registeredTypes.length).toBeGreaterThan(0);
      
      // 检查是否包含各类节点
      expect(registeredTypes.some(type => type.includes('Material'))).toBe(true);
      expect(registeredTypes.some(type => type.includes('Effect'))).toBe(true);
      expect(registeredTypes.some(type => type.includes('Shader'))).toBe(true);
      expect(registeredTypes.some(type => type.includes('LOD') || type.includes('Culling'))).toBe(true);
    });

    test('应该正确处理重复注册', () => {
      const warnSpy = jest.spyOn(console, 'warn').mockImplementation();
      
      registry.registerAllNodes();
      registry.registerAllNodes(); // 重复注册
      
      expect(warnSpy).toHaveBeenCalled();
      expect(warnSpy.mock.calls.some(call => 
        call[0].includes('已经注册，跳过重复注册')
      )).toBe(true);
      
      warnSpy.mockRestore();
    });
  });

  describe('节点验证测试', () => {
    test('应该正确验证节点完整性', () => {
      registry.registerAllNodes();
      const isValid = registry.validateBatch01Nodes();
      
      expect(isValid).toBe(true);
    });

    test('应该在节点不完整时返回false', () => {
      const errorSpy = jest.spyOn(console, 'error').mockImplementation();
      
      // 不注册节点，直接验证
      const isValid = registry.validateBatch01Nodes();
      
      expect(isValid).toBe(false);
      expect(errorSpy).toHaveBeenCalledWith(
        expect.stringContaining('批次0.1节点注册不完整')
      );
      
      errorSpy.mockRestore();
    });

    test('应该正确记录验证成功', () => {
      const logSpy = jest.spyOn(console, 'log').mockImplementation();
      
      registry.registerAllNodes();
      registry.validateBatch01Nodes();
      
      expect(logSpy).toHaveBeenCalledWith(
        '批次0.1节点验证通过：所有69个节点已正确注册'
      );
      
      logSpy.mockRestore();
    });
  });

  describe('统计信息测试', () => {
    test('应该返回正确的统计信息', () => {
      const stats = registry.getBatch01Statistics();
      
      expect(stats).toBeDefined();
      expect(typeof stats).toBe('object');
      
      // 检查基本统计信息
      expect(stats.totalNodes).toBe(69);
      expect(stats.categories).toBeDefined();
      expect(stats.categories.materialManagement).toBe(24);
      expect(stats.categories.postProcessing).toBe(15);
      expect(stats.categories.shader).toBe(15);
      expect(stats.categories.renderingOptimization).toBe(15);
      
      // 检查功能列表
      expect(Array.isArray(stats.features)).toBe(true);
      expect(stats.features.length).toBeGreaterThan(0);
      expect(stats.features).toContain('材质系统管理');
      expect(stats.features).toContain('后处理效果链');
      expect(stats.features).toContain('着色器编译器');
      expect(stats.features).toContain('LOD系统');
      
      // 检查兼容性信息
      expect(stats.compatibility).toBeDefined();
      expect(stats.compatibility.editor).toBe(true);
      expect(stats.compatibility.runtime).toBe(true);
      expect(stats.compatibility.webgl).toBe(true);
      expect(stats.compatibility.mobile).toBe(true);
    });

    test('统计信息应该保持一致', () => {
      const stats1 = registry.getBatch01Statistics();
      const stats2 = registry.getBatch01Statistics();
      
      expect(stats1).toEqual(stats2);
    });
  });

  describe('节点图标和标签测试', () => {
    test('应该为材质节点返回正确的图标', () => {
      const getNodeIcon = (registry as any).getNodeIcon;
      
      expect(getNodeIcon('MaterialSystem')).toBe('palette');
      expect(getNodeIcon('CreateMaterial')).toBe('palette');
      expect(getNodeIcon('MaterialEditor')).toBe('palette');
    });

    test('应该为着色器节点返回正确的图标', () => {
      const getNodeIcon = (registry as any).getNodeIcon;
      
      expect(getNodeIcon('VertexShader')).toBe('code');
      expect(getNodeIcon('FragmentShader')).toBe('code');
      expect(getNodeIcon('ShaderCompiler')).toBe('code');
    });

    test('应该为后处理节点返回正确的图标', () => {
      const getNodeIcon = (registry as any).getNodeIcon;
      
      expect(getNodeIcon('BloomEffect')).toBe('filter');
      expect(getNodeIcon('BlurEffect')).toBe('filter');
      expect(getNodeIcon('ColorGradingProcess')).toBe('filter');
    });

    test('应该为优化节点返回正确的图标', () => {
      const getNodeIcon = (registry as any).getNodeIcon;
      
      expect(getNodeIcon('LODOptimization')).toBe('speed');
      expect(getNodeIcon('FrustumCulling')).toBe('speed');
      expect(getNodeIcon('BatchOptimization')).toBe('speed');
    });

    test('应该为未知节点返回默认图标', () => {
      const getNodeIcon = (registry as any).getNodeIcon;
      
      expect(getNodeIcon('UnknownNode')).toBe('extension');
      expect(getNodeIcon('')).toBe('extension');
    });

    test('应该返回正确的节点标签', () => {
      const getNodeTags = (registry as any).getNodeTags;
      
      const materialTags = getNodeTags('MaterialSystem');
      expect(materialTags).toContain('batch01');
      expect(materialTags).toContain('rendering');
      expect(materialTags).toContain('material');
      expect(materialTags).toContain('graphics');
      
      const shaderTags = getNodeTags('VertexShader');
      expect(shaderTags).toContain('batch01');
      expect(shaderTags).toContain('rendering');
      expect(shaderTags).toContain('shader');
      expect(shaderTags).toContain('glsl');
      
      const effectTags = getNodeTags('BloomEffect');
      expect(effectTags).toContain('batch01');
      expect(effectTags).toContain('rendering');
      expect(effectTags).toContain('postprocess');
      expect(effectTags).toContain('effect');
      
      const optimizationTags = getNodeTags('LODOptimization');
      expect(optimizationTags).toContain('batch01');
      expect(optimizationTags).toContain('rendering');
      expect(optimizationTags).toContain('optimization');
      expect(optimizationTags).toContain('performance');
    });
  });

  describe('导出函数测试', () => {
    test('registerBatch01Nodes函数应该正确工作', () => {
      const logSpy = jest.spyOn(console, 'log').mockImplementation();
      
      registerBatch01Nodes();
      
      expect(logSpy).toHaveBeenCalledWith('批次0.1渲染系统节点注册完成');
      
      logSpy.mockRestore();
    });

    test('validateBatch01Nodes函数应该正确工作', () => {
      // 先注册节点
      registerBatch01Nodes();
      
      // 然后验证
      const isValid = validateBatch01Nodes();
      expect(isValid).toBe(true);
    });

    test('validateBatch01Nodes在未注册时应该返回false', () => {
      const errorSpy = jest.spyOn(console, 'error').mockImplementation();
      
      const isValid = validateBatch01Nodes();
      expect(isValid).toBe(false);
      
      errorSpy.mockRestore();
    });
  });

  describe('错误处理测试', () => {
    test('应该处理NodeRegistry获取失败', () => {
      // 模拟NodeRegistry.getInstance抛出错误
      const originalGetInstance = require('../NodeRegistry').NodeRegistry.getInstance;
      require('../NodeRegistry').NodeRegistry.getInstance = jest.fn().mockImplementation(() => {
        throw new Error('NodeRegistry初始化失败');
      });

      expect(() => {
        Batch01NodesRegistry.getInstance();
      }).not.toThrow(); // 应该优雅地处理错误

      // 恢复原始方法
      require('../NodeRegistry').NodeRegistry.getInstance = originalGetInstance;
    });

    test('应该处理节点注册过程中的错误', () => {
      const errorSpy = jest.spyOn(console, 'error').mockImplementation();
      
      // 模拟registerNode方法抛出错误
      const mockNodeRegistry = {
        registerNode: jest.fn().mockImplementation(() => {
          throw new Error('节点注册失败');
        })
      };
      
      (registry as any).nodeRegistry = mockNodeRegistry;
      
      expect(() => {
        registry.registerAllNodes();
      }).not.toThrow(); // 应该优雅地处理错误
      
      errorSpy.mockRestore();
    });
  });

  describe('内存管理测试', () => {
    test('应该正确管理注册的节点映射', () => {
      registry.registerAllNodes();
      
      const registeredNodes = (registry as any).registeredNodes;
      expect(registeredNodes).toBeInstanceOf(Map);
      expect(registeredNodes.size).toBeGreaterThan(0);
      
      // 检查映射的完整性
      const nodeTypes = registry.getAllRegisteredNodeTypes();
      expect(nodeTypes.length).toBe(registeredNodes.size);
    });

    test('重复创建实例不应该造成内存泄漏', () => {
      const instances = [];
      for (let i = 0; i < 100; i++) {
        instances.push(Batch01NodesRegistry.getInstance());
      }
      
      // 所有实例应该是同一个对象
      const firstInstance = instances[0];
      instances.forEach(instance => {
        expect(instance).toBe(firstInstance);
      });
    });
  });

  describe('并发安全测试', () => {
    test('并发调用getInstance应该返回同一实例', async () => {
      const promises = [];
      for (let i = 0; i < 10; i++) {
        promises.push(Promise.resolve(Batch01NodesRegistry.getInstance()));
      }
      
      const instances = await Promise.all(promises);
      const firstInstance = instances[0];
      
      instances.forEach(instance => {
        expect(instance).toBe(firstInstance);
      });
    });

    test('并发注册节点应该安全执行', async () => {
      const promises = [];
      for (let i = 0; i < 5; i++) {
        promises.push(Promise.resolve().then(() => {
          registry.registerAllNodes();
          return registry.getAllRegisteredNodeTypes().length;
        }));
      }
      
      const results = await Promise.all(promises);
      
      // 所有结果应该一致
      const firstResult = results[0];
      results.forEach(result => {
        expect(result).toBe(firstResult);
      });
    });
  });
});
