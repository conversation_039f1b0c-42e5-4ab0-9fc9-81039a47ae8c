/**
 * 批次3.4扩展节点注册表单元测试
 */

import { Batch34ExtensionNodesRegistry } from '../Batch34ExtensionNodesRegistry';
import { NodeRegistry } from '../../../registry/NodeRegistry';

// Mock NodeRegistry
jest.mock('../../../registry/NodeRegistry', () => ({
  NodeRegistry: {
    registerNode: jest.fn()
  }
}));

describe('Batch34ExtensionNodesRegistry', () => {
  let registry: Batch34ExtensionNodesRegistry;

  beforeEach(() => {
    registry = Batch34ExtensionNodesRegistry.getInstance();
    registry.resetRegistration();
    jest.clearAllMocks();
  });

  describe('单例模式', () => {
    test('应该返回同一个实例', () => {
      const instance1 = Batch34ExtensionNodesRegistry.getInstance();
      const instance2 = Batch34ExtensionNodesRegistry.getInstance();
      
      expect(instance1).toBe(instance2);
    });
  });

  describe('节点注册', () => {
    test('应该注册所有批次3.4扩展节点', () => {
      registry.registerAllNodes();

      // 验证注册状态
      expect(registry.isRegistered()).toBe(true);

      // 验证NodeRegistry.registerNode被调用了11次（6个支付节点 + 5个第三方节点）
      expect(NodeRegistry.registerNode).toHaveBeenCalledTimes(11);
    });

    test('应该注册支付系统节点', () => {
      registry.registerAllNodes();

      // 验证支付网关节点注册
      expect(NodeRegistry.registerNode).toHaveBeenCalledWith('PaymentGateway', expect.objectContaining({
        name: '支付网关',
        description: '处理各种支付方式的统一接口',
        category: '支付系统',
        color: '#4CAF50'
      }));

      // 验证订阅系统节点注册
      expect(NodeRegistry.registerNode).toHaveBeenCalledWith('Subscription', expect.objectContaining({
        name: '订阅系统',
        description: '处理订阅服务的创建、管理和计费',
        category: '支付系统',
        color: '#2196F3'
      }));

      // 验证应用内购买节点注册
      expect(NodeRegistry.registerNode).toHaveBeenCalledWith('InAppPurchase', expect.objectContaining({
        name: '应用内购买',
        description: '处理移动应用内购买和虚拟商品交易',
        category: '支付系统',
        color: '#FF9800'
      }));

      // 验证钱包系统节点注册
      expect(NodeRegistry.registerNode).toHaveBeenCalledWith('WalletSystem', expect.objectContaining({
        name: '钱包系统',
        description: '处理用户钱包余额、充值、提现等操作',
        category: '支付系统',
        color: '#9C27B0'
      }));

      // 验证交易历史节点注册
      expect(NodeRegistry.registerNode).toHaveBeenCalledWith('TransactionHistory', expect.objectContaining({
        name: '交易历史',
        description: '查询和管理用户的交易历史记录',
        category: '支付系统',
        color: '#607D8B'
      }));

      // 验证支付分析节点注册
      expect(NodeRegistry.registerNode).toHaveBeenCalledWith('PaymentAnalytics', expect.objectContaining({
        name: '支付分析',
        description: '提供支付数据的统计分析和报表功能',
        category: '支付系统',
        color: '#795548'
      }));
    });

    test('应该注册第三方集成节点', () => {
      registry.registerAllNodes();

      // 验证Google服务节点注册
      expect(NodeRegistry.registerNode).toHaveBeenCalledWith('GoogleServices', expect.objectContaining({
        name: 'Google服务',
        description: '集成Google各种服务API',
        category: '第三方集成',
        color: '#4285F4'
      }));

      // 验证Facebook集成节点注册
      expect(NodeRegistry.registerNode).toHaveBeenCalledWith('FacebookIntegration', expect.objectContaining({
        name: 'Facebook集成',
        description: '集成Facebook Graph API和社交功能',
        category: '第三方集成',
        color: '#1877F2'
      }));

      // 验证Twitter集成节点注册
      expect(NodeRegistry.registerNode).toHaveBeenCalledWith('TwitterIntegration', expect.objectContaining({
        name: 'Twitter集成',
        description: '集成Twitter API v2功能',
        category: '第三方集成',
        color: '#1DA1F2'
      }));

      // 验证云存储节点注册
      expect(NodeRegistry.registerNode).toHaveBeenCalledWith('CloudStorage', expect.objectContaining({
        name: '云存储',
        description: '集成各种云存储服务（AWS S3、Azure Blob、Google Cloud Storage等）',
        category: '第三方集成',
        color: '#FF6F00'
      }));

      // 验证分析集成节点注册
      expect(NodeRegistry.registerNode).toHaveBeenCalledWith('AnalyticsIntegration', expect.objectContaining({
        name: '分析集成',
        description: '集成各种分析服务（Google Analytics、Mixpanel、Amplitude等）',
        category: '第三方集成',
        color: '#E91E63'
      }));
    });

    test('应该防止重复注册', () => {
      registry.registerAllNodes();
      registry.registerAllNodes(); // 第二次调用

      // 验证NodeRegistry.registerNode只被调用了11次，不是22次
      expect(NodeRegistry.registerNode).toHaveBeenCalledTimes(11);
    });
  });

  describe('节点类型管理', () => {
    test('应该返回所有已注册的节点类型', () => {
      const nodeTypes = registry.getAllRegisteredNodeTypes();

      expect(nodeTypes).toHaveLength(11);
      expect(nodeTypes).toContain('PaymentGateway');
      expect(nodeTypes).toContain('Subscription');
      expect(nodeTypes).toContain('InAppPurchase');
      expect(nodeTypes).toContain('WalletSystem');
      expect(nodeTypes).toContain('TransactionHistory');
      expect(nodeTypes).toContain('PaymentAnalytics');
      expect(nodeTypes).toContain('GoogleServices');
      expect(nodeTypes).toContain('FacebookIntegration');
      expect(nodeTypes).toContain('TwitterIntegration');
      expect(nodeTypes).toContain('CloudStorage');
      expect(nodeTypes).toContain('AnalyticsIntegration');
    });

    test('应该正确报告注册状态', () => {
      expect(registry.isRegistered()).toBe(false);
      
      registry.registerAllNodes();
      
      expect(registry.isRegistered()).toBe(true);
    });

    test('应该能够重置注册状态', () => {
      registry.registerAllNodes();
      expect(registry.isRegistered()).toBe(true);
      
      registry.resetRegistration();
      expect(registry.isRegistered()).toBe(false);
    });
  });

  describe('节点配置验证', () => {
    test('支付系统节点应该有正确的输入输出配置', () => {
      registry.registerAllNodes();

      // 验证支付网关节点的输入配置
      const paymentGatewayCall = (NodeRegistry.registerNode as jest.Mock).mock.calls.find(
        call => call[0] === 'PaymentGateway'
      );
      
      expect(paymentGatewayCall[1].inputs).toEqual(expect.arrayContaining([
        expect.objectContaining({ name: 'amount', type: 'number' }),
        expect.objectContaining({ name: 'currency', type: 'string' }),
        expect.objectContaining({ name: 'paymentMethod', type: 'string' }),
        expect.objectContaining({ name: 'merchantId', type: 'string' }),
        expect.objectContaining({ name: 'orderId', type: 'string' })
      ]));

      // 验证支付网关节点的输出配置
      expect(paymentGatewayCall[1].outputs).toEqual(expect.arrayContaining([
        expect.objectContaining({ name: 'transactionId', type: 'string' }),
        expect.objectContaining({ name: 'status', type: 'string' }),
        expect.objectContaining({ name: 'success', type: 'boolean' }),
        expect.objectContaining({ name: 'errorMessage', type: 'string' })
      ]));
    });

    test('第三方集成节点应该有正确的输入输出配置', () => {
      registry.registerAllNodes();

      // 验证Google服务节点的输入配置
      const googleServicesCall = (NodeRegistry.registerNode as jest.Mock).mock.calls.find(
        call => call[0] === 'GoogleServices'
      );
      
      expect(googleServicesCall[1].inputs).toEqual(expect.arrayContaining([
        expect.objectContaining({ name: 'service', type: 'string' }),
        expect.objectContaining({ name: 'action', type: 'string' }),
        expect.objectContaining({ name: 'apiKey', type: 'string' }),
        expect.objectContaining({ name: 'clientId', type: 'string' })
      ]));

      // 验证Google服务节点的输出配置
      expect(googleServicesCall[1].outputs).toEqual(expect.arrayContaining([
        expect.objectContaining({ name: 'result', type: 'object' }),
        expect.objectContaining({ name: 'success', type: 'boolean' }),
        expect.objectContaining({ name: 'errorMessage', type: 'string' })
      ]));
    });
  });

  describe('节点创建函数', () => {
    test('每个节点应该有有效的创建函数', () => {
      registry.registerAllNodes();

      const allCalls = (NodeRegistry.registerNode as jest.Mock).mock.calls;
      
      allCalls.forEach(call => {
        const [nodeType, config] = call;
        expect(config.createNode).toBeDefined();
        expect(typeof config.createNode).toBe('function');
        
        // 尝试创建节点实例
        const nodeInstance = config.createNode();
        expect(nodeInstance).toBeDefined();
        expect(nodeInstance.name).toBeDefined();
        expect(nodeInstance.description).toBeDefined();
      });
    });
  });
});
