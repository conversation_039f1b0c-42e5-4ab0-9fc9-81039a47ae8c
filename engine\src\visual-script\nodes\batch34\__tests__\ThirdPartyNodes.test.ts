/**
 * 第三方集成节点单元测试
 */

import {
  GoogleServicesNode,
  FacebookIntegrationNode,
  TwitterIntegrationNode,
  CloudStorageNode,
  AnalyticsIntegrationNode
} from '../ThirdPartyNodes';

describe('第三方集成节点测试', () => {
  describe('GoogleServicesNode', () => {
    let node: GoogleServicesNode;

    beforeEach(() => {
      node = new GoogleServicesNode();
    });

    test('应该正确初始化', () => {
      expect(node.name).toBe('Google服务');
      expect(node.description).toBe('集成Google各种服务API');
      expect(node.category).toBe('第三方集成');
    });

    test('应该处理Google认证登录', () => {
      const inputs = {
        service: 'auth',
        action: 'login',
        clientId: 'client_123',
        scopes: ['profile', 'email']
      };

      const result = node.execute(inputs);

      expect(result.success).toBe(true);
      expect(result.accessToken).toBeDefined();
      expect(result.userInfo).toBeDefined();
      expect(result.userInfo.email).toBe('<EMAIL>');
    });

    test('应该处理Google地图地理编码', () => {
      const inputs = {
        service: 'maps',
        action: 'geocode',
        apiKey: 'api_key_123',
        query: 'San Francisco'
      };

      const result = node.execute(inputs);

      expect(result.success).toBe(true);
      expect(result.result.lat).toBeDefined();
      expect(result.result.lng).toBeDefined();
      expect(result.result.address).toBe('San Francisco');
    });

    test('应该拒绝不支持的服务类型', () => {
      const inputs = {
        service: 'invalid_service'
      };

      const result = node.execute(inputs);

      expect(result.success).toBe(false);
      expect(result.errorMessage).toBe('不支持的Google服务类型');
    });
  });

  describe('FacebookIntegrationNode', () => {
    let node: FacebookIntegrationNode;

    beforeEach(() => {
      node = new FacebookIntegrationNode();
    });

    test('应该正确初始化', () => {
      expect(node.name).toBe('Facebook集成');
      expect(node.description).toBe('集成Facebook Graph API和社交功能');
      expect(node.category).toBe('第三方集成');
    });

    test('应该处理Facebook登录', () => {
      const inputs = {
        action: 'login',
        appId: 'app_123',
        permissions: ['public_profile', 'email']
      };

      const result = node.execute(inputs);

      expect(result.success).toBe(true);
      expect(result.userProfile).toBeDefined();
      expect(result.userProfile.name).toBe('John Doe');
      expect(result.responseData.accessToken).toBeDefined();
    });

    test('应该处理发布内容', () => {
      const inputs = {
        action: 'post',
        accessToken: 'token_123',
        postContent: 'Hello Facebook!'
      };

      const result = node.execute(inputs);

      expect(result.success).toBe(true);
      expect(result.postId).toBeDefined();
      expect(result.result).toBe('post_created');
    });

    test('应该拒绝缺少应用ID的登录', () => {
      const inputs = {
        action: 'login'
        // 缺少 appId
      };

      const result = node.execute(inputs);

      expect(result.success).toBe(false);
      expect(result.errorMessage).toBe('Facebook应用ID缺失');
    });
  });

  describe('TwitterIntegrationNode', () => {
    let node: TwitterIntegrationNode;

    beforeEach(() => {
      node = new TwitterIntegrationNode();
    });

    test('应该正确初始化', () => {
      expect(node.name).toBe('Twitter集成');
      expect(node.description).toBe('集成Twitter API v2功能');
      expect(node.category).toBe('第三方集成');
    });

    test('应该发布推文', () => {
      const inputs = {
        action: 'tweet',
        apiKey: 'api_key_123',
        accessToken: 'token_123',
        tweetText: 'Hello Twitter!'
      };

      const result = node.execute(inputs);

      expect(result.success).toBe(true);
      expect(result.tweetData).toBeDefined();
      expect(result.tweetData.text).toBe('Hello Twitter!');
      expect(result.result).toBe('tweet_posted');
    });

    test('应该搜索推文', () => {
      const inputs = {
        action: 'search',
        apiKey: 'api_key_123',
        accessToken: 'token_123',
        query: 'javascript'
      };

      const result = node.execute(inputs);

      expect(result.success).toBe(true);
      expect(result.searchResults).toBeDefined();
      expect(Array.isArray(result.searchResults)).toBe(true);
      expect(result.searchResults.length).toBeGreaterThan(0);
    });

    test('应该拒绝超长推文', () => {
      const inputs = {
        action: 'tweet',
        apiKey: 'api_key_123',
        accessToken: 'token_123',
        tweetText: 'a'.repeat(300) // 超过280字符限制
      };

      const result = node.execute(inputs);

      expect(result.success).toBe(false);
      expect(result.errorMessage).toBe('推文内容超过280字符限制');
    });
  });

  describe('CloudStorageNode', () => {
    let node: CloudStorageNode;

    beforeEach(() => {
      node = new CloudStorageNode();
    });

    test('应该正确初始化', () => {
      expect(node.name).toBe('云存储');
      expect(node.description).toBe('集成各种云存储服务（AWS S3、Azure Blob、Google Cloud Storage等）');
      expect(node.category).toBe('第三方集成');
    });

    test('应该上传文件', () => {
      const inputs = {
        provider: 'aws',
        action: 'upload',
        accessKey: 'access_key_123',
        secretKey: 'secret_key_123',
        bucket: 'my-bucket',
        fileName: 'test.txt',
        fileData: 'Hello World!'
      };

      const result = node.execute(inputs);

      expect(result.success).toBe(true);
      expect(result.fileUrl).toBeDefined();
      expect(result.fileInfo).toBeDefined();
      expect(result.fileInfo.name).toBe('test.txt');
      expect(result.result).toBe('upload_success');
    });

    test('应该列出文件', () => {
      const inputs = {
        provider: 'aws',
        action: 'list',
        accessKey: 'access_key_123',
        secretKey: 'secret_key_123',
        bucket: 'my-bucket'
      };

      const result = node.execute(inputs);

      expect(result.success).toBe(true);
      expect(result.fileList).toBeDefined();
      expect(Array.isArray(result.fileList)).toBe(true);
      expect(result.result).toBe('list_success');
    });

    test('应该拒绝缺少认证信息的请求', () => {
      const inputs = {
        action: 'upload',
        fileName: 'test.txt'
        // 缺少认证信息
      };

      const result = node.execute(inputs);

      expect(result.success).toBe(false);
      expect(result.errorMessage).toBe('云存储认证信息或存储桶名称缺失');
    });
  });

  describe('AnalyticsIntegrationNode', () => {
    let node: AnalyticsIntegrationNode;

    beforeEach(() => {
      node = new AnalyticsIntegrationNode();
    });

    test('应该正确初始化', () => {
      expect(node.name).toBe('分析集成');
      expect(node.description).toBe('集成各种分析服务（Google Analytics、Mixpanel、Amplitude等）');
      expect(node.category).toBe('第三方集成');
    });

    test('应该跟踪事件', () => {
      const inputs = {
        provider: 'google',
        action: 'track',
        trackingId: 'GA-123456',
        eventName: 'button_click',
        eventProperties: { button_id: 'submit' },
        userId: 'user_123'
      };

      const result = node.execute(inputs);

      expect(result.success).toBe(true);
      expect(result.eventId).toBeDefined();
      expect(result.result.eventName).toBe('button_click');
      expect(result.result.userId).toBe('user_123');
    });

    test('应该识别用户', () => {
      const inputs = {
        provider: 'mixpanel',
        action: 'identify',
        apiKey: 'api_key_123',
        userId: 'user_123',
        userProperties: { name: 'John Doe', email: '<EMAIL>' }
      };

      const result = node.execute(inputs);

      expect(result.success).toBe(true);
      expect(result.userProfile).toBeDefined();
      expect(result.userProfile.userId).toBe('user_123');
      expect(result.result).toBe('user_identified');
    });

    test('应该生成报告', () => {
      const inputs = {
        provider: 'google',
        action: 'report',
        trackingId: 'GA-123456',
        reportType: 'realtime'
      };

      const result = node.execute(inputs);

      expect(result.success).toBe(true);
      expect(result.reportData).toBeDefined();
      expect(result.reportData.data.activeUsers).toBeDefined();
      expect(result.result).toBe('report_generated');
    });

    test('应该拒绝缺少跟踪ID的请求', () => {
      const inputs = {
        action: 'track',
        eventName: 'test_event'
        // 缺少 trackingId 和 apiKey
      };

      const result = node.execute(inputs);

      expect(result.success).toBe(false);
      expect(result.errorMessage).toBe('跟踪ID或API密钥缺失');
    });
  });
});
