/**
 * 编辑器集成测试脚本
 * 测试批次0.1节点在编辑器中的集成状态
 */

const fs = require('fs');
const path = require('path');

class EditorIntegrationTester {
  constructor() {
    this.results = {
      categoryManager: { valid: false, errors: [] },
      translations: { valid: false, errors: [] },
      nodePanel: { valid: false, errors: [] },
      integration: { complete: false, errors: [] }
    };
  }

  /**
   * 测试节点分类管理器
   */
  testCategoryManager() {
    console.log('🔍 测试节点分类管理器...');
    
    try {
      const categoryManagerPath = path.resolve('editor/src/components/visual-script/NodeCategoryManager.ts');
      if (!fs.existsSync(categoryManagerPath)) {
        this.results.categoryManager.errors.push('NodeCategoryManager.ts 文件不存在');
        return false;
      }
      
      const content = fs.readFileSync(categoryManagerPath, 'utf8');
      
      // 检查场景管理分类
      const sceneCategories = [
        'scene',
        'Scene/Management',
        'Scene/Editing', 
        'Scene/Generation',
        'Scene/Transition'
      ];
      
      sceneCategories.forEach(category => {
        if (!content.includes(`id: '${category}'`)) {
          this.results.categoryManager.errors.push(`缺少场景分类: ${category}`);
        }
      });
      
      // 检查资源管理分类
      const resourceCategories = [
        'resource',
        'Resource/Loading',
        'Resource/Optimization',
        'Resource/Management'
      ];
      
      resourceCategories.forEach(category => {
        if (!content.includes(`id: '${category}'`)) {
          this.results.categoryManager.errors.push(`缺少资源分类: ${category}`);
        }
      });
      
      this.results.categoryManager.valid = this.results.categoryManager.errors.length === 0;
      console.log(`✅ 节点分类管理器测试完成 - ${this.results.categoryManager.valid ? '通过' : '失败'}`);
      
      return this.results.categoryManager.valid;
      
    } catch (error) {
      this.results.categoryManager.errors.push(`测试失败: ${error.message}`);
      return false;
    }
  }

  /**
   * 测试翻译文件
   */
  testTranslations() {
    console.log('🔍 测试翻译文件...');
    
    try {
      const translationPath = path.resolve('editor/src/locales/zh/scripting.json');
      if (!fs.existsSync(translationPath)) {
        this.results.translations.errors.push('scripting.json 翻译文件不存在');
        return false;
      }
      
      const content = fs.readFileSync(translationPath, 'utf8');
      const translations = JSON.parse(content);
      
      // 检查批次0.1节点翻译
      if (!translations.batch01Nodes) {
        this.results.translations.errors.push('缺少 batch01Nodes 翻译配置');
      } else {
        const categories = translations.batch01Nodes.categories;
        if (!categories) {
          this.results.translations.errors.push('缺少 batch01Nodes.categories 翻译配置');
        } else {
          // 检查场景管理翻译
          const sceneTranslations = ['scene', 'Scene/Management', 'Scene/Editing', 'Scene/Generation', 'Scene/Transition'];
          sceneTranslations.forEach(key => {
            if (!categories[key]) {
              this.results.translations.errors.push(`缺少场景分类翻译: ${key}`);
            }
          });
          
          // 检查资源管理翻译
          const resourceTranslations = ['resource', 'Resource/Loading', 'Resource/Optimization', 'Resource/Management'];
          resourceTranslations.forEach(key => {
            if (!categories[key]) {
              this.results.translations.errors.push(`缺少资源分类翻译: ${key}`);
            }
          });
        }
      }
      
      this.results.translations.valid = this.results.translations.errors.length === 0;
      console.log(`✅ 翻译文件测试完成 - ${this.results.translations.valid ? '通过' : '失败'}`);
      
      return this.results.translations.valid;
      
    } catch (error) {
      this.results.translations.errors.push(`测试失败: ${error.message}`);
      return false;
    }
  }

  /**
   * 测试节点面板集成
   */
  testNodePanel() {
    console.log('🔍 测试节点面板集成...');
    
    try {
      // 检查是否存在节点面板相关文件
      const nodePanelFiles = [
        'editor/src/components/visual-script/nodes/Batch01NodesIntegration.ts'
      ];
      
      let integrationFound = false;
      
      nodePanelFiles.forEach(filePath => {
        if (fs.existsSync(path.resolve(filePath))) {
          integrationFound = true;
          const content = fs.readFileSync(path.resolve(filePath), 'utf8');
          
          // 检查是否包含场景和资源管理节点的引用
          if (!content.includes('场景管理') && !content.includes('SceneManagement')) {
            this.results.nodePanel.errors.push(`${filePath} 中缺少场景管理节点集成`);
          }
          
          if (!content.includes('资源管理') && !content.includes('ResourceManagement')) {
            this.results.nodePanel.errors.push(`${filePath} 中缺少资源管理节点集成`);
          }
        }
      });
      
      if (!integrationFound) {
        this.results.nodePanel.errors.push('未找到节点面板集成文件');
      }
      
      this.results.nodePanel.valid = this.results.nodePanel.errors.length === 0;
      console.log(`✅ 节点面板集成测试完成 - ${this.results.nodePanel.valid ? '通过' : '失败'}`);
      
      return this.results.nodePanel.valid;
      
    } catch (error) {
      this.results.nodePanel.errors.push(`测试失败: ${error.message}`);
      return false;
    }
  }

  /**
   * 测试整体集成状态
   */
  testOverallIntegration() {
    console.log('🔍 测试整体集成状态...');
    
    try {
      // 检查引擎注册表
      const registryPath = path.resolve('engine/src/visual-script/registry/NodeRegistry.ts');
      if (!fs.existsSync(registryPath)) {
        this.results.integration.errors.push('NodeRegistry.ts 文件不存在');
        return false;
      }
      
      const registryContent = fs.readFileSync(registryPath, 'utf8');
      
      // 检查批次0.1注册方法调用
      if (!registryContent.includes('this.registerBatch01Nodes()')) {
        this.results.integration.errors.push('NodeRegistry 中未调用 registerBatch01Nodes 方法');
      }
      
      // 检查场景和资源管理节点注册方法
      if (!registryContent.includes('registerSceneManagementNodes')) {
        this.results.integration.errors.push('NodeRegistry 中缺少 registerSceneManagementNodes 方法');
      }
      
      if (!registryContent.includes('registerResourceManagementNodes')) {
        this.results.integration.errors.push('NodeRegistry 中缺少 registerResourceManagementNodes 方法');
      }
      
      // 检查编辑器分类管理器
      const categoryManagerPath = path.resolve('editor/src/components/visual-script/NodeCategoryManager.ts');
      if (fs.existsSync(categoryManagerPath)) {
        const categoryContent = fs.readFileSync(categoryManagerPath, 'utf8');
        
        if (!categoryContent.includes("id: 'scene'") || !categoryContent.includes("id: 'resource'")) {
          this.results.integration.errors.push('NodeCategoryManager 中缺少场景或资源管理分类');
        }
      }
      
      // 检查翻译文件
      const translationPath = path.resolve('editor/src/locales/zh/scripting.json');
      if (fs.existsSync(translationPath)) {
        const translationContent = fs.readFileSync(translationPath, 'utf8');
        
        if (!translationContent.includes('batch01Nodes')) {
          this.results.integration.errors.push('翻译文件中缺少 batch01Nodes 配置');
        }
      }
      
      this.results.integration.complete = this.results.integration.errors.length === 0;
      console.log(`✅ 整体集成测试完成 - ${this.results.integration.complete ? '通过' : '失败'}`);
      
      return this.results.integration.complete;
      
    } catch (error) {
      this.results.integration.errors.push(`测试失败: ${error.message}`);
      return false;
    }
  }

  /**
   * 生成测试报告
   */
  generateReport() {
    console.log('\n📋 编辑器集成测试报告');
    console.log('='.repeat(50));
    
    // 节点分类管理器报告
    console.log(`\n🏗️ 节点分类管理器: ${this.results.categoryManager.valid ? '✅ 通过' : '❌ 失败'}`);
    if (this.results.categoryManager.errors.length > 0) {
      console.log('❌ 错误:');
      this.results.categoryManager.errors.forEach(error => console.log(`   - ${error}`));
    }
    
    // 翻译文件报告
    console.log(`\n🌐 翻译文件: ${this.results.translations.valid ? '✅ 通过' : '❌ 失败'}`);
    if (this.results.translations.errors.length > 0) {
      console.log('❌ 错误:');
      this.results.translations.errors.forEach(error => console.log(`   - ${error}`));
    }
    
    // 节点面板报告
    console.log(`\n🎛️ 节点面板: ${this.results.nodePanel.valid ? '✅ 通过' : '❌ 失败'}`);
    if (this.results.nodePanel.errors.length > 0) {
      console.log('❌ 错误:');
      this.results.nodePanel.errors.forEach(error => console.log(`   - ${error}`));
    }
    
    // 整体集成报告
    console.log(`\n🔗 整体集成: ${this.results.integration.complete ? '✅ 完成' : '❌ 未完成'}`);
    if (this.results.integration.errors.length > 0) {
      console.log('❌ 错误:');
      this.results.integration.errors.forEach(error => console.log(`   - ${error}`));
    }
    
    // 总结
    const allPassed = this.results.categoryManager.valid && 
                     this.results.translations.valid && 
                     this.results.nodePanel.valid && 
                     this.results.integration.complete;
    
    console.log(`\n📈 测试结果: ${allPassed ? '🎉 全部通过' : '⚠️ 需要修复'}`);
    
    return allPassed;
  }

  /**
   * 运行所有测试
   */
  async run() {
    console.log('🚀 开始编辑器集成测试...\n');
    
    const categoryManagerPassed = this.testCategoryManager();
    const translationsPassed = this.testTranslations();
    const nodePanelPassed = this.testNodePanel();
    const integrationPassed = this.testOverallIntegration();
    
    const success = this.generateReport();
    
    console.log('\n' + '='.repeat(50));
    console.log(success ? '✅ 测试完成' : '❌ 测试失败');
    
    return success;
  }
}

// 运行测试
if (require.main === module) {
  const tester = new EditorIntegrationTester();
  tester.run().then(success => {
    process.exit(success ? 0 : 1);
  }).catch(error => {
    console.error('测试过程中发生错误:', error);
    process.exit(1);
  });
}

module.exports = EditorIntegrationTester;
