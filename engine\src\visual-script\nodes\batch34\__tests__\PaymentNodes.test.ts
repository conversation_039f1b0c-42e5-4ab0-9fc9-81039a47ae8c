/**
 * 支付系统节点单元测试
 */

import {
  PaymentGatewayNode,
  SubscriptionNode,
  InAppPurchaseNode,
  WalletSystemNode,
  TransactionHistoryNode,
  PaymentAnalyticsNode
} from '../PaymentNodes';

describe('支付系统节点测试', () => {
  describe('PaymentGatewayNode', () => {
    let node: PaymentGatewayNode;

    beforeEach(() => {
      node = new PaymentGatewayNode();
    });

    test('应该正确初始化', () => {
      expect(node.name).toBe('支付网关');
      expect(node.description).toBe('处理各种支付方式的统一接口');
      expect(node.category).toBe('支付系统');
    });

    test('应该处理有效的支付请求', () => {
      const inputs = {
        amount: 100,
        currency: 'USD',
        paymentMethod: 'credit_card',
        merchantId: 'merchant_123',
        orderId: 'order_456',
        customerInfo: { name: '<PERSON>', email: '<EMAIL>' }
      };

      const result = node.execute(inputs);

      expect(result.success).toBe(true);
      expect(result.transactionId).toBeDefined();
      expect(result.status).toBe('completed');
      expect(result.fees).toBeGreaterThan(0);
      expect(result.netAmount).toBeLessThan(inputs.amount);
    });

    test('应该拒绝无效的支付金额', () => {
      const inputs = {
        amount: 0,
        merchantId: 'merchant_123',
        orderId: 'order_456'
      };

      const result = node.execute(inputs);

      expect(result.success).toBe(false);
      expect(result.errorMessage).toBe('支付金额无效');
      expect(result.transactionId).toBeNull();
    });

    test('应该拒绝缺少必需参数的请求', () => {
      const inputs = {
        amount: 100
        // 缺少 merchantId 和 orderId
      };

      const result = node.execute(inputs);

      expect(result.success).toBe(false);
      expect(result.errorMessage).toBe('商户ID或订单ID缺失');
    });
  });

  describe('SubscriptionNode', () => {
    let node: SubscriptionNode;

    beforeEach(() => {
      node = new SubscriptionNode();
    });

    test('应该正确初始化', () => {
      expect(node.name).toBe('订阅系统');
      expect(node.description).toBe('处理订阅服务的创建、管理和计费');
      expect(node.category).toBe('支付系统');
    });

    test('应该创建新订阅', () => {
      const inputs = {
        action: 'create',
        customerId: 'customer_123',
        planId: 'premium',
        billingCycle: 'monthly'
      };

      const result = node.execute(inputs);

      expect(result.success).toBe(true);
      expect(result.subscriptionId).toBeDefined();
      expect(result.status).toBe('active');
      expect(result.amount).toBe(19.99);
    });

    test('应该取消订阅', () => {
      const inputs = {
        action: 'cancel',
        subscriptionId: 'sub_123'
      };

      const result = node.execute(inputs);

      expect(result.success).toBe(true);
      expect(result.status).toBe('canceled');
    });

    test('应该拒绝无效的操作', () => {
      const inputs = {
        action: 'invalid_action'
      };

      const result = node.execute(inputs);

      expect(result.success).toBe(false);
      expect(result.errorMessage).toBe('不支持的操作类型');
    });
  });

  describe('InAppPurchaseNode', () => {
    let node: InAppPurchaseNode;

    beforeEach(() => {
      node = new InAppPurchaseNode();
    });

    test('应该正确初始化', () => {
      expect(node.name).toBe('应用内购买');
      expect(node.description).toBe('处理移动应用内购买和虚拟商品交易');
      expect(node.category).toBe('支付系统');
    });

    test('应该处理有效的购买请求', () => {
      const inputs = {
        productId: 'coins_100',
        userId: 'user_123',
        platform: 'ios',
        receipt: 'receipt_data'
      };

      const result = node.execute(inputs);

      expect(result.success).toBe(true);
      expect(result.transactionId).toBeDefined();
      expect(result.productInfo).toBeDefined();
      expect(result.virtualCurrency).toBe(100);
    });

    test('应该拒绝不存在的商品', () => {
      const inputs = {
        productId: 'invalid_product',
        userId: 'user_123'
      };

      const result = node.execute(inputs);

      expect(result.success).toBe(false);
      expect(result.errorMessage).toBe('商品不存在');
    });
  });

  describe('WalletSystemNode', () => {
    let node: WalletSystemNode;

    beforeEach(() => {
      node = new WalletSystemNode();
    });

    test('应该正确初始化', () => {
      expect(node.name).toBe('钱包系统');
      expect(node.description).toBe('处理用户钱包余额、充值、提现等操作');
      expect(node.category).toBe('支付系统');
    });

    test('应该获取钱包余额', () => {
      const inputs = {
        action: 'getBalance',
        userId: 'user_123'
      };

      const result = node.execute(inputs);

      expect(result.success).toBe(true);
      expect(result.balance).toBeDefined();
      expect(result.walletInfo).toBeDefined();
    });

    test('应该处理充值操作', () => {
      const inputs = {
        action: 'deposit',
        userId: 'user_123',
        amount: 50
      };

      const result = node.execute(inputs);

      expect(result.success).toBe(true);
      expect(result.transactionId).toBeDefined();
      expect(result.balance).toBeGreaterThan(150); // 原余额150.75 + 50
    });

    test('应该拒绝余额不足的提现', () => {
      const inputs = {
        action: 'withdraw',
        userId: 'user_123',
        amount: 1000 // 超过余额
      };

      const result = node.execute(inputs);

      expect(result.success).toBe(false);
      expect(result.errorMessage).toBe('余额不足');
    });
  });

  describe('TransactionHistoryNode', () => {
    let node: TransactionHistoryNode;

    beforeEach(() => {
      node = new TransactionHistoryNode();
    });

    test('应该正确初始化', () => {
      expect(node.name).toBe('交易历史');
      expect(node.description).toBe('查询和管理用户的交易历史记录');
      expect(node.category).toBe('支付系统');
    });

    test('应该返回交易历史', () => {
      const inputs = {
        userId: 'user_123',
        limit: 10
      };

      const result = node.execute(inputs);

      expect(result.success).toBe(true);
      expect(result.transactions).toBeDefined();
      expect(Array.isArray(result.transactions)).toBe(true);
      expect(result.totalCount).toBeDefined();
      expect(result.summary).toBeDefined();
    });

    test('应该过滤交易类型', () => {
      const inputs = {
        userId: 'user_123',
        transactionType: 'payment'
      };

      const result = node.execute(inputs);

      expect(result.success).toBe(true);
      expect(result.transactions.length).toBeGreaterThan(0);
    });
  });

  describe('PaymentAnalyticsNode', () => {
    let node: PaymentAnalyticsNode;

    beforeEach(() => {
      node = new PaymentAnalyticsNode();
    });

    test('应该正确初始化', () => {
      expect(node.name).toBe('支付分析');
      expect(node.description).toBe('提供支付数据的统计分析和报表功能');
      expect(node.category).toBe('支付系统');
    });

    test('应该生成收入分析', () => {
      const inputs = {
        analysisType: 'revenue',
        timeRange: '30d'
      };

      const result = node.execute(inputs);

      expect(result.success).toBe(true);
      expect(result.analyticsData).toBeDefined();
      expect(result.chartData).toBeDefined();
      expect(result.summary).toBeDefined();
      expect(result.trends).toBeDefined();
    });

    test('应该生成交易分析', () => {
      const inputs = {
        analysisType: 'transactions',
        timeRange: '7d'
      };

      const result = node.execute(inputs);

      expect(result.success).toBe(true);
      expect(result.analyticsData.totalTransactions).toBeDefined();
      expect(result.chartData).toBeDefined();
    });

    test('应该拒绝不支持的分析类型', () => {
      const inputs = {
        analysisType: 'invalid_type'
      };

      const result = node.execute(inputs);

      expect(result.success).toBe(false);
      expect(result.errorMessage).toBe('不支持的分析类型');
    });
  });
});
